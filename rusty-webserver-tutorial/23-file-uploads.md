# File Uploads and Multipart Handling

## Learning Objectives
- Implement secure file upload capabilities in your Rust webserver
- Process multipart form data efficiently
- Apply proper file validation and security measures
- Integrate with cloud storage services
- Handle large file uploads with streaming techniques

## Prerequisites
- Understanding of HTTP form submission
- Knowledge of MIME types and content encoding
- Familiarity with file system operations in Rust

## Introduction

File uploads are a common requirement for modern web applications, allowing users to share documents, images, and other content. This module explores how to handle file uploads securely and efficiently in a Rust webserver.

## Understanding Multipart Form Data

Multipart form data is a format for encoding form data that includes binary files:

```
POST /upload HTTP/1.1
Host: example.com
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryX3bY94Uc9CyB8eDT
Content-Length: 4582

------WebKitFormBoundaryX3bY94Uc9CyB8eDT
Content-Disposition: form-data; name="username"

alice
------WebKitFormBoundaryX3bY94Uc9CyB8eDT
Content-Disposition: form-data; name="profile_image"; filename="photo.jpg"
Content-Type: image/jpeg

[Binary data for photo.jpg]
------WebKitFormBoundaryX3bY94Uc9CyB8eDT--
```

Each part has its own headers and content, separated by a boundary string.

## Basic File Upload Implementation

### 1. Using Actix Multipart

Actix-web provides built-in support for multipart handling:

```rust
use actix_multipart::Multipart;
use actix_web::{web, App, Error, HttpResponse, HttpServer};
use futures::{StreamExt, TryStreamExt};
use std::io::Write;
use uuid::Uuid;
use std::path::Path;

// Handler for file upload
async fn upload(mut payload: Multipart, config: web::Data<AppConfig>) -> Result<HttpResponse, Error> {
    // Create uploads directory if it doesn't exist
    let upload_dir = &config.upload_dir;
    std::fs::create_dir_all(upload_dir).expect("Failed to create upload directory");

    // Process all parts of the multipart form
    let mut uploaded_files = Vec::new();
    
    while let Some(mut field) = payload.try_next().await? {
        // Extract field information
        let content_disposition = field.content_disposition();
        
        let filename = content_disposition
            .get_filename()
            .map_or_else(
                || Uuid::new_v4().to_string(), 
                sanitize_filename
            );
            
        // Generate a unique filename to prevent overwriting
        let filepath = Path::new(upload_dir)
            .join(format!("{}-{}", Uuid::new_v4(), filename));
            
        // Create file
        let mut file = web::block(|| std::fs::File::create(&filepath))
            .await??;
            
        // Write file content
        while let Some(chunk) = field.try_next().await? {
            // Limit chunk size for security
            if chunk.len() > config.max_file_size {
                return Err(actix_web::error::ErrorBadRequest("File too large"));
            }
            
            // Write chunk to file
            web::block(move || file.write_all(&chunk))
                .await??;
        }
        
        uploaded_files.push(filepath.to_string_lossy().to_string());
    }
    
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "success",
        "files": uploaded_files
    })))
}

// Sanitize filenames to prevent path traversal
fn sanitize_filename(filename: &str) -> String {
    // Remove any path components and keep just the filename
    let filename = Path::new(filename)
        .file_name()
        .unwrap_or_default()
        .to_string_lossy();
    
    // Replace characters that might cause issues
    filename
        .replace("/", "_")
        .replace("\\", "_")
        .replace(":", "_")
}

// Configure routes
fn config_app(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::resource("/upload")
            .route(web::post().to(upload))
    );
}

// App configuration
struct AppConfig {
    upload_dir: String,
    max_file_size: usize,
}

// Main application
#[actix_web::main]
async fn main() -> std::io::Result<()> {
    let config = web::Data::new(AppConfig {
        upload_dir: "./uploads".to_string(),
        max_file_size: 2_000_000, // 2MB chunk size limit
    });
    
    HttpServer::new(move || {
        App::new()
            .app_data(config.clone())
            .configure(config_app)
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}
```

### 2. Complex Form Handling with Files and Fields

Process forms with both files and text fields:

```rust
use actix_multipart::Multipart;
use actix_web::{web, HttpResponse, Error};
use futures::{StreamExt, TryStreamExt};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize)]
struct UploadData {
    title: String,
    description: Option<String>,
    category: Option<String>,
    tags: Vec<String>,
    files: Vec<UploadedFile>,
}

#[derive(Debug, Serialize, Deserialize)]
struct UploadedFile {
    filename: String,
    path: String,
    size: usize,
    content_type: String,
}

async fn process_mixed_form(mut payload: Multipart, config: web::Data<AppConfig>) -> Result<HttpResponse, Error> {
    let mut form_data = HashMap::new();
    let mut files = Vec::new();
    
    // Process form fields
    while let Some(mut field) = payload.try_next().await? {
        let content_disposition = field.content_disposition();
        let name = content_disposition
            .get_name()
            .ok_or_else(|| actix_web::error::ErrorBadRequest("No field name"))?
            .to_owned();
            
        // Check if this is a file field
        if let Some(filename) = content_disposition.get_filename() {
            // Process file
            let filename = sanitize_filename(filename);
            let content_type = field
                .content_type()
                .map_or("application/octet-stream".to_owned(), |ct| ct.to_string());
            
            // Generate unique file path
            let file_path = Path::new(&config.upload_dir)
                .join(format!("{}-{}", Uuid::new_v4(), &filename));
            
            // Create file and write content
            let mut file = web::block(|| std::fs::File::create(&file_path))
                .await??;
                
            let mut size = 0;
            while let Some(chunk) = field.try_next().await? {
                size += chunk.len();
                web::block(move || file.write_all(&chunk))
                    .await??;
            }
            
            // Add file info to list
            files.push(UploadedFile {
                filename: filename.to_owned(),
                path: file_path.to_string_lossy().to_string(),
                size,
                content_type,
            });
        } else {
            // Process regular form field
            let mut value = String::new();
            while let Some(chunk) = field.try_next().await? {
                value.push_str(std::str::from_utf8(&chunk).map_err(|_| 
                    actix_web::error::ErrorBadRequest("Invalid UTF-8 in form field"))?);
            }
            form_data.insert(name, value);
        }
    }
    
    // Construct response data
    let upload_data = UploadData {
        title: form_data.get("title")
            .cloned()
            .unwrap_or_else(|| "Untitled".to_owned()),
        description: form_data.get("description").cloned(),
        category: form_data.get("category").cloned(),
        tags: form_data.get("tags")
            .map(|t| t.split(',').map(|s| s.trim().to_owned()).collect())
            .unwrap_or_else(Vec::new),
        files,
    };
    
    // Save metadata to database or process further
    
    Ok(HttpResponse::Ok().json(upload_data))
}
```

## Advanced File Upload Techniques

### 1. Streaming Large File Uploads

For large files, stream directly to storage without loading the entire file into memory:

```rust
use actix_multipart::Multipart;
use actix_web::{web, Error, HttpResponse};
use futures::{StreamExt, TryStreamExt};
use tokio::fs::File;
use tokio::io::AsyncWriteExt;

async fn stream_large_file(mut payload: Multipart) -> Result<HttpResponse, Error> {
    while let Some(mut field) = payload.try_next().await? {
        // Extract filename from field
        let filename = field
            .content_disposition()
            .get_filename()
            .map_or_else(|| "unnamed.dat".to_string(), sanitize_filename);
            
        // Create file with tokio for async I/O
        let filepath = format!("./large_uploads/{}", filename);
        let mut file = File::create(&filepath).await.map_err(|e| {
            actix_web::error::ErrorInternalServerError(e)
        })?;
        
        // Stream file data with backpressure handling
        while let Some(chunk) = field.try_next().await? {
            // Write chunk and flush to ensure data is written to disk
            file.write_all(&chunk).await.map_err(|e| {
                actix_web::error::ErrorInternalServerError(e)
            })?;
            file.flush().await.map_err(|e| {
                actix_web::error::ErrorInternalServerError(e)
            })?;
        }
    }
    
    Ok(HttpResponse::Ok().body("File uploaded successfully"))
}
```

### 2. Resumable File Uploads

Implement the `tus` protocol for resumable uploads:

```rust
use actix_web::{web, HttpRequest, HttpResponse, Error};
use futures::StreamExt;
use std::path::Path;
use std::fs::{File, OpenOptions};
use std::io::SeekFrom;
use tokio::io::{AsyncSeekExt, AsyncWriteExt};

async fn handle_resumable_upload(
    req: HttpRequest,
    mut payload: web::Payload,
    config: web::Data<AppConfig>,
) -> Result<HttpResponse, Error> {
    // Extract upload metadata
    let upload_id = req.headers()
        .get("Upload-ID")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| actix_web::error::ErrorBadRequest("Missing Upload-ID header"))?;
        
    let content_length = req.headers()
        .get("Content-Length")
        .and_then(|h| h.to_str().ok())
        .and_then(|s| s.parse::<usize>().ok())
        .ok_or_else(|| actix_web::error::ErrorBadRequest("Invalid Content-Length"))?;
        
    // Get offset from header or assume 0
    let offset = req.headers()
        .get("Upload-Offset")
        .and_then(|h| h.to_str().ok())
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(0);
        
    // Validate upload ID (should be UUID or similar)
    if !is_valid_upload_id(upload_id) {
        return Err(actix_web::error::ErrorBadRequest("Invalid Upload-ID"));
    }
    
    // Determine file path
    let file_path = Path::new(&config.upload_dir).join(upload_id);
    
    // Open file for appending
    let mut file = tokio::fs::OpenOptions::new()
        .create(true)
        .write(true)
        .append(false)
        .open(&file_path)
        .await
        .map_err(actix_web::error::ErrorInternalServerError)?;
        
    // Seek to offset
    file.seek(SeekFrom::Start(offset))
        .await
        .map_err(actix_web::error::ErrorInternalServerError)?;
    
    // Write data chunks
    let mut bytes_written = 0;
    while let Some(chunk) = payload.next().await {
        let data = chunk.map_err(actix_web::error::ErrorInternalServerError)?;
        file.write_all(&data)
            .await
            .map_err(actix_web::error::ErrorInternalServerError)?;
        bytes_written += data.len();
    }
    
    // Calculate new offset
    let new_offset = offset + bytes_written as u64;
    
    // Check if upload is complete
    let is_complete = req.headers()
        .get("Upload-Complete")
        .and_then(|h| h.to_str().ok())
        .map_or(false, |s| s == "1" || s.to_lowercase() == "true");
        
    if is_complete {
        // Move file to final location or process it
        let final_filename = req.headers()
            .get("Upload-Name")
            .and_then(|h| h.to_str().ok())
            .map_or_else(|| "unnamed.dat".to_string(), sanitize_filename);
            
        let final_path = Path::new(&config.finished_dir).join(&final_filename);
        tokio::fs::rename(&file_path, &final_path)
            .await
            .map_err(actix_web::error::ErrorInternalServerError)?;
            
        Ok(HttpResponse::Created()
            .append_header(("Location", format!("/files/{}", final_filename)))
            .finish())
    } else {
        // Return successful chunk upload response
        Ok(HttpResponse::Ok()
            .append_header(("Upload-Offset", new_offset.to_string()))
            .append_header(("Upload-ID", upload_id.to_string()))
            .finish())
    }
}

fn is_valid_upload_id(id: &str) -> bool {
    // Validate upload ID format (example: UUID validation)
    uuid::Uuid::parse_str(id).is_ok()
}
```

### 3. Cloud Storage Integration

Upload directly to cloud storage like AWS S3:

```rust
use actix_multipart::Multipart;
use actix_web::{web, Error, HttpResponse};
use futures::{StreamExt, TryStreamExt};
use rusoto_core::{Region, ByteStream};
use rusoto_s3::{S3, S3Client, PutObjectRequest};
use uuid::Uuid;

// Setup S3 client
fn create_s3_client() -> S3Client {
    S3Client::new(Region::UsEast1) // Use your region
}

// Upload handler
async fn s3_upload(mut payload: Multipart, config: web::Data<S3Config>) -> Result<HttpResponse, Error> {
    let s3_client = create_s3_client();
    let mut uploaded_files = Vec::new();
    
    while let Some(mut field) = payload.try_next().await? {
        // Get filename, generate unique key
        let filename = field.content_disposition()
            .get_filename()
            .map_or_else(
                || Uuid::new_v4().to_string(),
                sanitize_filename
            );
            
        let s3_key = format!("{}/{}", config.folder_prefix, filename);
        
        // Collect file data
        let mut file_data = Vec::new();
        while let Some(chunk) = field.try_next().await? {
            file_data.extend_from_slice(&chunk);
        }
        
        // Get content type
        let content_type = field.content_type().map(|ct| ct.to_string())
            .unwrap_or_else(|| "application/octet-stream".to_string());
            
        // Create upload request
        let put_request = PutObjectRequest {
            bucket: config.bucket.clone(),
            key: s3_key.clone(),
            body: Some(ByteStream::from(file_data)),
            content_type: Some(content_type),
            acl: Some("private".to_string()),
            ..Default::default()
        };
        
        // Upload to S3
        let result = s3_client.put_object(put_request).await
            .map_err(|e| {
                log::error!("S3 upload error: {}", e);
                actix_web::error::ErrorInternalServerError("Failed to upload to S3")
            })?;
            
        // Get file URL (or use presigned URL)
        let file_url = format!(
            "https://{}.s3.amazonaws.com/{}",
            config.bucket, s3_key
        );
            
        uploaded_files.push(serde_json::json!({
            "filename": filename,
            "url": file_url,
            "etag": result.e_tag
        }));
    }
    
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "success",
        "files": uploaded_files
    })))
}

// S3 configuration
struct S3Config {
    bucket: String,
    folder_prefix: String,
}
```

## Security Measures for File Uploads

### 1. File Type Validation

Verify uploaded files match their claimed type:

```rust
use actix_web::{web, Error, HttpResponse};
use infer::Infer;
use std::collections::HashSet;

// Allowed file types
fn get_allowed_mime_types() -> HashSet<&'static str> {
    let mut allowed = HashSet::new();
    // Images
    allowed.insert("image/jpeg");
    allowed.insert("image/png");
    allowed.insert("image/gif");
    // Documents
    allowed.insert("application/pdf");
    allowed.insert("application/msword");
    allowed.insert("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    
    allowed
}

// Validate file type using content inspection
fn validate_file_type(data: &[u8], claimed_mime: &str) -> Result<(), String> {
    // Use infer to detect file type
    let kind = infer::get(data)
        .ok_or_else(|| "Unknown file type".to_string())?;
    
    // Check if detected type matches claimed type
    if kind.mime_type() != claimed_mime {
        return Err(format!(
            "File type mismatch: claimed {}, detected {}",
            claimed_mime, kind.mime_type()
        ));
    }
    
    // Check if type is allowed
    if !get_allowed_mime_types().contains(kind.mime_type()) {
        return Err(format!(
            "File type not allowed: {}",
            kind.mime_type()
        ));
    }
    
    Ok(())
}

// Implementation in upload handler
async fn secure_file_upload(mut payload: Multipart) -> Result<HttpResponse, Error> {
    while let Some(mut field) = payload.try_next().await? {
        // Get content type
        let content_type = field
            .content_type()
            .map(|ct| ct.to_string())
            .unwrap_or_else(|| "application/octet-stream".to_string());
            
        // Buffer for file header (first chunk)
        let mut file_header = None;
        
        // Process file data
        let mut file_data = Vec::new();
        while let Some(chunk) = field.try_next().await? {
            // Save the first chunk for content type verification
            if file_header.is_none() && !chunk.is_empty() {
                file_header = Some(chunk.clone());
            }
            
            file_data.extend_from_slice(&chunk);
        }
        
        // Validate file type
        if let Some(header) = file_header {
            match validate_file_type(&header, &content_type) {
                Ok(_) => {
                    // Type is valid, continue processing
                },
                Err(msg) => {
                    // Type validation failed
                    return Err(actix_web::error::ErrorBadRequest(msg));
                }
            }
        }
        
        // Continue with safe file processing...
    }
    
    Ok(HttpResponse::Ok().finish())
}
```

### 2. Anti-Virus Integration

Scan uploaded files for malware:

```rust
use std::process::Command;
use actix_web::{web, Error, HttpResponse};
use futures::{StreamExt, TryStreamExt};
use tempfile::NamedTempFile;
use std::io::Write;

// Scan file for viruses using ClamAV
async fn scan_file(file_path: &str) -> Result<bool, String> {
    // Call ClamScan to check the file
    let output = Command::new("clamdscan")
        .args(&["--no-summary", file_path])
        .output()
        .map_err(|e| format!("Failed to run virus scan: {}", e))?;
        
    // Check scan status (ClamAV returns 1 if virus found)
    let exit_code = output.status.code().unwrap_or(1);
    let is_clean = exit_code == 0;
    
    if !is_clean {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);
        log::warn!(
            "Virus detected: file={}, stdout={}, stderr={}", 
            file_path, stdout, stderr
        );
    }
    
    Ok(is_clean)
}

// Upload handler with virus scanning
async fn secure_upload(mut payload: Multipart) -> Result<HttpResponse, Error> {
    while let Some(mut field) = payload.try_next().await? {
        // Get filename
        let filename = field.content_disposition()
            .get_filename()
            .map_or_else(|| "unnamed.dat".to_string(), sanitize_filename);
            
        // Create temporary file
        let mut temp_file = web::block(|| {
            NamedTempFile::new().map_err(|e| std::io::Error::new(
                std::io::ErrorKind::Other, 
                format!("Failed to create temp file: {}", e)
            ))
        })
        .await??;
        
        // Write file data to temp file
        while let Some(chunk) = field.try_next().await? {
            web::block(move || {
                temp_file.write_all(&chunk).map_err(|e| std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Failed to write to temp file: {}", e)
                ))
            })
            .await??;
        }
        
        // Get temp file path
        let temp_path = temp_file.path().to_string_lossy().to_string();
        
        // Scan file for viruses
        let scan_result = web::block(move || {
            // Run in a blocking context since virus scanning is CPU-intensive
            tokio::task::block_in_place(|| {
                scan_file(&temp_path)
            })
        })
        .await?;
        
        // Check scan result
        match scan_result {
            Ok(true) => {
                // File is clean, move to permanent storage
                let dest_path = format!("./uploads/{}", filename);
                std::fs::copy(temp_path, &dest_path)?;
            },
            Ok(false) => {
                // Virus detected
                return Err(actix_web::error::ErrorBadRequest(
                    "File rejected: potential security threat detected"
                ));
            },
            Err(e) => {
                // Scan error
                log::error!("Virus scan error: {}", e);
                return Err(actix_web::error::ErrorInternalServerError(
                    "Could not verify file security"
                ));
            }
        }
    }
    
    Ok(HttpResponse::Ok().body("File uploaded and verified"))
}
```

### 3. File Size Limits and Quotas

Enforce strict file size limits and user quotas:

```rust
use actix_web::{web, Error, HttpRequest, HttpResponse};
use actix_multipart::Multipart;
use futures::{StreamExt, TryStreamExt};
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;

// User quota tracking
struct QuotaManager {
    // Map of user ID to used storage in bytes
    user_quotas: RwLock<HashMap<String, usize>>,
    max_quota_bytes: usize,
    max_file_bytes: usize,
}

impl QuotaManager {
    fn new(max_quota_mb: usize, max_file_mb: usize) -> Self {
        Self {
            user_quotas: RwLock::new(HashMap::new()),
            max_quota_bytes: max_quota_mb * 1024 * 1024,
            max_file_bytes: max_file_mb * 1024 * 1024,
        }
    }
    
    // Check if user has enough quota for new file
    async fn can_upload(&self, user_id: &str, file_size: usize) -> Result<(), String> {
        // Check file size limit
        if file_size > self.max_file_bytes {
            return Err(format!(
                "File exceeds size limit of {} MB",
                self.max_file_bytes / (1024 * 1024)
            ));
        }
        
        // Check user quota
        let quota_map = self.user_quotas.read().await;
        let used_bytes = quota_map.get(user_id).unwrap_or(&0);
        
        if used_bytes + file_size > self.max_quota_bytes {
            return Err(format!(
                "Upload exceeds your storage quota of {} MB",
                self.max_quota_bytes / (1024 * 1024)
            ));
        }
        
        Ok(())
    }
    
    // Update user quota after upload
    async fn update_quota(&self, user_id: &str, additional_bytes: usize) {
        let mut quota_map = self.user_quotas.write().await;
        let entry = quota_map.entry(user_id.to_string()).or_insert(0);
        *entry += additional_bytes;
    }
}

// Upload handler with quota enforcement
async fn upload_with_quota(
    req: HttpRequest,
    mut payload: Multipart,
    quota_manager: web::Data<QuotaManager>
) -> Result<HttpResponse, Error> {
    // Get user ID (from authentication middleware or session)
    let user_id = get_user_id_from_request(&req)?;
    
    // Process uploaded files
    let mut total_size = 0;
    let mut files = Vec::new();
    
    while let Some(mut field) = payload.try_next().await? {
        // Extract metadata
        let filename = field.content_disposition()
            .get_filename()
            .map_or_else(|| "unnamed.dat".to_string(), sanitize_filename);
            
        // Pre-validate file size from content-length if available
        if let Some(length) = field.content_type().and_then(|ct| {
            ct.to_string().parse::<usize>().ok()
        }) {
            // Check quota before processing
            quota_manager.can_upload(&user_id, length)
                .await
                .map_err(|e| actix_web::error::ErrorBadRequest(e))?;
        }
        
        // Process file chunks with size tracking
        let file_path = format!("./uploads/{}/{}", user_id, filename);
        let mut file = std::fs::File::create(&file_path)?;
        let mut file_size = 0;
        
        while let Some(chunk) = field.try_next().await? {
            file_size += chunk.len();
            
            // Check for size limit during streaming
            if file_size > quota_manager.max_file_bytes {
                // Delete partial file
                std::fs::remove_file(&file_path)?;
                
                return Err(actix_web::error::ErrorBadRequest(format!(
                    "File exceeds size limit of {} MB",
                    quota_manager.max_file_bytes / (1024 * 1024)
                )));
            }
            
            // Write chunk
            file.write_all(&chunk)?;
        }
        
        // Update user quota
        quota_manager.update_quota(&user_id, file_size).await;
        total_size += file_size;
        
        files.push(serde_json::json!({
            "filename": filename,
            "size": file_size,
            "path": file_path
        }));
    }
    
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "success",
        "files": files,
        "total_size": total_size
    })))
}
```

## Image Processing for Uploads

Automatically process image uploads to create thumbnails:

```rust
use actix_multipart::Multipart;
use actix_web::{web, Error, HttpResponse};
use futures::{StreamExt, TryStreamExt};
use image::{ImageFormat, DynamicImage};
use std::io::Cursor;
use std::path::Path;
use uuid::Uuid;

// Process uploaded images
async fn process_image_upload(mut payload: Multipart) -> Result<HttpResponse, Error> {
    let mut responses = Vec::new();
    
    while let Some(mut field) = payload.try_next().await? {
        // Get filename
        let filename = field.content_disposition()
            .get_filename()
            .map_or_else(|| Uuid::new_v4().to_string(), sanitize_filename);
            
        // Check if it's an image by content type
        let content_type = field.content_type().map(|ct| ct.to_string())
            .unwrap_or_else(|| "application/octet-stream".to_string());
            
        if !content_type.starts_with("image/") {
            continue; // Skip non-image files
        }
        
        // Collect image data
        let mut image_data = Vec::new();
        while let Some(chunk) = field.try_next().await? {
            image_data.extend_from_slice(&chunk);
        }
        
        // Process image
        let result = web::block(move || -> Result<Vec<String>, String> {
            // Load image
            let img = image::load_from_memory(&image_data)
                .map_err(|e| format!("Failed to load image: {}", e))?;
                
            // Generate paths
            let filename_stem = Path::new(&filename)
                .file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("image");
                
            let extension = Path::new(&filename)
                .extension()
                .and_then(|s| s.to_str())
                .unwrap_or("jpg");
                
            let base_path = format!("./uploads/{}-{}", Uuid::new_v4(), filename_stem);
            
            // Save original
            let original_path = format!("{}.{}", base_path, extension);
            img.save(&original_path)
                .map_err(|e| format!("Failed to save original: {}", e))?;
                
            // Create thumbnails
            let thumbnail_sizes = [(150, 150), (300, 300), (600, 600)];
            let mut saved_paths = vec![original_path];
            
            for (width, height) in &thumbnail_sizes {
                let thumbnail = img.resize(*width, *height, image::imageops::FilterType::Lanczos3);
                let thumb_path = format!("{}-{}x{}.{}", base_path, width, height, extension);
                
                thumbnail.save(&thumb_path)
                    .map_err(|e| format!("Failed to save thumbnail: {}", e))?;
                    
                saved_paths.push(thumb_path);
            }
            
            Ok(saved_paths)
        })
        .await?;
        
        match result {
            Ok(paths) => {
                responses.push(serde_json::json!({
                    "filename": filename,
                    "paths": paths,
                    "type": content_type
                }));
            },
            Err(e) => {
                log::error!("Image processing error: {}", e);
            }
        }
    }
    
    Ok(HttpResponse::Ok().json(responses))
}
```

## Progress Reporting and Client Integration

Implement progress reporting for uploads:

```rust
// Server-side event stream for progress reporting
use actix_web::{web, HttpResponse, Error};
use actix_web_lab::sse;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use std::collections::HashMap;

// Progress tracker
struct UploadProgressTracker {
    // Map of upload ID to progress data
    progress: Mutex<HashMap<String, ProgressData>>,
}

struct ProgressData {
    total_bytes: usize,
    received_bytes: usize,
    filename: String,
}

impl UploadProgressTracker {
    fn new() -> Self {
        Self {
            progress: Mutex::new(HashMap::new()),
        }
    }
    
    // Create new upload record
    async fn new_upload(&self, id: &str, filename: &str, total_bytes: usize) {
        let mut progress = self.progress.lock().await;
        progress.insert(id.to_string(), ProgressData {
            total_bytes,
            received_bytes: 0,
            filename: filename.to_string(),
        });
    }
    
    // Update progress for an upload
    async fn update_progress(&self, id: &str, bytes: usize) {
        let mut progress = self.progress.lock().await;
        if let Some(data) = progress.get_mut(id) {
            data.received_bytes += bytes;
        }
    }
    
    // Get progress data
    async fn get_progress(&self, id: &str) -> Option<(usize, usize, String)> {
        let progress = self.progress.lock().await;
        progress.get(id).map(|data| (
            data.received_bytes,
            data.total_bytes,
            data.filename.clone()
        ))
    }
    
    // Remove upload after completion
    async fn complete_upload(&self, id: &str) {
        let mut progress = self.progress.lock().await;
        progress.remove(id);
    }
}

// SSE endpoint for progress updates
async fn progress_stream(
    req: HttpRequest,
    upload_id: web::Path<String>,
    tracker: web::Data<UploadProgressTracker>,
) -> Result<HttpResponse, Error> {
    // Create channel for SSE events
    let (tx, rx) = mpsc::channel(100);
    
    // Spawn task to send progress updates
    let upload_id = upload_id.into_inner();
    let tracker_clone = tracker.clone();
    
    actix_web::rt::spawn(async move {
        let mut interval = tokio::time::interval(std::time::Duration::from_millis(500));
        
        loop {
            interval.tick().await;
            
            // Get current progress
            if let Some((received, total, filename)) = tracker_clone.get_progress(&upload_id).await {
                // Calculate percentage
                let percentage = if total > 0 {
                    (received as f64 / total as f64 * 100.0) as u8
                } else { 0 };
                
                // Send progress update
                let event = serde_json::json!({
                    "uploadId": upload_id,
                    "received": received,
                    "total": total,
                    "percentage": percentage,
                    "filename": filename
                });
                
                if tx.send(Ok::<_, Error>(sse::Event::default().json_data(event).unwrap()))
                    .await
                    .is_err() 
                {
                    break; // Client disconnected
                }
                
                // If upload is complete, send completion event and stop
                if received >= total && total > 0 {
                    let complete_event = serde_json::json!({
                        "uploadId": upload_id,
                        "status": "complete",
                        "filename": filename
                    });
                    
                    let _ = tx.send(Ok::<_, Error>(
                        sse::Event::default()
                            .event("complete")
                            .json_data(complete_event)
                            .unwrap()
                    )).await;
                    
                    break;
                }
            } else {
                // Upload not found or completed
                break;
            }
        }
    });
    
    // Return SSE response
    let response = sse::Sse::from_infallible_receiver(rx)
        .with_keep_alive(tokio::time::Duration::from_secs(5));
        
    Ok(response.into_response())
}

// Client-side JavaScript example
/*
const uploadFile = async (file) => {
    // Generate upload ID
    const uploadId = uuidv4();
    
    // Set up event source for progress updates
    const eventSource = new EventSource(`/progress/${uploadId}`);
    
    eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        updateProgressBar(data.percentage);
    };
    
    eventSource.addEventListener('complete', (event) => {
        const data = JSON.parse(event.data);
        showUploadComplete(data.filename);
        eventSource.close();
    });
    
    // Create form data and append file
    const formData = new FormData();
    formData.append('file', file);
    
    // Send upload request with upload ID in header
    const response = await fetch('/upload', {
        method: 'POST',
        headers: {
            'Upload-ID': uploadId
        },
        body: formData
    });
    
    return response.json();
};
*/
```

## Security Considerations

File uploads present significant security challenges in web applications due to the potential for processing and storing malicious files. This section outlines critical security measures and implementations needed to ensure secure file handling.

### Threat Model for File Uploads

Before implementing security measures, understand the specific threats associated with file uploads:

```mermaid
flowchart TD
    A[Attacker] -->|1. Malicious file uploads| S[Web Server]
    A -->|2. Path traversal in filenames| S
    A -->|3. File type spoofing| S
    A -->|4. Oversized files/DoS| S
    A -->|5. Server-side execution| S
    A -->|6. Metadata exploitation| S
    A -->|7. Client-side attacks| S
    
    S --> D[(Storage)]
    S --> P[Processing Engine]
    S --> C[Content Delivery]
    
    C --> U[Users]
    A -->|8. Stored XSS via SVG| U
    
    class A fill:#f96,stroke:#333
    class S fill:#69f,stroke:#333
    class D,P,C fill:#6d9,stroke:#333
    class U fill:#d8bfd8,stroke:#333
```

### Secure File Validation Implementation

Implement comprehensive file validation that checks both file metadata and content:

```rust
use infer::Infer;
use std::collections::HashSet;
use std::path::Path;

/// Configuration for secure file uploads
pub struct FileSecurityConfig {
    /// Maximum file size in bytes
    pub max_file_size: usize,
    
    /// Allowed MIME types
    pub allowed_mime_types: HashSet<String>,
    
    /// Allowed file extensions
    pub allowed_extensions: HashSet<String>,
    
    /// Whether to scan files for malware
    pub enable_virus_scan: bool,
    
    /// Disallowed characters in filenames
    pub disallowed_chars: Vec<char>,
    
    /// Whether to strip metadata from images
    pub strip_metadata: bool,
}

impl Default for FileSecurityConfig {
    fn default() -> Self {
        let mut allowed_mime_types = HashSet::new();
        // Images
        allowed_mime_types.insert("image/jpeg".to_string());
        allowed_mime_types.insert("image/png".to_string());
        allowed_mime_types.insert("image/gif".to_string());
        // Documents
        allowed_mime_types.insert("application/pdf".to_string());
        allowed_mime_types.insert("application/msword".to_string());
        allowed_mime_types.insert("application/vnd.openxmlformats-officedocument.wordprocessingml.document".to_string());
        
        let mut allowed_extensions = HashSet::new();
        allowed_extensions.insert("jpg".to_string());
        allowed_extensions.insert("jpeg".to_string());
        allowed_extensions.insert("png".to_string());
        allowed_extensions.insert("gif".to_string());
        allowed_extensions.insert("pdf".to_string());
        allowed_extensions.insert("doc".to_string());
        allowed_extensions.insert("docx".to_string());
        
        Self {
            max_file_size: 10 * 1024 * 1024, // 10MB
            allowed_mime_types,
            allowed_extensions,
            enable_virus_scan: true,
            disallowed_chars: vec!['/', '\\', ':', '*', '?', '"', '<', '>', '|', ';'],
            strip_metadata: true,
        }
    }
}

/// Service for validating and securing file uploads
pub struct FileSecurityService {
    config: FileSecurityConfig,
    logger: Logger,
}

impl FileSecurityService {
    /// Create a new file security service
    pub fn new(config: FileSecurityConfig, logger: Logger) -> Self {
        Self { config, logger }
    }
    
    /// Complete file validation process
    pub fn validate_file(&self, 
                        data: &[u8], 
                        claimed_filename: &str, 
                        claimed_mime: &str) -> Result<(), FileValidationError> {
        // 1. Check file size
        if data.len() > self.config.max_file_size {
            self.logger.warn(
                "File size exceeds limit", 
                &[("size", &data.len().to_string()), 
                  ("limit", &self.config.max_file_size.to_string())]
            );
            return Err(FileValidationError::FileTooLarge(
                data.len(), 
                self.config.max_file_size
            ));
        }
        
        // 2. Validate filename and extension
        if let Err(e) = self.validate_filename(claimed_filename) {
            return Err(e);
        }
        
        // 3. Validate MIME type against allowed list
        if !self.config.allowed_mime_types.contains(claimed_mime) {
            self.logger.warn(
                "Disallowed MIME type", 
                &[("mime", claimed_mime)]
            );
            return Err(FileValidationError::DisallowedMimeType(claimed_mime.to_string()));
        }
        
        // 4. Extract extension from filename
        let extension = Path::new(claimed_filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| ext.to_lowercase())
            .unwrap_or_default();
            
        if !self.config.allowed_extensions.contains(&extension) {
            self.logger.warn(
                "Disallowed file extension", 
                &[("extension", &extension)]
            );
            return Err(FileValidationError::DisallowedExtension(extension));
        }
        
        // 5. Detect actual file type from content
        let kind = infer::get(data).ok_or_else(|| {
            self.logger.warn(
                "Unable to determine file type from content", 
                &[("filename", claimed_filename)]
            );
            FileValidationError::UnknownFileType
        })?;
        
        // 6. Check if detected type matches claimed type
        if kind.mime_type() != claimed_mime {
            self.logger.warn(
                "File type mismatch", 
                &[("claimed", claimed_mime), 
                  ("detected", kind.mime_type())]
            );
            return Err(FileValidationError::TypeMismatch(
                claimed_mime.to_string(), 
                kind.mime_type().to_string()
            ));
        }
        
        // 7. Check for malware if enabled
        if self.config.enable_virus_scan {
            self.scan_for_malware(data)?;
        }
        
        Ok(())
    }
    
    /// Validate and sanitize a filename
    fn validate_filename(&self, filename: &str) -> Result<String, FileValidationError> {
        // Check for disallowed characters
        if filename.chars().any(|c| self.config.disallowed_chars.contains(&c)) {
            self.logger.warn(
                "Filename contains disallowed characters", 
                &[("filename", filename)]
            );
            return Err(FileValidationError::InvalidFilename(filename.to_string()));
        }
        
        // Check for path traversal attempts
        if filename.contains("..") {
            self.logger.warn(
                "Path traversal attempt detected", 
                &[("filename", filename)]
            );
            return Err(FileValidationError::PathTraversalAttempt(filename.to_string()));
        }
        
        // Remove any path components and keep just the filename
        let safe_filename = Path::new(filename)
            .file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("unnamed");
            
        // Add UUID for uniqueness
        let unique_filename = format!("{}-{}", uuid::Uuid::new_v4(), safe_filename);
        
        Ok(unique_filename)
    }
    
    /// Scan file for malware
    fn scan_for_malware(&self, data: &[u8]) -> Result<(), FileValidationError> {
        // Create a temporary file for scanning
        let mut temp_file = tempfile::NamedTempFile::new()
            .map_err(|e| FileValidationError::ScanError(format!("Failed to create temp file: {}", e)))?;
            
        // Write file data to temp file
        temp_file.write_all(data)
            .map_err(|e| FileValidationError::ScanError(format!("Failed to write temp file: {}", e)))?;
            
        // Get path of temp file
        let temp_path = temp_file.path().to_string_lossy();
        
        // Run ClamAV scan
        let output = std::process::Command::new("clamdscan")
            .args(&["--no-summary", temp_path.as_ref()])
            .output()
            .map_err(|e| FileValidationError::ScanError(format!("Failed to run virus scan: {}", e)))?;
            
        // Check scan result
        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            let stdout = String::from_utf8_lossy(&output.stdout);
            
            self.logger.warn(
                "Malware detected in uploaded file", 
                &[("stdout", &stdout), ("stderr", &stderr)]
            );
            
            return Err(FileValidationError::MalwareDetected);
        }
        
        Ok(())
    }
}

/// Error types for file validation
pub enum FileValidationError {
    FileTooLarge(usize, usize),
    DisallowedMimeType(String),
    DisallowedExtension(String),
    InvalidFilename(String),
    PathTraversalAttempt(String),
    TypeMismatch(String, String),
    UnknownFileType,
    MalwareDetected,
    ScanError(String),
}
```

### Secure Storage Practices

Implement secure file storage that prevents unauthorized access and execution:

```rust
use std::path::{Path, PathBuf};
use uuid::Uuid;

/// Storage configuration for secure file handling
pub struct SecureStorageConfig {
    /// Base directory for file storage
    pub base_dir: PathBuf,
    
    /// Directory permissions (Unix only)
    pub dir_permissions: u32,
    
    /// File permissions (Unix only)
    pub file_permissions: u32,
    
    /// Whether to use isolated namespacing per user
    pub use_user_isolation: bool,
    
    /// Whether to distribute files across subdirectories
    pub shard_files: bool,
}

impl Default for SecureStorageConfig {
    fn default() -> Self {
        Self {
            base_dir: PathBuf::from("./secure_uploads"),
            dir_permissions: 0o755, // rwxr-xr-x
            file_permissions: 0o644, // rw-r--r--
            use_user_isolation: true,
            shard_files: true,
        }
    }
}

/// Service for secure file storage
pub struct SecureFileStorage {
    config: SecureStorageConfig,
    logger: Logger,
}

impl SecureFileStorage {
    /// Create a new secure file storage service
    pub fn new(config: SecureStorageConfig, logger: Logger) -> Self {
        Self { config, logger }
    }
    
    /// Store a file securely
    pub async fn store_file(&self, 
                          data: &[u8], 
                          filename: &str, 
                          user_id: Option<&str>) -> Result<StoredFileInfo, StorageError> {
        // Generate a UUID for the file
        let file_uuid = Uuid::new_v4();
        
        // Determine storage path with sharding if enabled
        let storage_path = if self.config.shard_files {
            // Shard by first characters of UUID for better directory distribution
            let uuid_str = file_uuid.to_string();
            let shard = &uuid_str[0..2];
            
            let mut path = self.config.base_dir.clone();
            
            // Add user isolation if enabled
            if self.config.use_user_isolation && user_id.is_some() {
                path = path.join("users").join(user_id.unwrap());
            }
            
            // Add shard directory
            path.join(shard)
        } else {
            let mut path = self.config.base_dir.clone();
            
            // Add user isolation if enabled
            if self.config.use_user_isolation && user_id.is_some() {
                path = path.join("users").join(user_id.unwrap());
            }
            
            path
        };
        
        // Create directory if it doesn't exist
        if !storage_path.exists() {
            tokio::fs::create_dir_all(&storage_path).await
                .map_err(|e| {
                    self.logger.error(
                        "Failed to create storage directory", 
                        &[("path", &storage_path.display().to_string()), 
                          ("error", &e.to_string())]
                    );
                    StorageError::DirectoryCreationFailed(e.to_string())
                })?;
                
            // Set directory permissions on Unix
            #[cfg(unix)]
            {
                use std::os::unix::fs::PermissionsExt;
                
                let permissions = std::fs::Permissions::from_mode(self.config.dir_permissions);
                tokio::fs::set_permissions(&storage_path, permissions).await
                    .map_err(|e| StorageError::PermissionError(e.to_string()))?;
            }
        }
        
        // Create full file path with UUID to prevent collisions
        let safe_filename = Path::new(filename).file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("unnamed");
            
        let file_path = storage_path.join(format!("{}-{}", file_uuid, safe_filename));
        
        // Write file
        tokio::fs::write(&file_path, data).await
            .map_err(|e| {
                self.logger.error(
                    "Failed to write file", 
                    &[("path", &file_path.display().to_string()), 
                      ("error", &e.to_string())]
                );
                StorageError::WriteError(e.to_string())
            })?;
            
        // Set file permissions on Unix
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            
            let permissions = std::fs::Permissions::from_mode(self.config.file_permissions);
            tokio::fs::set_permissions(&file_path, permissions).await
                .map_err(|e| StorageError::PermissionError(e.to_string()))?;
        }
        
        // Log successful storage
        self.logger.info(
            "File stored successfully", 
            &[("file_uuid", &file_uuid.to_string()), 
              ("path", &file_path.display().to_string())]
        );
        
        // Return storage information
        Ok(StoredFileInfo {
            uuid: file_uuid,
            path: file_path.to_string_lossy().to_string(),
            filename: safe_filename.to_string(),
            size: data.len(),
        })
    }
    
    /// Securely delete a file
    pub async fn delete_file(&self, file_uuid: &Uuid) -> Result<(), StorageError> {
        // Find file by UUID pattern
        let pattern = format!("{}-*", file_uuid);
        let mut found = false;
        
        let walk_dir = self.config.base_dir.clone();
        let mut entries = tokio::fs::read_dir(walk_dir).await
            .map_err(|e| StorageError::ReadError(e.to_string()))?;
            
        while let Some(entry) = entries.next_entry().await
            .map_err(|e| StorageError::ReadError(e.to_string()))? 
        {
            let path = entry.path();
            
            // Check if this is a file and matches our UUID pattern
            if path.is_file() && 
               path.file_name()
                   .and_then(|name| name.to_str())
                   .map(|name| name.starts_with(&file_uuid.to_string()))
                   .unwrap_or(false) 
            {
                // Secure deletion with overwrite
                self.secure_delete_file(&path).await?;
                found = true;
                break;
            }
        }
        
        if !found {
            return Err(StorageError::FileNotFound(file_uuid.to_string()));
        }
        
        Ok(())
    }
    
    /// Securely delete a file with overwrite
    async fn secure_delete_file(&self, path: &Path) -> Result<(), StorageError> {
        // Get file size
        let metadata = tokio::fs::metadata(path).await
            .map_err(|e| StorageError::ReadError(e.to_string()))?;
            
        let file_size = metadata.len() as usize;
        
        // Open file for writing
        let mut file = tokio::fs::OpenOptions::new()
            .write(true)
            .open(path)
            .await
            .map_err(|e| StorageError::WriteError(e.to_string()))?;
            
        // Create a buffer with random data
        let mut buffer = vec![0u8; 1024]; // 1KB buffer
        getrandom::getrandom(&mut buffer)
            .map_err(|e| StorageError::RandomError(e.to_string()))?;
            
        // Overwrite file contents with random data
        let mut remaining = file_size;
        while remaining > 0 {
            let to_write = std::cmp::min(buffer.len(), remaining);
            file.write_all(&buffer[..to_write]).await
                .map_err(|e| StorageError::WriteError(e.to_string()))?;
                
            remaining = remaining.saturating_sub(to_write);
        }
        
        // Flush changes
        file.flush().await
            .map_err(|e| StorageError::WriteError(e.to_string()))?;
            
        // Close file
        drop(file);
        
        // Delete the file
        tokio::fs::remove_file(path).await
            .map_err(|e| StorageError::DeleteError(e.to_string()))?;
            
        self.logger.info(
            "File securely deleted", 
            &[("path", &path.display().to_string())]
        );
        
        Ok(())
    }
}

/// Information about a stored file
pub struct StoredFileInfo {
    uuid: Uuid,
    path: String,
    filename: String,
    size: usize,
}

/// Error types for file storage
pub enum StorageError {
    DirectoryCreationFailed(String),
    WriteError(String),
    ReadError(String),
    DeleteError(String),
    PermissionError(String),
    FileNotFound(String),
    RandomError(String),
}
```

### Defense in Depth: Multiple Validation Layers

Implement a multi-layered defense strategy that validates files at various stages:

```rust
use actix_web::{web, Error, HttpResponse};
use actix_multipart::Multipart;
use futures::{StreamExt, TryStreamExt};
use std::sync::Arc;

/// Handler with layered security checks
async fn secure_upload_handler(
    mut payload: Multipart,
    security_service: web::Data<Arc<FileSecurityService>>,
    storage_service: web::Data<Arc<SecureFileStorage>>,
    request: HttpRequest,
) -> Result<HttpResponse, Error> {
    // Layer 1: Authenticate user before allowing uploads
    let user = get_authenticated_user(&request)?;
    
    // Track metrics for this upload session
    let mut total_files = 0;
    let mut total_size = 0;
    let mut rejected_files = 0;
    
    // Store information about accepted files
    let mut accepted_files = Vec::new();
    
    // Layer 2: Rate limiting per user (check if user has exceeded upload quota)
    if let Err(e) = check_user_quota(&user.id).await {
        return Ok(HttpResponse::TooManyRequests().json(json!({
            "error": "Upload quota exceeded",
            "details": e.to_string(),
            "retry_after": 3600 // 1 hour
        })));
    }
    
    // Process each file in the multipart request
    while let Some(mut field) = payload.try_next().await? {
        // Layer 3: Extract and validate field metadata
        let content_disposition = field.content_disposition();
        
        let original_filename = content_disposition
            .get_filename()
            .ok_or_else(|| {
                actix_web::error::ErrorBadRequest("No filename provided")
            })?;
            
        let content_type = field
            .content_type()
            .map(|ct| ct.to_string())
            .unwrap_or_else(|| "application/octet-stream".to_string());
            
        // Collect file data with size limits
        let mut file_data = Vec::new();
        let mut file_size = 0;
        const CHUNK_SIZE_LIMIT: usize = 2 * 1024 * 1024; // 2MB per chunk
        
        // Layer 4: Stream processing with chunk validation
        while let Some(chunk) = field.try_next().await? {
            // Check chunk size
            if chunk.len() > CHUNK_SIZE_LIMIT {
                log::warn!(
                    "Chunk size exceeded limit: {} bytes (user: {})", 
                    chunk.len(), user.id
                );
                return Err(actix_web::error::ErrorBadRequest("Chunk size too large"));
            }
            
            // Update size and check total file size limit during streaming
            file_size += chunk.len();
            if file_size > security_service.config.max_file_size {
                log::warn!(
                    "File size exceeded limit during streaming: {} bytes (user: {})", 
                    file_size, user.id
                );
                return Err(actix_web::error::ErrorBadRequest("File too large"));
            }
            
            // Add chunk to file data
            file_data.extend_from_slice(&chunk);
        }
        
        // Layer 5: Comprehensive file validation
        match security_service.validate_file(&file_data, original_filename, &content_type) {
            Ok(()) => {
                // Layer 6: Secure storage
                let storage_result = storage_service
                    .store_file(&file_data, original_filename, Some(&user.id))
                    .await;
                    
                match storage_result {
                    Ok(file_info) => {
                        // Layer 7: Post-storage validation (e.g., secondary virus scan)
                        match perform_secondary_validation(&file_info.path).await {
                            Ok(()) => {
                                // File accepted
                                total_files += 1;
                                total_size += file_size;
                                accepted_files.push(file_info);
                                
                                // Layer 8: Log successful upload for audit trail
                                log::info!(
                                    "File uploaded successfully: {} ({} bytes) by user {}", 
                                    original_filename, file_size, user.id
                                );
                            },
                            Err(e) => {
                                // Secondary validation failed, delete the file
                                let _ = storage_service.delete_file(&file_info.uuid).await;
                                rejected_files += 1;
                                
                                log::warn!(
                                    "Secondary validation failed for file {}: {}", 
                                    original_filename, e
                                );
                            }
                        }
                    },
                    Err(e) => {
                        rejected_files += 1;
                        log::error!(
                            "Storage error for file {}: {}", 
                            original_filename, e
                        );
                    }
                }
            },
            Err(e) => {
                // File validation failed
                rejected_files += 1;
                log::warn!(
                    "File validation failed: {} - {}", 
                    original_filename, e
                );
            }
        }
    }
    
    // Layer 9: Update user metrics and quotas
    update_user_upload_metrics(&user.id, total_files, total_size).await?;
    
    // Layer 10: Return appropriate response
    Ok(HttpResponse::Ok().json(json!({
        "status": "success",
        "files_accepted": total_files,
        "files_rejected": rejected_files,
        "total_size": total_size,
        "files": accepted_files
    })))
}
```

### Best Practices for Secure File Uploads

1. **Isolate Upload Storage**:
   - Store uploaded files outside the web root directory
   - Use a dedicated file server or cloud storage when possible
   - Implement proper access controls on upload directories

2. **Implement Strict Validation**:
   - Always validate file content, not just extensions or MIME types
   - Use multiple validation layers for defense in depth
   - Log and alert on validation failures for potential attack detection

3. **Apply Least Privilege Principle**:
   - Run file processing with minimal permissions
   - Use dedicated service accounts for file operations
   - Implement separate microservices for file handling when possible

4. **Use Sandboxing for Execution Protection**:
   - Process files in isolated environments
   - Utilize containerization for file processing tasks
   - Implement execution controls to prevent uploaded files from being executed

5. **Implement Secure Processing Pipelines**:
   - Process files asynchronously without blocking the main request thread
   - Implement timeouts for all file operations
   - Use transactional processing to ensure file consistency

6. **Consider Rate Limiting and Quotas**:
   - Implement per-user upload quotas
   - Apply rate limiting to prevent upload-based DoS attacks
   - Track and alert on unusual file upload patterns

7. **Implement Secure Metadata Handling**:
   - Strip metadata from images to prevent information leakage
   - Sanitize filenames before storing or displaying
   - Generate random names for stored files to prevent enumeration

8. **Apply Content Security Policies**:
   - Use Content-Security-Policy headers to prevent XSS in user-uploaded content
   - Implement X-Content-Type-Options to prevent MIME sniffing
   - Serve uploaded files with appropriate content dispositions

9. **Implement Monitoring and Auditing**:
   - Log all file operations with user context for audit trails
   - Monitor file storage usage for anomalies
   - Implement alerts for suspicious file activities

10. **Apply Secure Content Delivery**:
    - Use a dedicated CDN or specialized service for serving uploaded files
    - Implement expiring URLs for sensitive file access
    - Apply proper caching controls to prevent unauthorized access

### Security Monitoring and Detection

Implement monitoring to detect and respond to file upload attacks:

```rust
use chrono::{DateTime, Utc};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::collections::HashMap;
use tokio::sync::Mutex;

/// File upload monitoring service
pub struct FileUploadMonitor {
    /// Rate tracking per user
    user_rates: Mutex<HashMap<String, UserUploadStats>>,
    
    /// Global upload stats
    global_stats: FileUploadGlobalStats,
    
    /// Alert threshold configuration
    alert_thresholds: AlertThresholds,
    
    /// Alert manager for sending notifications
    alert_manager: Arc<AlertManager>,
}

/// Statistics for a specific user's uploads
struct UserUploadStats {
    /// Recent upload timestamps
    recent_uploads: Vec<DateTime<Utc>>,
    
    /// Total bytes uploaded in current period
    bytes_uploaded: usize,
    
    /// Number of rejected uploads
    rejection_count: usize,
    
    /// Time window start for rate tracking
    window_start: DateTime<Utc>,
}

/// Global file upload statistics
struct FileUploadGlobalStats {
    /// Total uploads processed
    total_uploads: AtomicUsize,
    
    /// Total uploads rejected
    total_rejected: AtomicUsize,
    
    /// Current uploads in progress
    current_uploads: AtomicUsize,
    
    /// Total bytes uploaded
    total_bytes: AtomicUsize,
}

/// Alert thresholds for suspicious activity
struct AlertThresholds {
    /// Maximum uploads per minute per user
    max_uploads_per_minute: usize,
    
    /// Maximum data per minute per user (bytes)
    max_data_per_minute: usize,
    
    /// Rejection rate that triggers alerts (percentage)
    rejection_rate_threshold: f32,
    
    /// Minimum number of uploads before applying rejection rate
    min_uploads_for_rejection_rate: usize,
}

impl FileUploadMonitor {
    /// Create a new file upload monitor
    pub fn new(alert_manager: Arc<AlertManager>) -> Self {
        Self {
            user_rates: Mutex::new(HashMap::new()),
            global_stats: FileUploadGlobalStats {
                total_uploads: AtomicUsize::new(0),
                total_rejected: AtomicUsize::new(0),
                current_uploads: AtomicUsize::new(0),
                total_bytes: AtomicUsize::new(0),
            },
            alert_thresholds: AlertThresholds {
                max_uploads_per_minute: 30,
                max_data_per_minute: 50 * 1024 * 1024, // 50MB
                rejection_rate_threshold: 0.2, // 20%
                min_uploads_for_rejection_rate: 5,
            },
            alert_manager,
        }
    }
    
    /// Track the start of a file upload
    pub async fn track_upload_start(&self, user_id: &str) {
        // Increment current uploads counter
        self.global_stats.current_uploads.fetch_add(1, Ordering::SeqCst);
        
        // Update user stats
        let mut user_rates = self.user_rates.lock().await;
        let now = Utc::now();
        
        let user_stats = user_rates.entry(user_id.to_string())
            .or_insert_with(|| UserUploadStats {
                recent_uploads: Vec::new(),
                bytes_uploaded: 0,
                rejection_count: 0,
                window_start: now,
            });
            
        // Add current timestamp to recent uploads
        user_stats.recent_uploads.push(now);
        
        // Check for rate limit violations
        self.check_upload_rate(user_id, user_stats).await;
    }
    
    /// Track the completion of a file upload
    pub async fn track_upload_complete(&self, 
                                     user_id: &str, 
                                     file_size: usize, 
                                     was_rejected: bool) {
        // Update global stats
        self.global_stats.current_uploads.fetch_sub(1, Ordering::SeqCst);
        self.global_stats.total_uploads.fetch_add(1, Ordering::SeqCst);
        
        if was_rejected {
            self.global_stats.total_rejected.fetch_add(1, Ordering::SeqCst);
        } else {
            self.global_stats.total_bytes.fetch_add(file_size, Ordering::SeqCst);
        }
        
        // Update user stats
        let mut user_rates = self.user_rates.lock().await;
        
        if let Some(user_stats) = user_rates.get_mut(user_id) {
            if was_rejected {
                user_stats.rejection_count += 1;
                
                // Check rejection rate
                self.check_rejection_rate(user_id, user_stats).await;
            } else {
                user_stats.bytes_uploaded += file_size;
                
                // Check data rate
                self.check_data_rate(user_id, user_stats).await;
            }
        }
    }
    
    /// Check user's upload rate for suspicious activity
    async fn check_upload_rate(&self, user_id: &str, stats: &UserUploadStats) {
        let now = Utc::now();
        let one_minute_ago = now - chrono::Duration::minutes(1);
        
        // Count uploads in the last minute
        let recent_count = stats.recent_uploads.iter()
            .filter(|time| **time >= one_minute_ago)
            .count();
            
        if recent_count >= self.alert_thresholds.max_uploads_per_minute {
            // Alert on high upload rate
            self.alert_manager.send_alert(
                AlertLevel::Warning,
                "High upload rate detected",
                &[
                    ("user_id", user_id),
                    ("uploads_per_minute", &recent_count.to_string()),
                    ("threshold", &self.alert_thresholds.max_uploads_per_minute.to_string()),
                ],
            ).await;
        }
    }
    
    /// Check user's data upload rate for suspicious activity
    async fn check_data_rate(&self, user_id: &str, stats: &UserUploadStats) {
        let now = Utc::now();
        let minutes_elapsed = (now - stats.window_start).num_milliseconds() as f64 / 60000.0;
        
        if minutes_elapsed >= 1.0 {
            // Calculate bytes per minute
            let bytes_per_minute = (stats.bytes_uploaded as f64 / minutes_elapsed) as usize;
            
            if bytes_per_minute >= self.alert_thresholds.max_data_per_minute {
                // Alert on high data rate
                self.alert_manager.send_alert(
                    AlertLevel::Warning,
                    "High data upload rate detected",
                    &[
                        ("user_id", user_id),
                        ("bytes_per_minute", &bytes_per_minute.to_string()),
                        ("threshold", &self.alert_thresholds.max_data_per_minute.to_string()),
                    ],
                ).await;
            }
        }
    }
    
    /// Check user's rejection rate for suspicious activity
    async fn check_rejection_rate(&self, user_id: &str, stats: &UserUploadStats) {
        let total_uploads = stats.recent_uploads.len();
        
        if total_uploads >= self.alert_thresholds.min_uploads_for_rejection_rate {
            let rejection_rate = stats.rejection_count as f32 / total_uploads as f32;
            
            if rejection_rate >= self.alert_thresholds.rejection_rate_threshold {
                // Alert on high rejection rate
                self.alert_manager.send_alert(
                    AlertLevel::Medium,
                    "High file rejection rate detected",
                    &[
                        ("user_id", user_id),
                        ("rejection_rate", &format!("{:.2}", rejection_rate)),
                        ("threshold", &format!("{:.2}", self.alert_thresholds.rejection_rate_threshold)),
                        ("total_uploads", &total_uploads.to_string()),
                    ],
                ).await;
            }
        }
    }
}
```

## Knowledge Check

1. What is multipart form data and why is it used for file uploads?
   - Multipart form data is a format used to submit forms with binary data (like files) and text fields together.
   - It uses boundaries to separate different parts of the form, allowing file uploads alongside regular form fields.

2. What security measures should be implemented for file uploads?
   - File type validation to verify the actual content matches the claimed MIME type
   - Size limits to prevent denial of service attacks
   - Antivirus scanning to detect malicious files
   - Storage outside the webserver's document root
   - Sanitized filenames to prevent path traversal

3. How can you handle large file uploads efficiently in Rust?
   - Stream data directly to storage without loading the entire file into memory
   - Implement chunked uploading with resumable capabilities
   - Use asynchronous I/O operations for better performance
   - Offload processing to background tasks

4. What is the purpose of image processing for uploaded files?
   - Creating thumbnails for efficient display in applications
   - Stripping metadata for privacy
   - Optimizing images for web display
   - Converting between formats for compatibility

5. How can you integrate cloud storage for file uploads?
   - Using SDK libraries like rusoto for AWS S3
   - Implementing direct-to-cloud uploads via presigned URLs
   - Setting appropriate permissions and bucket policies
   - Handling metadata and post-processing callbacks

## Additional Resources

- [Actix Multipart Documentation](https://docs.rs/actix-multipart)
- [OWASP File Upload Security Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/File_Upload_Security_Cheat_Sheet.html)
- [AWS S3 Rust SDK Documentation](https://docs.rs/rusoto_s3)
- [Image Processing with Rust](https://docs.rs/image)
- [Multipart Form Data Specification](https://tools.ietf.org/html/rfc7578)
