# API Design (RESTful Endpoints)

## Learning Objectives
- Design consistent and intuitive REST APIs in Rust
- Implement proper resource-oriented API architecture
- Handle complex API concerns such as versioning, pagination, and filtering
- Document your API using OpenAPI/Swagger
- Apply hypermedia principles for more flexible APIs

## Prerequisites
- Understanding of HTTP protocol fundamentals
- Basic knowledge of JSON and serialization
- Familiarity with Rust web frameworks

## Introduction

A well-designed API is crucial for any modern webserver. This module covers the principles and best practices for designing RESTful APIs in Rust, focusing on consistency, usability, and maintainability.

## RESTful API Fundamentals

REST (Representational State Transfer) is an architectural style for designing networked applications. Key principles include:

- **Resource-Based**: Everything is a resource identified by a unique URI
- **Standard Methods**: Using HTTP verbs (GET, POST, PUT, DELETE, etc.) for standard operations
- **Representation**: Resources can have multiple representations (JSON, XML, etc.)
- **Stateless**: Each request contains all necessary information
- **HATEOAS**: Hypermedia as the Engine of Application State

### Basic REST API Design with Axum

```rust
use axum::{
    routing::{get, post, put, delete},
    http::StatusCode,
    Json, Router,
    extract::{Path, State},
};
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use std::net::SocketAddr;

// Resource model
#[derive(Debug, Clone, Serialize, Deserialize)]
struct User {
    id: u64,
    name: String,
    email: String,
}

// Application state
#[derive(Clone)]
struct AppState {
    users: Arc<Mutex<HashMap<u64, User>>>,
    next_id: Arc<Mutex<u64>>,
}

// Request and response types
#[derive(Deserialize)]
struct CreateUserRequest {
    name: String,
    email: String,
}

#[derive(Deserialize)]
struct UpdateUserRequest {
    name: Option<String>,
    email: Option<String>,
}

#[derive(Serialize)]
struct ApiResponse<T> {
    success: bool,
    data: Option<T>,
    message: Option<String>,
}

// Helper function to create API responses
fn api_response<T>(success: bool, data: Option<T>, message: Option<String>) -> Json<ApiResponse<T>> {
    Json(ApiResponse { success, data, message })
}

// API handlers
async fn get_users(State(state): State<AppState>) -> Json<ApiResponse<Vec<User>>> {
    let users = state.users.lock().unwrap();
    let user_list = users.values().cloned().collect();
    api_response(true, Some(user_list), None)
}

async fn get_user(
    Path(id): Path<u64>, 
    State(state): State<AppState>
) -> Result<Json<ApiResponse<User>>, (StatusCode, Json<ApiResponse<()>>) > {
    let users = state.users.lock().unwrap();
    
    match users.get(&id) {
        Some(user) => Ok(api_response(true, Some(user.clone()), None)),
        None => Err((
            StatusCode::NOT_FOUND,
            api_response(false, None, Some(format!("User with ID {} not found", id)))
        )),
    }
}

async fn create_user(
    State(state): State<AppState>,
    Json(payload): Json<CreateUserRequest>
) -> (StatusCode, Json<ApiResponse<User>>) {
    // Get next available ID
    let mut id = state.next_id.lock().unwrap();
    let new_id = *id;
    *id += 1;
    
    // Create new user
    let user = User {
        id: new_id,
        name: payload.name,
        email: payload.email,
    };
    
    // Store user
    let mut users = state.users.lock().unwrap();
    users.insert(new_id, user.clone());
    
    // Return response
    (
        StatusCode::CREATED,
        api_response(true, Some(user), Some("User created successfully".to_string()))
    )
}

async fn update_user(
    Path(id): Path<u64>,
    State(state): State<AppState>,
    Json(payload): Json<UpdateUserRequest>
) -> Result<Json<ApiResponse<User>>, (StatusCode, Json<ApiResponse<()>>) > {
    let mut users = state.users.lock().unwrap();
    
    if let Some(user) = users.get_mut(&id) {
        // Update fields if provided
        if let Some(name) = payload.name {
            user.name = name;
        }
        
        if let Some(email) = payload.email {
            user.email = email;
        }
        
        Ok(api_response(true, Some(user.clone()), Some("User updated successfully".to_string())))
    } else {
        Err((
            StatusCode::NOT_FOUND,
            api_response(false, None, Some(format!("User with ID {} not found", id)))
        ))
    }
}

async fn delete_user(
    Path(id): Path<u64>,
    State(state): State<AppState>
) -> Result<Json<ApiResponse<()>>, (StatusCode, Json<ApiResponse<()>>) > {
    let mut users = state.users.lock().unwrap();
    
    if users.remove(&id).is_some() {
        Ok(api_response(true, None, Some("User deleted successfully".to_string())))
    } else {
        Err((
            StatusCode::NOT_FOUND,
            api_response(false, None, Some(format!("User with ID {} not found", id)))
        ))
    }
}

// Create API router
fn create_api_router() -> Router<AppState> {
    Router::new()
        .route("/users", get(get_users))
        .route("/users", post(create_user))
        .route("/users/:id", get(get_user))
        .route("/users/:id", put(update_user))
        .route("/users/:id", delete(delete_user))
}

#[tokio::main]
async fn main() {
    // Initialize application state
    let state = AppState {
        users: Arc::new(Mutex::new(HashMap::new())),
        next_id: Arc::new(Mutex::new(1)),
    };
    
    // Build application with API router
    let app = create_api_router()
        .with_state(state);
    
    // Run server
    let addr = SocketAddr::from(([127, 0, 0, 1], 3000));
    println!("REST API server running on {}", addr);
    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await
        .unwrap();
}
```

## Advanced API Design Patterns

### 1. API Versioning

Versioning is critical for maintaining backward compatibility as your API evolves:

```rust
// Version in URL path
let app = Router::new()
    .nest("/api/v1", api_v1_routes())
    .nest("/api/v2", api_v2_routes());

// Or using header-based versioning
async fn versioned_endpoint(
    TypedHeader(version): TypedHeader<headers::ApiVersion>,
    // other parameters...
) -> impl IntoResponse {
    match version.0.as_str() {
        "1" => handle_v1_request().await,
        "2" => handle_v2_request().await,
        _ => Err(StatusCode::NOT_IMPLEMENTED),
    }
}
```

### 2. Collection Pagination

Handle large result sets with pagination:

```rust
#[derive(Deserialize)]
struct PaginationParams {
    page: Option<usize>,
    per_page: Option<usize>,
}

#[derive(Serialize)]
struct PaginatedResponse<T> {
    data: Vec<T>,
    meta: PaginationMeta,
}

#[derive(Serialize)]
struct PaginationMeta {
    current_page: usize,
    per_page: usize,
    total_items: usize,
    total_pages: usize,
    has_next_page: bool,
    has_prev_page: bool,
}

async fn get_paginated_users(
    Query(params): Query<PaginationParams>,
    State(state): State<AppState>,
) -> impl IntoResponse {
    // Set defaults
    let page = params.page.unwrap_or(1);
    let per_page = params.per_page.unwrap_or(10).min(100); // Limit maximum
    
    let users = state.users.lock().unwrap();
    let total_items = users.len();
    
    // Calculate pagination
    let total_pages = (total_items + per_page - 1) / per_page;
    let offset = (page - 1) * per_page;
    
    // Get page of results
    let items = users.values()
        .skip(offset)
        .take(per_page)
        .cloned()
        .collect::<Vec<_>>();
    
    // Create pagination metadata
    let meta = PaginationMeta {
        current_page: page,
        per_page,
        total_items,
        total_pages,
        has_next_page: page < total_pages,
        has_prev_page: page > 1,
    };
    
    // Return paginated response
    Json(PaginatedResponse { data: items, meta })
}
```

### 3. Resource Filtering, Sorting and Search

Allow clients to filter, sort, and search resources:

```rust
#[derive(Deserialize)]
struct UserFilters {
    name: Option<String>,
    email_domain: Option<String>,
    sort_by: Option<String>,
    order: Option<String>,
    search: Option<String>,
}

async fn filter_users(
    Query(filters): Query<UserFilters>,
    State(state): State<AppState>,
) -> impl IntoResponse {
    let users = state.users.lock().unwrap();
    
    // Start with all users
    let mut results: Vec<User> = users.values().cloned().collect();
    
    // Apply filters
    if let Some(name) = &filters.name {
        results.retain(|user| user.name == *name);
    }
    
    if let Some(domain) = &filters.email_domain {
        results.retain(|user| user.email.ends_with(&format!("@{}", domain)));
    }
    
    // Apply search
    if let Some(query) = &filters.search {
        let query_lower = query.to_lowercase();
        results.retain(|user| {
            user.name.to_lowercase().contains(&query_lower) || 
            user.email.to_lowercase().contains(&query_lower)
        });
    }
    
    // Apply sorting
    if let Some(sort_by) = &filters.sort_by {
        let ascending = filters.order.as_deref() != Some("desc");
        
        match sort_by.as_str() {
            "name" => {
                if ascending {
                    results.sort_by(|a, b| a.name.cmp(&b.name));
                } else {
                    results.sort_by(|a, b| b.name.cmp(&a.name));
                }
            },
            "id" => {
                if ascending {
                    results.sort_by_key(|u| u.id);
                } else {
                    results.sort_by_key(|u| std::cmp::Reverse(u.id));
                }
            },
            _ => {} // Ignore invalid sort fields
        }
    }
    
    Json(ApiResponse {
        success: true,
        data: Some(results),
        message: None,
    })
}
```

### 4. HATEOAS (Hypermedia as the Engine of Application State)

Enhance API discoverability with hypermedia links:

```rust
#[derive(Serialize)]
struct Link {
    href: String,
    rel: String,
    method: String,
}

#[derive(Serialize)]
struct UserWithLinks {
    #[serde(flatten)]
    user: User,
    links: Vec<Link>,
}

async fn get_user_hateoas(
    Path(id): Path<u64>,
    State(state): State<AppState>
) -> Result<Json<ApiResponse<UserWithLinks>>, (StatusCode, Json<ApiResponse<()>>) > {
    let users = state.users.lock().unwrap();
    
    if let Some(user) = users.get(&id) {
        // Create hypermedia links
        let links = vec![
            Link {
                href: format!("/api/users/{}", id),
                rel: "self".to_string(),
                method: "GET".to_string(),
            },
            Link {
                href: format!("/api/users/{}", id),
                rel: "update".to_string(),
                method: "PUT".to_string(),
            },
            Link {
                href: format!("/api/users/{}", id),
                rel: "delete".to_string(),
                method: "DELETE".to_string(),
            },
            Link {
                href: "/api/users".to_string(),
                rel: "collection".to_string(),
                method: "GET".to_string(),
            },
        ];
        
        let user_with_links = UserWithLinks {
            user: user.clone(),
            links,
        };
        
        Ok(api_response(true, Some(user_with_links), None))
    } else {
        Err((
            StatusCode::NOT_FOUND,
            api_response(false, None, Some(format!("User with ID {} not found", id))),
        ))
    }
}
```

## API Documentation with OpenAPI

Document your API using OpenAPI/Swagger for better client integration:

```rust
use utoipa::{OpenApi, Component, Path, ToSchema};
use utoipa_swagger_ui::SwaggerUi;

// OpenAPI schema definitions
#[derive(OpenApi)]
#[openapi(
    info(
        title = "User Management API",
        version = "1.0.0",
        description = "API for managing users",
        contact(
            name = "API Support",
            email = "<EMAIL>"
        ),
    ),
    servers(
        (url = "http://localhost:3000", description = "Development server"),
        (url = "https://api.example.com", description = "Production server"),
    ),
    paths(
        get_users,
        get_user,
        create_user,
        update_user,
        delete_user
    ),
    components(
        schemas(User, CreateUserRequest, UpdateUserRequest, ApiResponse)
    ),
    tags(
        (name = "users", description = "User management endpoints")
    )
)]
struct ApiDoc;

// Add OpenAPI documentation to handlers
#[utoipa::path(
    get,
    path = "/users",
    tag = "users",
    responses(
        (status = 200, description = "List all users", body = ApiResponse<Vec<User>>)
    )
)]
async fn get_users(/* ... */) { /* ... */ }

#[utoipa::path(
    get,
    path = "/users/{id}",
    tag = "users",
    params(
        ("id" = u64, Path, description = "User ID")
    ),
    responses(
        (status = 200, description = "User found", body = ApiResponse<User>),
        (status = 404, description = "User not found", body = ApiResponse<()>)
    )
)]
async fn get_user(/* ... */) { /* ... */ }

// Configure OpenAPI in the application
let app = Router::new()
    .merge(SwaggerUi::new("/docs").url("/api-docs/openapi.json", ApiDoc::openapi()))
    .nest("/api", create_api_router());
```

## Advanced Response Handling

### 1. Content Negotiation

Support multiple response formats based on client preferences:

```rust
async fn content_negotiation(
    TypedHeader(accept): TypedHeader<headers::Accept>,
    State(state): State<AppState>,
) -> impl IntoResponse {
    let users = state.users.lock().unwrap();
    let user_list: Vec<User> = users.values().cloned().collect();
    
    // Check Accept header for content negotiation
    for mime in accept.iter() {
        match mime.as_ref() {
            "application/json" => {
                return Json(user_list).into_response();
            }
            "application/xml" => {
                // XML serialization (using quick-xml as example)
                let xml = quick_xml::se::to_string(&user_list)
                    .unwrap_or_else(|_| "<error>Failed to serialize XML</error>".to_string());
                
                return (
                    [(header::CONTENT_TYPE, "application/xml")],
                    xml
                ).into_response();
            }
            "text/csv" => {
                // CSV serialization
                let mut csv = String::from("id,name,email\n");
                for user in &user_list {
                    csv.push_str(&format!("{},{},{}\n", user.id, user.name, user.email));
                }
                
                return (
                    [(header::CONTENT_TYPE, "text/csv")],
                    csv
                ).into_response();
            }
            _ => continue,
        }
    }
    
    // Default to JSON if no acceptable format found
    Json(user_list).into_response()
}
```

### 2. HTTP Status Codes and Error Handling

Implement consistent error handling and status code usage:

```rust
// Custom error types for API
enum ApiError {
    NotFound(String),
    BadRequest(String),
    Unauthorized(String),
    Forbidden(String),
    InternalError(String),
}

impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        let (status, message) = match self {
            ApiError::NotFound(msg) => (StatusCode::NOT_FOUND, msg),
            ApiError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            ApiError::Unauthorized(msg) => (StatusCode::UNAUTHORIZED, msg),
            ApiError::Forbidden(msg) => (StatusCode::FORBIDDEN, msg),
            ApiError::InternalError(msg) => {
                // Log internal errors
                log::error!("Internal server error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error".to_string())
            }
        };
        
        // Create consistent error response
        let body = Json(ApiResponse {
            success: false,
            data: None::<()>,
            message: Some(message),
        });
        
        (status, body).into_response()
    }
}

// Example handler with error handling
async fn delete_user_with_errors(
    Path(id): Path<u64>,
    State(state): State<AppState>
) -> Result<impl IntoResponse, ApiError> {
    // Check authorization (example)
    if !is_admin() {
        return Err(ApiError::Forbidden("Only admins can delete users".to_string()));
    }
    
    let mut users = state.users.lock().unwrap();
    
    if users.remove(&id).is_some() {
        Ok(api_response(true, Option::<()>::None, Some("User deleted successfully".to_string())))
    } else {
        Err(ApiError::NotFound(format!("User with ID {} not found", id)))
    }
}
```

### 3. Conditional Requests (ETags, If-Modified-Since)

Implement resource caching with conditional requests:

```rust
use chrono::{DateTime, Utc};
use sha2::{Sha256, Digest};

// Add cache metadata to User
#[derive(Debug, Clone, Serialize, Deserialize)]
struct UserWithCache {
    #[serde(flatten)]
    user: User,
    #[serde(skip_serializing)]
    last_modified: DateTime<Utc>,
    #[serde(skip_serializing)]
    etag: String,
}

impl UserWithCache {
    fn from_user(user: User) -> Self {
        let last_modified = Utc::now();
        
        // Create ETag based on user data and modification time
        let mut hasher = Sha256::new();
        hasher.update(format!("{}:{}:{}:{}", 
            user.id, 
            user.name, 
            user.email,
            last_modified.timestamp()
        ));
        let etag = format!("\"{}\"", hex::encode(&hasher.finalize()[..16]));
        
        Self {
            user,
            last_modified,
            etag,
        }
    }
}

async fn get_user_with_caching(
    Path(id): Path<u64>,
    TypedHeader(if_none_match): Option<TypedHeader<headers::IfNoneMatch>>,
    TypedHeader(if_modified_since): Option<TypedHeader<headers::IfModifiedSince>>,
    State(state): State<AppState>
) -> Result<impl IntoResponse, ApiError> {
    let users = state.users.lock().unwrap();
    
    match users.get(&id).map(|user| UserWithCache::from_user(user.clone())) {
        Some(user_with_cache) => {
            // Check ETag for conditional request
            if let Some(TypedHeader(if_none_match)) = if_none_match {
                if if_none_match.matched(&user_with_cache.etag) {
                    return Ok(StatusCode::NOT_MODIFIED.into_response());
                }
            }
            
            // Check If-Modified-Since
            if let Some(TypedHeader(if_modified_since)) = if_modified_since {
                if user_with_cache.last_modified <= if_modified_since.into() {
                    return Ok(StatusCode::NOT_MODIFIED.into_response());
                }
            }
            
            // Return full response with cache headers
            let mut response = Json(api_response(true, Some(user_with_cache.user), None)).into_response();
            
            response.headers_mut().insert(
                header::ETAG,
                HeaderValue::from_str(&user_with_cache.etag).unwrap()
            );
            
            response.headers_mut().insert(
                header::LAST_MODIFIED,
                HeaderValue::from_str(&user_with_cache.last_modified.to_rfc2822()).unwrap()
            );
            
            Ok(response)
        },
        None => Err(ApiError::NotFound(format!("User with ID {} not found", id))),
    }
}
```

## API Testing and Monitoring

### 1. API Testing with `reqwest`

Test your API endpoints:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use reqwest::{Client, StatusCode};
    use serde_json::json;
    use tokio::net::TcpListener;

    async fn spawn_app() -> String {
        // Create random port for test
        let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
        let addr = listener.local_addr().unwrap();
        
        // Initialize app state
        let state = AppState {
            users: Arc::new(Mutex::new(HashMap::new())),
            next_id: Arc::new(Mutex::new(1)),
        };
        
        // Start server
        let app = create_api_router().with_state(state);
        let server = axum::Server::from_tcp(listener.into_std().unwrap())
            .unwrap()
            .serve(app.into_make_service());
            
        tokio::spawn(server);
        
        format!("http://{}", addr)
    }
    
    #[tokio::test]
    async fn test_create_user() {
        // Arrange
        let app_url = spawn_app().await;
        let client = Client::new();
        
        // Act
        let response = client.post(&format!("{}/users", app_url))
            .json(&json!({
                "name": "Test User",
                "email": "<EMAIL>"
            }))
            .send()
            .await
            .unwrap();
            
        // Assert
        assert_eq!(response.status(), StatusCode::CREATED);
        
        let body: serde_json::Value = response.json().await.unwrap();
        assert_eq!(body["success"], true);
        assert_eq!(body["data"]["name"], "Test User");
        assert_eq!(body["data"]["email"], "<EMAIL>");
        assert!(body["data"]["id"].is_number());
    }
    
    #[tokio::test]
    async fn test_get_nonexistent_user() {
        // Arrange
        let app_url = spawn_app().await;
        let client = Client::new();
        
        // Act
        let response = client.get(&format!("{}/users/999", app_url))
            .send()
            .await
            .unwrap();
            
        // Assert
        assert_eq!(response.status(), StatusCode::NOT_FOUND);
        
        let body: serde_json::Value = response.json().await.unwrap();
        assert_eq!(body["success"], false);
        assert_eq!(body["data"], serde_json::Value::Null);
        assert!(body["message"].as_str().unwrap().contains("not found"));
    }
}
```

### 2. API Documentation with Inline Examples

Include examples in your OpenAPI documentation:

```rust
#[utoipa::path(
    post,
    path = "/users",
    tag = "users",
    request_body = CreateUserRequest,
    responses(
        (status = 201, description = "User created successfully", body = ApiResponse<User>),
        (status = 400, description = "Invalid request", body = ApiResponse<()>)
    ),
    request_body_example(
        example = json!({
            "name": "John Doe",
            "email": "<EMAIL>"
        })
    ),
    response_example(
        status = 201,
        example = json!({
            "success": true,
            "data": {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>"
            },
            "message": "User created successfully"
        })
    )
)]
async fn create_user(/* ... */) { /* ... */ }
```

## Best Practices for API Design

### 1. Resource Naming Conventions

- Use plural nouns for collection resources: `/users`, `/articles`
- Use consistent case (usually kebab-case): `/blog-posts`
- Express relationships through nested resources: `/users/{id}/orders`
- Keep URIs simple and predictable

```rust
// Good API structure
let app = Router::new()
    .route("/users", get(list_users).post(create_user))
    .route("/users/:id", get(get_user).put(update_user).delete(delete_user))
    .route("/users/:user_id/posts", get(list_user_posts).post(create_user_post))
    .route("/users/:user_id/posts/:post_id", get(get_user_post));
```

### 2. Versioning Strategies

Choose a consistent versioning strategy and stick with it:

```rust
// Option 1: URL Path versioning
let api = Router::new()
    .nest("/v1", api_v1_routes())
    .nest("/v2", api_v2_routes());

// Option 2: Header versioning
// Custom extractor for API version
struct ApiVersion(String);

#[async_trait]
impl<B> FromRequest<B> for ApiVersion
where
    B: Send,
{
    type Rejection = StatusCode;

    async fn from_request(req: &mut RequestParts<B>) -> Result<Self, Self::Rejection> {
        // Check for version in Accept header
        if let Some(accept) = req.headers().get("Accept") {
            if let Ok(accept_str) = accept.to_str() {
                if let Some(version) = parse_version_from_accept(accept_str) {
                    return Ok(ApiVersion(version.to_string()));
                }
            }
        }
        
        // Check for explicit version header
        if let Some(version) = req.headers().get("API-Version") {
            if let Ok(version_str) = version.to_str() {
                return Ok(ApiVersion(version_str.to_string()));
            }
        }
        
        // Default to version 1
        Ok(ApiVersion("1".to_string()))
    }
}

async fn versioned_handler(
    ApiVersion(version): ApiVersion,
    // other parameters...
) -> impl IntoResponse {
    match version.as_str() {
        "1" => v1_handler().await,
        "2" => v2_handler().await,
        _ => (StatusCode::NOT_IMPLEMENTED, "API version not supported").into_response(),
    }
}
```

### 3. Consistent Error Handling

Use a consistent error format across your API:

```rust
// Example error response format
#[derive(Serialize, ToSchema)]
struct ErrorResponse {
    status: String,
    code: u16,
    message: String,
    details: Option<serde_json::Value>,
    #[schema(example = "2023-05-25T14:30:00Z")]
    timestamp: String,
}

impl ErrorResponse {
    fn new(status_code: StatusCode, message: impl ToString, details: Option<serde_json::Value>) -> Self {
        Self {
            status: "error".to_string(),
            code: status_code.as_u16(),
            message: message.to_string(),
            details,
            timestamp: chrono::Utc::now().to_rfc3339(),
        }
    }
    
    fn into_response(self, status: StatusCode) -> Response {
        (status, Json(self)).into_response()
    }
}

// Create application error handler
fn handle_application_error(error: AppError) -> Response {
    match error {
        AppError::Validation(errors) => {
            let details = serde_json::to_value(&errors).ok();
            ErrorResponse::new(
                StatusCode::BAD_REQUEST,
                "Validation failed",
                details
            ).into_response(StatusCode::BAD_REQUEST)
        },
        AppError::Authentication => {
            ErrorResponse::new(
                StatusCode::UNAUTHORIZED,
                "Authentication required",
                None
            ).into_response(StatusCode::UNAUTHORIZED)
        },
        // Other error types...
    }
}
```

### 4. Respect HTTP Methods

Use HTTP methods appropriately for different operations:

- `GET`: Read/retrieve resources (safe, idempotent)
- `POST`: Create new resources or trigger operations
- `PUT`: Replace/update entire resources (idempotent)
- `PATCH`: Partial update of resources
- `DELETE`: Remove resources (idempotent)
- `HEAD`: Get headers only (like GET without body)
- `OPTIONS`: Get supported methods for a resource

### 5. Rate Limiting and Throttling

Implement rate limiting for API stability:

```rust
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tower::Service;
use tower_http::limit::RateLimitLayer;

// Simple rate limiter based on client IP
#[derive(Clone)]
struct IpRateLimiter {
    // Maps IP address to (request count, window start time)
    limits: Arc<Mutex<HashMap<String, (u32, Instant)>>>,
    max_requests: u32,
    window_duration: Duration,
}

impl IpRateLimiter {
    fn new(max_requests: u32, window_duration: Duration) -> Self {
        Self {
            limits: Arc::new(Mutex::new(HashMap::new())),
            max_requests,
            window_duration,
        }
    }
    
    async fn check_rate_limit(&self, ip: &str) -> bool {
        let mut limits = self.limits.lock().await;
        let now = Instant::now();
        
        // Get or create rate limit entry for IP
        let entry = limits.entry(ip.to_string()).or_insert((0, now));
        
        // Reset count if window expired
        if now.duration_since(entry.1) >= self.window_duration {
            *entry = (1, now);
            return true;
        }
        
        // Increment count and check limit
        entry.0 += 1;
        entry.0 <= self.max_requests
    }
}

// Rate limit middleware
async fn rate_limit_middleware<B>(
    req: Request<B>,
    limiter: &IpRateLimiter,
) -> Result<Request<B>, Response> {
    // Get client IP (simplified - use a proper IP extraction in production)
    let ip = req
        .headers()
        .get("X-Forwarded-For")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown");
    
    // Check rate limit
    if limiter.check_rate_limit(ip).await {
        Ok(req)
    } else {
        let response = ErrorResponse::new(
            StatusCode::TOO_MANY_REQUESTS,
            "Rate limit exceeded. Try again later.",
            None
        ).into_response(StatusCode::TOO_MANY_REQUESTS);
        
        Err(response)
    }
}
```

## Security Considerations

APIs often serve as the primary entry point to your application's data and functionality, making them a prime target for attackers. This section outlines critical security measures and patterns for designing secure APIs in Rust.

### Threat Model for API Security

Understanding the threat landscape is crucial for implementing appropriate security controls:

```mermaid
flowchart TD
    A[Attacker] -->|1. Authentication bypass| API[API Endpoints]
    A -->|2. Authorization flaws| API
    A -->|3. Injection attacks| API
    A -->|4. Excessive data exposure| API
    A -->|5. Rate limiting bypass| API
    A -->|6. MITM attacks| API
    A -->|7. API parameter tampering| API
    
    API -->|Authorized access| D[(Database)]
    API -->|Authorized access| S[Services]
    API -->|Authorized access| FS[File System]
    
    class A fill:#f96,stroke:#333
    class API fill:#69f,stroke:#333
    class D,S,FS fill:#6d9,stroke:#333
```

### Secure Authentication Implementation

Implement robust API authentication and session management:

```rust
use axum::{
    http::StatusCode,
    extract::{TypedHeader, State},
    headers::{Authorization, authorization::Bearer},
    RequestPartsExt, Json,
};
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,
    exp: usize,
    iat: usize,
    roles: Vec<String>,
}

struct AuthenticatedUser {
    id: String,
    roles: Vec<String>,
}

// JWT Authentication middleware
async fn authenticate<B>(
    mut req: Request<B>,
    jwt_secret: &[u8],
    next: Next<B>,
) -> Result<Response, StatusCode> {
    // Get JWT from Authorization header
    let auth_header = req
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
        .and_then(|header| header.strip_prefix("Bearer "));
    
    let token = match auth_header {
        Some(token) => token,
        None => return Err(StatusCode::UNAUTHORIZED),
    };
    
    // Validate JWT
    let token_data = match decode::<Claims>(
        token,
        &DecodingKey::from_secret(jwt_secret),
        &Validation::new(Algorithm::HS256),
    ) {
        Ok(data) => data,
        Err(e) => {
            log::warn!("JWT validation failed: {}", e);
            return Err(StatusCode::UNAUTHORIZED);
        }
    };
    
    // Check if token is expired
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() as usize;
        
    if token_data.claims.exp < now {
        log::warn!("Expired JWT token from user {}", token_data.claims.sub);
        return Err(StatusCode::UNAUTHORIZED);
    }
    
    // Add user info to request extensions
    let user = AuthenticatedUser {
        id: token_data.claims.sub.clone(),
        roles: token_data.claims.roles.clone(),
    };
    
    req.extensions_mut().insert(user);
    
    // Continue to handler
    Ok(next.run(req).await)
}

// Secure login handler with rate limiting
async fn login(
    State(state): State<AppState>,
    Json(credentials): Json<LoginCredentials>,
) -> Result<impl IntoResponse, StatusCode> {
    // Rate limiting - check for repeated failed logins
    if state.login_attempt_tracker.is_rate_limited(&credentials.username).await {
        log::warn!("Rate limit exceeded for login attempts: {}", credentials.username);
        return Err(StatusCode::TOO_MANY_REQUESTS);
    }
    
    // Validate credentials (example)
    match validate_credentials(&credentials.username, &credentials.password).await {
        Ok(user) => {
            // Create JWT token
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs() as usize;
                
            let claims = Claims {
                sub: user.id.to_string(),
                iat: now,
                exp: now + 3600, // 1 hour expiration
                roles: user.roles,
            };
            
            let token = jsonwebtoken::encode(
                &Header::new(Algorithm::HS256),
                &claims,
                &EncodingKey::from_secret(state.jwt_secret.as_bytes()),
            ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
            
            // Reset failed login attempts counter
            state.login_attempt_tracker.reset_attempts(&credentials.username).await;
            
            // Return token
            Ok(Json(json!({
                "access_token": token,
                "token_type": "Bearer",
                "expires_in": 3600,
            })))
        }
        Err(_) => {
            // Record failed login attempt
            state.login_attempt_tracker.record_failed_attempt(&credentials.username).await;
            
            // Always use constant time for comparison to prevent timing attacks
            tokio::time::sleep(Duration::from_millis(100)).await;
            
            Err(StatusCode::UNAUTHORIZED)
        }
    }
}

// Authorization middleware - role-based access control
async fn require_role<B>(
    req: Request<B>,
    required_role: &str,
    next: Next<B>,
) -> Result<Response, StatusCode> {
    // Get user from request extensions (set by authentication middleware)
    let user = req
        .extensions()
        .get::<AuthenticatedUser>()
        .ok_or(StatusCode::UNAUTHORIZED)?;
    
    // Check if user has required role
    if user.roles.contains(&required_role.to_string()) {
        Ok(next.run(req).await)
    } else {
        log::warn!(
            "Authorization failure: User {} missing role {}", 
            user.id, required_role
        );
        Err(StatusCode::FORBIDDEN)
    }
}
```

### Input Validation and Injection Prevention

Implement thorough validation to prevent injection attacks:

```rust
use validator::{Validate, ValidationError};
use regex::Regex;
use serde::{Deserialize, Serialize};
use axum::Json;

// Define a strongly typed request model with validation
#[derive(Debug, Deserialize, Serialize, Validate)]
struct CreateUserRequest {
    #[validate(length(min = 3, max = 50, message = "Name must be between 3-50 characters"))]
    name: String,
    
    #[validate(email(message = "Invalid email format"))]
    email: String,
    
    #[validate(length(min = 8, message = "Password must be at least 8 characters"),
               custom = "validate_password_strength")]
    password: String,
    
    #[validate(range(min = 1, max = 120, message = "Age must be between 1-120"))]
    age: Option<u8>,
    
    #[validate(url(message = "Invalid URL format"))]
    website: Option<String>,
    
    #[validate]
    address: Option<Address>,
}

// Nested validation
#[derive(Debug, Deserialize, Serialize, Validate)]
struct Address {
    #[validate(length(min = 1, max = 100, message = "Street address is required"))]
    street: String,
    
    #[validate(length(min = 1, max = 100, message = "City is required"))]
    city: String,
    
    #[validate(regex(path = "ZIP_CODE_REGEX", message = "Invalid ZIP code format"))]
    zip_code: String,
    
    #[validate(length(min = 2, max = 2, message = "Country code must be 2 characters"))]
    country_code: String,
}

// Custom password strength validation
fn validate_password_strength(password: &str) -> Result<(), ValidationError> {
    // Define password requirements
    let has_uppercase = Regex::new(r"[A-Z]").unwrap().is_match(password);
    let has_lowercase = Regex::new(r"[a-z]").unwrap().is_match(password);
    let has_digit = Regex::new(r"[0-9]").unwrap().is_match(password);
    let has_special = Regex::new(r"[^A-Za-z0-9]").unwrap().is_match(password);
    
    if has_uppercase && has_lowercase && has_digit && has_special {
        Ok(())
    } else {
        let mut error = ValidationError::new("weak_password");
        error.message = Some("Password must include uppercase, lowercase, number, and special character".into());
        Err(error)
    }
}

// Compile regex once
lazy_static! {
    static ref ZIP_CODE_REGEX: Regex = Regex::new(r"^\d{5}(-\d{4})?$").unwrap();
}

// Validation middleware for request bodies
async fn validate_request_body<T>(
    Json(payload): Json<T>,
) -> Result<T, ApiError>
where
    T: Validate,
{
    match payload.validate() {
        Ok(_) => Ok(payload),
        Err(validation_errors) => {
            // Convert validation errors to API error response
            let error_messages = validation_errors
                .field_errors()
                .iter()
                .map(|(field, errors)| {
                    let messages: Vec<&str> = errors
                        .iter()
                        .filter_map(|e| e.message.as_ref().map(|m| m.as_ref()))
                        .collect();
                    (field.to_string(), messages)
                })
                .collect::<HashMap<String, Vec<&str>>>();
                
            Err(ApiError::ValidationError(error_messages))
        }
    }
}

// Example handler with validation
async fn create_user_handler(
    validated: Result<CreateUserRequest, ApiError>,
    State(state): State<AppState>,
) -> impl IntoResponse {
    // Extract validated data or return validation error
    let user_data = match validated {
        Ok(data) => data,
        Err(e) => return e.into_response(),
    };
    
    // Additional security checks - SQL injection prevention
    if contains_sql_injection_patterns(&user_data.name) || 
       contains_sql_injection_patterns(&user_data.email) {
        return ApiError::SecurityViolation("Potential SQL injection detected".into())
            .into_response();
    }
    
    // Sanitize inputs for XSS prevention before storing
    let sanitized_name = sanitize_html(&user_data.name);
    
    // Process validated and sanitized data
    // ...
}

// SQL injection pattern detection
fn contains_sql_injection_patterns(input: &str) -> bool {
    let sql_patterns = [
        "SELECT", "INSERT", "UPDATE", "DELETE", "DROP",
        "UNION", "--", "/*", "*/", "';", "EXEC", "EXECUTE"
    ];
    
    let input_upper = input.to_uppercase();
    sql_patterns.iter().any(|pattern| input_upper.contains(*pattern))
}

// XSS prevention with HTML sanitization
fn sanitize_html(input: &str) -> String {
    // Use an HTML sanitization library like ammonia
    ammonia::clean(input)
}
```

### Security Headers and Response Hardening

Implement security headers and response protections:

```rust
use axum::{middleware::map_response, response::Response};
use http::HeaderMap;

// Security headers middleware
async fn add_security_headers<B>(response: Response<B>) -> Response<B> {
    // Create response builder with existing parts
    let (parts, body) = response.into_parts();
    let mut new_response = Response::from_parts(parts, body);
    
    // Add security headers
    let headers = new_response.headers_mut();
    
    // Prevent browsers from performing MIME sniffing
    headers.insert(
        "X-Content-Type-Options",
        HeaderValue::from_static("nosniff"),
    );
    
    // Strict Transport Security to enforce HTTPS
    headers.insert(
        "Strict-Transport-Security",
        HeaderValue::from_static("max-age=31536000; includeSubDomains; preload"),
    );
    
    // Prevent iframe embedding to avoid clickjacking
    headers.insert(
        "X-Frame-Options",
        HeaderValue::from_static("DENY"),
    );
    
    // Enable browser XSS protection
    headers.insert(
        "X-XSS-Protection",
        HeaderValue::from_static("1; mode=block"),
    );
    
    // Content Security Policy to mitigate XSS and other attacks
    headers.insert(
        "Content-Security-Policy",
        HeaderValue::from_static(
            "default-src 'self'; script-src 'self'; object-src 'none'; \
             style-src 'self'; img-src 'self'; frame-ancestors 'none'; \
             base-uri 'self'; form-action 'self'"
        ),
    );
    
    // Permissions policy to limit browser features
    headers.insert(
        "Permissions-Policy",
        HeaderValue::from_static(
            "camera=(), microphone=(), geolocation=()"
        ),
    );
    
    // Add Referrer-Policy to control information in the Referer header
    headers.insert(
        "Referrer-Policy",
        HeaderValue::from_static("strict-origin-when-cross-origin"),
    );
    
    new_response
}

// Use the security headers middleware in your application
let app = Router::new()
    .route("/api/v1/users", post(create_user))
    .layer(middleware::from_fn(add_security_headers));
```

### CORS (Cross-Origin Resource Sharing) Configuration

Implement secure CORS policies:

```rust
use tower_http::cors::{Cors, CorsLayer};

// Create a secure CORS configuration
fn create_cors_layer() -> CorsLayer {
    // Allow only specific origins in production
    let allowed_origins = if cfg!(debug_assertions) {
        vec!["http://localhost:3000".parse::<HeaderValue>().unwrap()]
    } else {
        vec![
            "https://app.example.com".parse::<HeaderValue>().unwrap(),
            "https://admin.example.com".parse::<HeaderValue>().unwrap(),
        ]
    };
    
    CorsLayer::new()
        .allow_origins(allowed_origins)
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::OPTIONS,
        ])
        .allow_headers([
            header::AUTHORIZATION,
            header::ACCEPT,
            header::CONTENT_TYPE,
        ])
        .allow_credentials(true)
        .max_age(Duration::from_secs(3600))
}

// Apply CORS to the application
let app = Router::new()
    .route("/api/users", get(get_users).post(create_user))
    .layer(create_cors_layer());
```

### Rate Limiting and Brute Force Protection

Implement advanced rate limiting to prevent abuse:

```rust
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use async_trait::async_trait;

// Rate limiter with token bucket algorithm
struct TokenBucketRateLimiter {
    // Maps key (IP or user ID) to (tokens, last refill time)
    buckets: Arc<RwLock<HashMap<String, (f64, Instant)>>>,
    capacity: f64,         // Maximum tokens
    refill_rate: f64,      // Tokens added per second
    refill_interval: Duration, // Minimum time between refills
}

impl TokenBucketRateLimiter {
    fn new(capacity: f64, refill_rate: f64) -> Self {
        Self {
            buckets: Arc::new(RwLock::new(HashMap::new())),
            capacity,
            refill_rate,
            refill_interval: Duration::from_millis(100), // 100ms minimum between refills
        }
    }
    
    async fn check_and_decrement(&self, key: &str, cost: f64) -> bool {
        let now = Instant::now();
        let mut buckets = self.buckets.write().await;
        
        // Get or create bucket for this key
        let bucket = buckets
            .entry(key.to_string())
            .or_insert_with(|| (self.capacity, now));
        
        // Calculate time since last refill
        let elapsed = now.duration_since(bucket.1).as_secs_f64();
        
        // Refill tokens based on elapsed time
        if elapsed > 0.0 {
            let new_tokens = elapsed * self.refill_rate;
            bucket.0 = (bucket.0 + new_tokens).min(self.capacity);
            bucket.1 = now;
        }
        
        // Check if we have enough tokens and decrement
        if bucket.0 >= cost {
            bucket.0 -= cost;
            true
        } else {
            false
        }
    }
}

// IP-based rate limiter middleware
async fn ip_rate_limit<B>(
    req: Request<B>,
    limiter: &TokenBucketRateLimiter,
    next: Next<B>,
) -> Result<Response, StatusCode> {
    // Get client IP
    let ip = get_client_ip(&req).unwrap_or("unknown".to_string());
    
    // Adjust cost based on endpoint sensitivity
    let path = req.uri().path();
    let cost = if path.contains("/login") || path.contains("/register") {
        // Higher cost for authentication endpoints
        2.0
    } else {
        // Standard cost for other endpoints
        1.0
    };
    
    // Check rate limit
    if limiter.check_and_decrement(&ip, cost).await {
        // Within limit, proceed with request
        Ok(next.run(req).await)
    } else {
        // Rate limit exceeded
        log::warn!("Rate limit exceeded for IP: {}", ip);
        
        Err(StatusCode::TOO_MANY_REQUESTS)
    }
}

// Per-user API request limiter
struct UserRateLimiter {
    // General limiter for all users
    global_limiter: TokenBucketRateLimiter,
    // Stricter limiters for specific endpoints
    sensitive_endpoints: HashMap<String, TokenBucketRateLimiter>,
}

impl UserRateLimiter {
    fn new() -> Self {
        let mut sensitive_endpoints = HashMap::new();
        
        // Stricter limits for sensitive operations
        sensitive_endpoints.insert(
            "/api/users".to_string(),
            TokenBucketRateLimiter::new(20.0, 0.2) // 20 reqs max, 0.2/sec refill
        );
        
        sensitive_endpoints.insert(
            "/api/admin".to_string(),
            TokenBucketRateLimiter::new(10.0, 0.1) // 10 reqs max, 0.1/sec refill
        );
        
        Self {
            global_limiter: TokenBucketRateLimiter::new(100.0, 1.0), // 100 reqs max, 1/sec refill
            sensitive_endpoints,
        }
    }
    
    async fn check_limit(&self, user_id: &str, path: &str) -> bool {
        // First check endpoint-specific limits
        for (endpoint, limiter) in &self.sensitive_endpoints {
            if path.starts_with(endpoint) {
                if !limiter.check_and_decrement(&format!("user:{}", user_id), 1.0).await {
                    return false;
                }
                break;
            }
        }
        
        // Then check global limit
        self.global_limiter.check_and_decrement(&format!("user:{}", user_id), 1.0).await
    }
}
```

### Secure API Keys and Secrets Management

Implement secure API key validation and management:

```rust
use argon2::{self, Config};
use rand::Rng;
use base64::{encode, decode};
use std::time::{SystemTime, UNIX_EPOCH};

// API key generation and validation
struct ApiKeyManager {
    db: Arc<DatabaseConnection>,
    argon_config: Config<'static>,
}

impl ApiKeyManager {
    fn new(db: Arc<DatabaseConnection>) -> Self {
        // Configure Argon2id for hashing API keys
        let argon_config = Config {
            variant: argon2::Variant::Argon2id,
            version: argon2::Version::Version13,
            mem_cost: 65536, // 64MB
            time_cost: 10,   // 10 iterations
            lanes: 4,        // Parallelism factor
            ..Default::default()
        };
        
        Self { db, argon_config }
    }
    
    // Generate a new API key with prefix for identifying the key type
    async fn generate_key(&self, user_id: &str, key_type: &str) -> Result<String, Error> {
        let mut rng = rand::thread_rng();
        
        // Generate 32 random bytes
        let mut random_bytes = [0u8; 32];
        rng.fill(&mut random_bytes);
        
        // Get current timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        // Create encoded key parts
        let key_id = uuid::Uuid::new_v4().to_string().replace("-", "")[..8].to_string();
        let encoded_bytes = encode(&random_bytes);
        
        // Format as: prefix_keyid_encodedsecret
        let api_key = format!("{}_{}_{}",
            key_type,
            key_id,
            encoded_bytes
        );
        
        // Hash the API key for storage
        let salt = generate_salt();
        let key_hash = argon2::hash_encoded(
            api_key.as_bytes(),
            &salt,
            &self.argon_config
        )?;
        
        // Store in database with metadata
        self.db.execute(
            "INSERT INTO api_keys (user_id, key_id, key_hash, key_type, created_at) VALUES (?, ?, ?, ?, ?)",
            &[user_id, &key_id, &key_hash, key_type, &timestamp]
        ).await?;
        
        Ok(api_key)
    }
    
    // Validate an API key
    async fn validate_key(&self, api_key: &str) -> Result<ApiKeyInfo, Error> {
        // Parse key format
        let parts: Vec<&str> = api_key.split('_').collect();
        if parts.len() != 3 {
            return Err(Error::InvalidApiKey);
        }
        
        let key_type = parts[0];
        let key_id = parts[1];
        
        // Look up key in database
        let result = self.db.query_one(
            "SELECT user_id, key_hash, key_type, created_at FROM api_keys WHERE key_id = ? AND revoked = 0",
            &[key_id]
        ).await?;
        
        // Get stored hash
        let stored_hash: String = result.get("key_hash")?;
        
        // Verify API key against stored hash
        if argon2::verify_encoded(&stored_hash, api_key.as_bytes())? {
            // Key is valid, extract metadata
            let user_id: String = result.get("user_id")?;
            let key_type_db: String = result.get("key_type")?;
            let created_at: i64 = result.get("created_at")?;
            
            // Ensure key type matches to prevent key reuse across services
            if key_type != key_type_db {
                return Err(Error::InvalidApiKey);
            }
            
            Ok(ApiKeyInfo {
                user_id,
                key_type: key_type.to_string(),
                created_at: created_at as u64,
            })
        } else {
            Err(Error::InvalidApiKey)
        }
    }
    
    // Revoke an API key
    async fn revoke_key(&self, key_id: &str, user_id: &str) -> Result<bool, Error> {
        let rows_affected = self.db.execute(
            "UPDATE api_keys SET revoked = 1, revoked_at = ? WHERE key_id = ? AND user_id = ?",
            &[SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(), key_id, user_id]
        ).await?;
        
        Ok(rows_affected > 0)
    }
}

// API key middleware
async fn api_key_auth<B>(
    req: Request<B>,
    key_manager: &ApiKeyManager,
    next: Next<B>,
) -> Result<Response, StatusCode> {
    // Extract API key from header
    let api_key = req
        .headers()
        .get("X-API-Key")
        .and_then(|h| h.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;
    
    // Validate API key
    match key_manager.validate_key(api_key).await {
        Ok(key_info) => {
            // Add user info to request extensions
            req.extensions_mut().insert(key_info);
            Ok(next.run(req).await)
        }
        Err(_) => {
            Err(StatusCode::UNAUTHORIZED)
        }
    }
}
```

### API Security Logging and Monitoring

Implement comprehensive security logging for API activities:

```rust
use log::{info, warn, error};
use serde_json::json;
use uuid::Uuid;
use std::time::Instant;

// Security logging middleware
async fn security_logging<B>(
    req: Request<B>,
    next: Next<B>,
) -> Response {
    let start = Instant::now();
    let request_id = Uuid::new_v4().to_string();
    
    // Extract security relevant information
    let method = req.method().clone();
    let path = req.uri().path().to_owned();
    let client_ip = get_client_ip(&req).unwrap_or_else(|| "unknown".to_string());
    
    // Extract authentication information if available
    let auth_info = req
        .extensions()
        .get::<AuthenticatedUser>()
        .map(|u| json!({ "user_id": u.id, "roles": u.roles }))
        .unwrap_or_else(|| json!({ "authenticated": false }));
    
    // Log request start
    info!(
        "API Request: {} {} | client: {} | request_id: {} | auth: {}",
        method, path, client_ip, request_id, auth_info
    );
    
    // Process the request
    let mut response = next.run(req).await;
    
    // Calculate request duration
    let duration = start.elapsed();
    
    // Add request ID to response headers for correlation
    response.headers_mut().insert(
        "X-Request-ID",
        HeaderValue::from_str(&request_id).unwrap(),
    );
    
    // Get response status
    let status = response.status();
    
    // Log based on status code
    match status.as_u16() {
        200..=299 => {
            info!(
                "API Response: {} | {} {} | client: {} | request_id: {} | duration: {:?}",
                status.as_u16(), method, path, client_ip, request_id, duration
            );
        }
        400..=499 => {
            warn!(
                "API Client Error: {} | {} {} | client: {} | request_id: {} | duration: {:?} | auth: {}",
                status.as_u16(), method, path, client_ip, request_id, duration, auth_info
            );
        }
        _ => {
            error!(
                "API Server Error: {} | {} {} | client: {} | request_id: {} | duration: {:?}",
                status.as_u16(), method, path, client_ip, request_id, duration
            );
        }
    }
    
    // Return the response
    response
}

// Enhanced API security monitoring with suspicious activity detection
struct ApiSecurityMonitor {
    db: Arc<DatabaseConnection>,
}

impl ApiSecurityMonitor {
    // Log suspicious API activity
    async fn log_suspicious_activity(&self, 
                                   activity_type: &str,
                                   user_id: Option<&str>,
                                   client_ip: &str,
                                   details: &serde_json::Value) {
        // Log to database for analysis
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        let _ = self.db.execute(
            "INSERT INTO security_events (event_type, user_id, client_ip, details, timestamp) 
             VALUES (?, ?, ?, ?, ?)",
            &[
                activity_type,
                &user_id.unwrap_or("anonymous"),
                client_ip,
                &details.to_string(),
                &(timestamp as i64)
            ]
        ).await;
        
        // Log high severity events
        match activity_type {
            "auth_brute_force" | "api_abuse" | "data_exfiltration" => {
                error!(
                    "SECURITY ALERT: {} from IP: {} user: {} details: {}",
                    activity_type, 
                    client_ip,
                    user_id.unwrap_or("anonymous"),
                    details
                );
                
                // TODO: Send real-time alert to security team
            }
            _ => {
                warn!(
                    "Security event: {} from IP: {} user: {} details: {}",
                    activity_type, 
                    client_ip,
                    user_id.unwrap_or("anonymous"),
                    details
                );
            }
        }
    }
    
    // Detect potential API enumeration (scanning for endpoints)
    async fn check_for_api_enumeration(&self, client_ip: &str) -> bool {
        // Count 404 errors in the last minute
        let count = self.db.query_one(
            "SELECT COUNT(*) as count FROM security_events 
             WHERE client_ip = ? AND event_type = 'not_found' 
             AND timestamp > ?",
            &[
                client_ip,
                &((SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() - 60) as i64)
            ]
        ).await.map(|row| row.get::<_, i64>("count").unwrap_or(0)).unwrap_or(0);
        
        // Threshold for detection
        if count > 10 {
            self.log_suspicious_activity(
                "api_enumeration",
                None,
                client_ip,
                &json!({ "not_found_count": count })
            ).await;
            return true;
        }
        
        false
    }
}
```

### Best Practices for API Security

1. **Use HTTPS-Only Communications**:
   - Encrypt all API communications using HTTPS
   - Implement proper TLS configuration with modern cipher suites
   - Use HTTP Strict Transport Security (HSTS) headers

2. **Implement Proper Authentication**:
   - Use standard authentication methods (OAuth 2.0, JWT)
   - Implement secure token validation and verification
   - Use short-lived tokens with proper expiration
   - Implement refresh token rotation for long-lived sessions

3. **Apply Robust Authorization Controls**:
   - Use role-based or attribute-based access control (RBAC/ABAC)
   - Validate authorization on every request, not just at login
   - Apply principle of least privilege
   - Check object-level permissions for data access

4. **Secure Data Handling**:
   - Implement strict input validation
   - Apply output encoding to prevent XSS
   - Filter sensitive data from responses (PII, secrets)
   - Implement proper error handling to avoid information leakage

5. **Implement Rate Limiting and Throttling**:
   - Apply rate limits per client/user
   - Use token bucket or sliding window algorithms
   - Implement progressive throttling for suspicious clients
   - Monitor for unusual request patterns

6. **API Versioning and Change Management**:
   - Use proper versioning to maintain compatibility
   - Plan for secure API deprecation
   - Communicate security-related changes to clients
   - Maintain backward compatibility without compromising security

7. **Secure Logging and Monitoring**:
   - Log security-relevant events with proper context
   - Avoid logging sensitive data
   - Implement real-time security monitoring
   - Create alerts for suspicious API activity

8. **Use Content Security Headers**:
   - Implement appropriate security headers
   - Configure CORS policies correctly
   - Use Content-Security-Policy headers

9. **Implement API Gateway Security**:
   - Use an API gateway for centralized security enforcement
   - Implement consistent security policies across all APIs
   - Apply traffic filtering and inspection

10. **Regular Security Testing**:
    - Perform regular API security assessments
    - Use automated scanning tools for APIs
    - Implement security-focused code reviews
    - Conduct penetration testing of API endpoints

## Knowledge Check

1. What are the key principles of RESTful API design?
   - Resource-based architecture
   - Using appropriate HTTP methods for operations
   - Statelessness
   - Self-descriptive messages
   - HATEOAS (Hypermedia as the Engine of Application State)

2. Why is API versioning important and what are the common approaches?
   - Versioning allows evolving the API without breaking existing clients
   - Common approaches include: URL path versioning, header-based versioning, and content negotiation

3. What issues does pagination solve in API design?
   - Prevents overwhelming clients with large data sets
   - Reduces server load and response time
   - Improves application performance
   - Provides a better user experience

4. How does content negotiation enhance API usability?
   - Allows clients to request data in their preferred format (JSON, XML, CSV, etc.)
   - Promotes API reuse across different client types
   - Improves backward compatibility

5. Why is consistent error handling important in API design?
   - Provides a better developer experience
   - Makes client-side error handling more predictable
   - Improves API documentation and usability
   - Facilitates debugging and troubleshooting

## Additional Resources

- [REST API Tutorial](https://restfulapi.net/)
- [OpenAPI Specification](https://swagger.io/specification/)
- [HTTP Status Codes](https://httpstatuses.com/)
- [Richardson Maturity Model](https://martinfowler.com/articles/richardsonMaturityModel.html)
- [JSON:API Specification](https://jsonapi.org/)
