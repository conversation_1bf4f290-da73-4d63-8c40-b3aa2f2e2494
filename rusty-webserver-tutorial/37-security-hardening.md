# Security Hardening and Threat Modeling

## Navigation
- [Previous: High-Performance Networking](36-high-performance-networking.md)
- [Next: Advanced Testing and Fuzzing](38-advanced-testing-fuzzing.md)

Harden your webserver and proactively identify threats.

## Theory
- Security hardening: minimize attack surface, use least privilege.
- Threat modeling: identify, prioritize, and mitigate risks.

## Threat Modeling Process

Threat modeling is a structured approach to identifying, quantifying, and addressing security risks. Follow these steps:

1. **Identify assets**: What are you protecting?
2. **Create architecture overview**: How does the system work?
3. **Decompose the application**: Break it into components.
4. **Identify threats**: What could go wrong?
5. **Document threats**: Record findings.
6. **Rate threats**: Prioritize based on risk.

### STRIDE Threat Categories

- **S**poofing: Impersonating something or someone else
- **T**ampering: Modifying data or code
- **R**epudiation: Claiming you didn't do something
- **I**nformation disclosure: Exposing information to unauthorized users
- **D**enial of service: Making the system unavailable
- **E**levation of privilege: Gaining unauthorized capabilities

## Common Web Server Threats and Mitigations

| Threat | Mitigation |
|--------|------------|
| Injection attacks | Input validation, parameterized queries |
| XSS | Output encoding, Content Security Policy |
| CSRF | Anti-CSRF tokens, SameSite cookies |
| Brute force | Rate limiting, account lockout |
| DoS | Rate limiting, resource quotas |
| Data exposure | Encryption, proper access control |
| Path traversal | Validate paths, use safe APIs |
| Dependency vulnerabilities | Regular updates, security scanning |

## Example: mTLS with rustls

```rust
use std::sync::Arc;
use std::fs::File;
use std::io::{self, BufReader};

use rustls::{Certificate, PrivateKey};
use rustls::server::{ServerConfig, ClientCertVerifier};
use rustls_pemfile::{certs, pkcs8_private_keys};

// Load server certificates
fn load_certs(filename: &str) -> io::Result<Vec<Certificate>> {
    let cert_file = File::open(filename)?;
    let mut cert_reader = BufReader::new(cert_file);
    let certs = certs(&mut cert_reader)?;
    Ok(certs.into_iter().map(Certificate).collect())
}

// Load private key
fn load_keys(filename: &str) -> io::Result<Vec<PrivateKey>> {
    let key_file = File::open(filename)?;
    let mut key_reader = BufReader::new(key_file);
    let keys = pkcs8_private_keys(&mut key_reader)?;
    Ok(keys.into_iter().map(PrivateKey).collect())
}

// Set up mTLS
fn configure_mtls() -> Result<ServerConfig, Box<dyn std::error::Error>> {
    // Load server certificates and key
    let certs = load_certs("server.crt")?;
    let mut keys = load_keys("server.key")?;
    
    // Load CA certificates for client verification
    let client_auth_certs = load_certs("ca.crt")?;
    let client_auth_roots = rustls::RootCertStore::empty();
    for cert in client_auth_certs {
        client_auth_roots.add(&cert)?;
    }
    
    // Create verifier that requires client certificates
    let client_verifier = rustls::server::AllowAnyAuthenticatedClient::new(client_auth_roots);
    
    // Configure server
    let config = ServerConfig::builder()
        .with_safe_defaults()
        .with_client_cert_verifier(Arc::new(client_verifier))
        .with_single_cert(certs, keys.remove(0))?;
    
    Ok(config)
}
```

## Security Headers Implementation

```rust
// Add security headers to HTTP responses
fn add_security_headers(headers: &mut HeaderMap) {
    // Prevent browsers from interpreting files as a different MIME type
    headers.insert(CONTENT_TYPE_OPTIONS, HeaderValue::from_static("nosniff"));
    
    // Apply XSS filter
    headers.insert(X_XSS_PROTECTION, HeaderValue::from_static("1; mode=block"));
    
    // Control how much information is included in referrer header
    headers.insert(REFERRER_POLICY, HeaderValue::from_static("strict-origin-when-cross-origin"));
    
    // Control iframe embedding
    headers.insert(X_FRAME_OPTIONS, HeaderValue::from_static("DENY"));
    
    // Content Security Policy
    headers.insert(
        CONTENT_SECURITY_POLICY,
        HeaderValue::from_static(
            "default-src 'self'; script-src 'self'; object-src 'none'; style-src 'self'; img-src 'self'; frame-ancestors 'none'"
        )
    );
    
    // HTTP Strict Transport Security (HSTS)
    headers.insert(
        STRICT_TRANSPORT_SECURITY,
        HeaderValue::from_static("max-age=31536000; includeSubDomains; preload")
    );
}
```

## Secure Cookie Handling

```rust
use cookie::{Cookie, SameSite};

fn create_secure_cookie(name: &str, value: &str) -> Cookie<'static> {
    let mut cookie = Cookie::new(name, value);
    cookie.set_secure(true);        // Only sent over HTTPS
    cookie.set_http_only(true);     // Not accessible via JavaScript
    cookie.set_same_site(SameSite::Strict);  // Prevent CSRF
    cookie.set_path("/");
    // Optionally set expiration
    // cookie.set_expires(...);
    cookie
}
```

## Implementing Rate Limiting

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

struct RateLimiter {
    // Maps IP addresses to a list of request timestamps
    requests: Arc<Mutex<HashMap<IpAddr, Vec<Instant>>>>,
    window: Duration,
    max_requests: usize,
}

impl RateLimiter {
    fn new(window_seconds: u64, max_requests: usize) -> Self {
        Self {
            requests: Arc::new(Mutex::new(HashMap::new())),
            window: Duration::from_secs(window_seconds),
            max_requests,
        }
    }
    
    fn is_rate_limited(&self, ip: IpAddr) -> bool {
        let mut requests = self.requests.lock().unwrap();
        let now = Instant::now();
        
        // Get or insert the request list for this IP
        let request_times = requests.entry(ip).or_insert_with(Vec::new);
        
        // Remove timestamps outside the window
        while request_times.first().map_or(false, |t| now.duration_since(*t) > self.window) {
            request_times.remove(0);
        }
        
        // Check if too many requests
        if request_times.len() >= self.max_requests {
            return true;
        }
        
        // Add current timestamp
        request_times.push(now);
        false
    }
}

// Usage in a middleware
async fn rate_limit_middleware(
    req: Request<Body>,
    limiter: Arc<RateLimiter>,
    next: Next,
) -> Result<Response<Body>> {
    let ip = req.remote_addr().ip();
    
    if limiter.is_rate_limited(ip) {
        // Return 429 Too Many Requests
        let mut response = Response::builder()
            .status(StatusCode::TOO_MANY_REQUESTS)
            .body(Body::from("Rate limit exceeded. Please try again later."))?;
            
        // Add Retry-After header
        response.headers_mut().insert(
            header::RETRY_AFTER, 
            HeaderValue::from_static("60")
        );
        
        return Ok(response);
    }
    
    next.run(req).await
}
```

## File Path Sanitization

```rust
use std::path::{Path, PathBuf};

fn sanitize_path(base_dir: &Path, user_path: &str) -> Option<PathBuf> {
    let path = Path::new(user_path);
    
    // Check for path traversal attempts
    if path.components().any(|c| c == std::path::Component::ParentDir) {
        return None; // Path contains ".."
    }
    
    // Create the full path
    let full_path = base_dir.join(path);
    
    // Ensure the resolved path is still within the base directory
    if !full_path.starts_with(base_dir) {
        return None; // Path would escape the base directory
    }
    
    Some(full_path)
}
```

## Dependencies Security Management

```rust
// Add this to Cargo.toml
// [dependencies]
// cargo-audit = "0.17"

// Scan dependencies
fn check_dependencies() {
    use std::process::Command;
    
    println!("Checking dependencies for known vulnerabilities...");
    
    let output = Command::new("cargo")
        .args(&["audit", "--json"])
        .output()
        .expect("Failed to execute cargo audit");
        
    if !output.status.success() {
        let vulnerabilities = String::from_utf8_lossy(&output.stdout);
        eprintln!("Security vulnerabilities found: {}", vulnerabilities);
    } else {
        println!("No vulnerabilities found.");
    }
}
```

## Best Practices
- Regularly update dependencies.
- Use security headers and strict TLS settings.
- Apply the principle of least privilege.
- Validate and sanitize all user input.
- Use secure defaults and fail closed.
- Implement defense in depth (multiple security layers).
- Log security-relevant events.
- Conduct regular security testing.

## Integration
- Integrate with vulnerability scanners and SIEM.
- Use automated security testing in CI/CD.
- Monitor for new vulnerabilities in dependencies.

## Quiz
1. What is threat modeling?
2. How does mTLS differ from TLS?
3. Name three security headers you should use in a web application.
4. What is the purpose of rate limiting?
5. How can you prevent path traversal attacks?

## Further Reading
- [OWASP Top Ten](https://owasp.org/www-project-top-ten/)
- [Threat Modeling: Designing for Security](https://www.amazon.com/Threat-Modeling-Designing-Adam-Shostack/dp/1118809998)
- [The Cargo Audit Tool](https://github.com/rustsec/rustsec/tree/main/cargo-audit)
