<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\09-virtual-hosts.md -->
# Virtual Hosts

## Learning Objectives
- Understand name-based and IP-based virtual hosting concepts and their practical applications
- Implement a flexible virtual host configuration system using Rust data structures
- Create an efficient domain name matching system including exact matches and wildcard subdomains
- Design per-host configuration for document roots, error pages, and logging
- Develop a robust request routing system based on the HTTP Host header 
- Build comprehensive tests to verify virtual host functionality and edge cases

## Prerequisites
- Completion of modules 1-8 of the Rusty Webserver tutorial
- Understanding of HTTP headers, especially the Host header
- Familiarity with HashMap and other collection types in Rust
- Basic knowledge of domain name structure and DNS concepts
- Experience with configuration management patterns

## Navigation
- [Previous: Connection Pooling and HTTP Keep-Alive](08-connection-pooling.md)
- [Next: Reverse Proxy](10-reverse-proxy.md)

## Introduction

Virtual hosting is a technique that enables a single web server to host multiple websites or domains on the same IP address and port. Instead of requiring a dedicated server or IP address for each website, virtual hosts allow efficient resource sharing while keeping websites logically separate.

In today's web infrastructure, virtual hosting is ubiquitous. Major hosting providers may run thousands of websites on a single physical server using this technique. Popular web servers like Apache, Nginx, and Caddy all implement sophisticated virtual hosting capabilities.

In this module, we'll extend our Rusty Web Server to support name-based virtual hosting, allowing it to serve different content based on the domain name in the client's request. This feature significantly enhances our server's versatility and brings it closer to production-ready functionality.

By the end of this module, you'll understand how to:
- Design a flexible configuration system for multiple virtual hosts
- Efficiently route requests to the correct content based on the HTTP Host header
- Implement advanced features like wildcard domains and host aliases

## Virtual Hosting Fundamentals

Virtual hosting is a method for serving multiple websites from a single server instance. This approach maximizes resource utilization and simplifies server management while maintaining the appearance of separate, dedicated hosting for each website.

### Types of Virtual Hosting

There are two primary approaches to implementing virtual hosts:

#### 1. Name-Based Virtual Hosting

Name-based virtual hosting uses the HTTP `Host` header to determine which website content to serve. When a client makes an HTTP request, it includes a `Host` header specifying the domain name it's trying to reach:

```
GET /index.html HTTP/1.1
Host: example.com
```

The server examines this header and routes the request to the appropriate virtual host configuration. This approach:

- Requires only one IP address for multiple domains
- Is more scalable and resource-efficient
- Works well for most modern web applications
- Relies on HTTP/1.1 compatibility (virtually universal today)

#### 2. IP-Based Virtual Hosting

IP-based virtual hosting uses different IP addresses to distinguish between websites:

- Each website has its own dedicated IP address
- The server determines which site to serve based on the destination IP
- More resource-intensive but works with older protocols
- Useful when SSL/TLS certificates require dedicated IPs (less common with SNI)

### How Name-Based Virtual Hosting Works

```mermaid
sequenceDiagram
    participant Browser
    participant DNS as DNS Server
    participant WebServer as Web Server
    participant VHost1 as example.com Content
    participant VHost2 as blog.example.com Content

    Browser->>DNS: 🔍 Lookup example.com
    DNS->>Browser: IP: ************

    Browser->>WebServer: GET / HTTP/1.1<br>Host: example.com
    WebServer->>WebServer: 🔍 Check Host header
    WebServer->>VHost1: 📍 Route to example.com handler
    VHost1->>WebServer: 📄 Return example.com content
    WebServer->>Browser: HTTP Response with example.com content

    Note over Browser,WebServer: 🔄 Later request to blog.example.com

    Browser->>DNS: 🔍 Lookup blog.example.com
    DNS->>Browser: IP: ************ (same IP)

    Browser->>WebServer: GET / HTTP/1.1<br>Host: blog.example.com
    WebServer->>WebServer: 🔍 Check Host header
    WebServer->>VHost2: 📍 Route to blog.example.com handler
    VHost2->>WebServer: 📄 Return blog.example.com content
    WebServer->>Browser: HTTP Response with blog.example.com content

    %% Enhanced styling for better contrast
    style Browser fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    style DNS fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    style WebServer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    style VHost1 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style VHost2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
```

*Both requests are directed to the same IP address and server, but different content is served based on the Host header.*

## Virtual Host Design Considerations

Before implementing virtual hosts, we need to make several key design decisions:

### 1. Host Name Matching Strategy

We need to decide how to match incoming Host headers with configured virtual hosts:

| Strategy | Description | Examples | Trade-offs |
|----------|-------------|----------|------------|
| **Exact match** | Direct string comparison | example.com | Simple, fast lookup |
| **Wildcard subdomains** | Match any subdomain | *.example.com | More flexible, slightly more complex |
| **Regular expressions** | Pattern matching | ^(www\|m)\.example\.com$ | Most flexible, more overhead |

Our implementation will support both exact matches and wildcard subdomains for a good balance of performance and flexibility.

### 2. Configuration Storage

For efficiency, we'll store virtual host configurations in a `HashMap` for O(1) lookup time. This provides better performance than linear searches through an array, especially with many virtual hosts.

### 3. Default Host Behavior

We need a fallback strategy for requests with:
- Missing Host headers
- Unknown/unconfigured domains
- Invalid Host header format

We'll implement a designated default host that handles these scenarios.

### 4. Per-Host Configuration

Each virtual host needs independent configuration for:
- Document root directory
- Custom error pages
- Logging paths
- Default files (index.html, etc.)
- Host aliases (multiple domains serving the same content)

## Virtual Hosts Implementation

Our implementation will focus on creating a flexible, efficient, and maintainable virtual hosting system.

### Virtual Host Architecture

The diagram below illustrates the high-level architecture of our virtual host system:

```mermaid
flowchart LR
    Client[Client Browser] -->|Request with<br>Host header| Server[Web Server]
    
    subgraph "Virtual Host System"
        HostExtractor[Host Header<br>Extractor] --> HostMatcher[Host Matcher]
        HostMatcher --> VHostConfig[Virtual Host<br>Configuration]
        VHostConfig --> ContentHandler[Content Handler]
    end
    
    Server --> HostExtractor
    ContentHandler --> Response[HTTP Response]
    Response --> Client
    
    subgraph "Configuration"
        ConfigFile[TOML Config] --> Parser[Config Parser]
        Parser --> VHostRegistry[Virtual Host Registry<br>HashMap]
        VHostRegistry --> HostMatcher
    end
    
    style Client fill:#f9f,stroke:#333,stroke-width:2px
    style Server fill:#bbf,stroke:#333,stroke-width:2px
    style HostMatcher fill:#bfb,stroke:#333,stroke-width:2px
    style VHostConfig fill:#fbb,stroke:#333,stroke-width:2px
    style Response fill:#ffd,stroke:#333,stroke-width:2px
```

*This architecture provides a clean separation of concerns and efficient request routing.*

### Request Routing Flow

The sequence diagram below shows how a request flows through our virtual host system:

```mermaid
sequenceDiagram
    participant Client
    participant Server
    participant Router as "VHost Router"
    participant Config as "Config Store"
    participant Handler as "Static File Handler"
    
    Client->>Server: GET /page.html HTTP/1.1<br>Host: blog.example.com
    Server->>Router: Extract Host header
    Router->>Config: Lookup "blog.example.com"
    
    alt Host Found
        Config->>Router: Return VHost Config
        Router->>Handler: Create handler with VHost doc root
        Handler->>Server: Serve file from /var/www/blog/
        Server->>Client: 200 OK with file content
    else Host Not Found
        Config->>Router: Return Default VHost Config
        Router->>Handler: Create handler with default doc root
        Handler->>Server: Serve file from /var/www/default/
        Server->>Client: 200 OK with default content
    end
```

*Our server uses the Host header to determine which document root to serve files from.*

## Implementing Virtual Hosts

## Implementing the Virtual Host System

Let's build our virtual host system step by step, focusing on creating a flexible and maintainable solution.

### Step 1: Enhanced Configuration System

First, we need to extend our configuration system to support multiple virtual hosts. We'll create a dedicated structure for virtual host settings and update our main configuration.

```rust
// src/config/mod.rs

use std::collections::HashMap;
use std::path::PathBuf;

/// Configuration for a single virtual host
#[derive(Debug, Clone)]
pub struct VirtualHostConfig {
    // Primary domain name for this virtual host
    pub server_name: String,
    
    // Additional domain names that should be served with this config
    pub server_aliases: Vec<String>,
    
    // Root directory for this virtual host's files
    pub document_root: PathBuf,
    
    // Default files to serve for directory requests
    pub index_files: Vec<String>,
    
    // Custom error pages for different status codes
    pub error_pages: HashMap<u16, PathBuf>, // Maps status code to relative path
    
    // Host-specific access log path (None uses server default)
    pub access_log: Option<PathBuf>,
    
    // Host-specific error log path (None uses server default)
    pub error_log: Option<PathBuf>,
    
    // Whether this host should redirect HTTP to HTTPS
    pub require_https: bool,
}

impl Default for VirtualHostConfig {
    fn default() -> Self {
        Self {
            server_name: String::from("localhost"),
            server_aliases: Vec::new(),
            document_root: PathBuf::from("./public"),
            index_files: vec![String::from("index.html"), String::from("index.htm")],
            error_pages: HashMap::new(),
            access_log: None,
            error_log: None,
            require_https: false,
        }
    }
}

/// Main server configuration with virtual host support
#[derive(Debug, Clone)]
pub struct Config {
    // Server bind address
    pub address: String,
    
    // Server port
    pub port: u16,
    
    // Number of worker threads in the thread pool
    pub worker_threads: usize,
    
    // General connection timeout in seconds
    pub connection_timeout: u64,
    
    // Keep-alive timeout in seconds
    pub keep_alive_timeout: u64,
    
    // Default host configuration for unmatched domains
    pub default_host: VirtualHostConfig,
    
    // Map of domain names to their virtual host configurations
    pub virtual_hosts: HashMap<String, VirtualHostConfig>,
    
    // Array of virtual host configurations from the config file
    // This is used during loading and then converted to the HashMap
    #[serde(skip)]
    pub virtual_hosts_array: Vec<VirtualHostConfig>,
}

impl Default for Config {
    fn default() -> Self {
        let default_host = VirtualHostConfig::default();
        let mut virtual_hosts = HashMap::new();
        
        // Always include localhost in the virtual hosts map
        virtual_hosts.insert("localhost".to_string(), default_host.clone());
        
        Self {
            address: String::from("127.0.0.1"),
            port: 8080,
            worker_threads: 4,
            connection_timeout: 60,
            keep_alive_timeout: 5,
            default_host,
            virtual_hosts,
            virtual_hosts_array: Vec::new(),
        }
    }
}

impl Config {
    /// Loads server configuration from a TOML file
    pub fn load_from_file(path: &str) -> Result<Self, std::io::Error> {
        use std::fs;
        use toml::from_str;
        
        // Read and parse the configuration file
        let content = fs::read_to_string(path)?;
        let mut config: Config = from_str(&content)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?;
        
        // Process virtual hosts from the array into the HashMap for efficient lookup
        config.process_virtual_hosts();
        
        Ok(config)
    }
    
    /// Process the virtual_hosts_array into the virtual_hosts HashMap
    /// Also registers aliases and validates configurations
    fn process_virtual_hosts(&mut self) {
        // Start with a fresh HashMap containing just the localhost entry
        let mut hosts_map = HashMap::new();
        hosts_map.insert("localhost".to_string(), self.default_host.clone());
        
        // Process each virtual host configuration
        for vhost in self.virtual_hosts_array.iter() {
            // Add the primary server name
            hosts_map.insert(vhost.server_name.clone(), vhost.clone());
            
            // Add all server aliases
            for alias in &vhost.server_aliases {
                hosts_map.insert(alias.clone(), vhost.clone());
            }
        }
        
        self.virtual_hosts = hosts_map;
    }
    
    /// Gets the appropriate virtual host configuration for a given hostname
    /// Handles port stripping, wildcard domains, and fallback to default host
    pub fn get_virtual_host(&self, host: &str) -> &VirtualHostConfig {
        // Remove port if present (Host: example.com:8080 -> example.com)
        let host = host.split(':').next().unwrap_or(host);
        
        // First try an exact match
        if let Some(vhost) = self.virtual_hosts.get(host) {
            return vhost;
        }
        
        // Try wildcard matching for subdomains
        // For "blog.example.com", check if there's a "*.example.com" entry
        if let Some((_, parent_domain)) = host.split_once('.') {
            let wildcard_host = format!("*.{}", parent_domain);
            if let Some(vhost) = self.virtual_hosts.get(&wildcard_host) {
                return vhost;
            }
        }
        
        // Fall back to default host if no match found
        &self.default_host
    }
}
```

### Step 2: Creating the Virtual Host Handler

Now, let's create a dedicated module for handling virtual host routing. This handler will:
1. Extract the Host header from incoming requests
2. Find the matching virtual host configuration
3. Route the request to the appropriate content handler
4. Handle cases like missing Host headers and custom error pages

```rust
// src/server/vhost_handler.rs

use std::sync::Arc;
use std::path::Path;
use std::fs;
use std::io::Read;
use std::time::SystemTime;

use log::{debug, warn, error};
use crate::config::{Config, VirtualHostConfig};
use crate::http::{Request, Response, StatusCode, Method, Version};
use crate::server::static_handler::StaticFileHandler;
use crate::util::mime_types::get_mime_type;
use crate::error::{Result, ServerError};

/// Handler for routing requests to the appropriate virtual host
pub struct VirtualHostHandler {
    /// Shared server configuration
    config: Arc<Config>,
    
    /// Cache of static file handlers for each virtual host
    /// This avoids recreating handlers for each request
    static_handlers: parking_lot::RwLock<std::collections::HashMap<String, StaticFileHandler>>,
}

impl VirtualHostHandler {
    /// Create a new virtual host handler with the given configuration
    pub fn new(config: Arc<Config>) -> Self {
        Self { 
            config,
            static_handlers: parking_lot::RwLock::new(std::collections::HashMap::new()),
        }
    }
    
    /// Handle an HTTP request by routing to the appropriate virtual host
    pub fn handle_request(&self, request: &Request) -> Response {
        // Extract and validate the Host header
        let host = match self.extract_host_header(request) {
            Ok(host) => host,
            Err(response) => return response,
        };
        
        debug!("Request for host: {} path: {}", host, request.path);
        
        // Get the virtual host configuration for this host
        let vhost_config = self.config.get_virtual_host(&host);
        
        // Check if we should redirect HTTP to HTTPS
        if vhost_config.require_https && !request.is_secure() {
            return self.create_https_redirect(request, &host);
        }
        
        // Get or create a static file handler for this virtual host
        let static_handler = self.get_static_handler(&vhost_config);
        
        // Handle the request using the appropriate handler
        let mut response = static_handler.handle_request(request);
        
        // For error responses, try to use custom error pages
        if response.status.is_client_error() || response.status.is_server_error() {
            if let Some(custom_error) = self.get_custom_error_page(vhost_config, response.status) {
                response = custom_error;
            }
        }
        
        // Add server and virtual host identification headers
        response.headers.insert(
            "Server".to_string(), 
            format!("RustyServer/{}", env!("CARGO_PKG_VERSION"))
        );
        
        response
    }
    
    /// Extract and validate the Host header from the request
    fn extract_host_header(&self, request: &Request) -> Result<String, Response> {
        // In HTTP/1.1, Host header is mandatory
        if request.version == Version::HTTP1_1 && !request.headers.contains_key("host") {
            return Err(Response::new()
                .with_status(StatusCode::BadRequest)
                .with_content_type("text/plain")
                .with_body("Missing Host header"));
        }
        
        // Get the host header, defaulting to an empty string for HTTP/1.0
        let host = request.headers.get("host")
            .cloned()
            .unwrap_or_default();
            
        // Basic validation of the host header format
        if !host.is_empty() && !self.is_valid_host(&host) {
            return Err(Response::new()
                .with_status(StatusCode::BadRequest)
                .with_content_type("text/plain")
                .with_body("Invalid Host header"));
        }
        
        Ok(host)
    }
    
    /// Simple validation of host header format
    fn is_valid_host(&self, host: &str) -> bool {
        // Basic validation - more comprehensive validation could be added
        // This checks for obviously malformed hosts
        !host.contains(char::is_whitespace) && 
            !host.contains('<') && 
            !host.contains('>') &&
            host.len() < 255
    }
    
    /// Get or create a static file handler for the virtual host
    fn get_static_handler(&self, vhost_config: &VirtualHostConfig) -> StaticFileHandler {
        let host_key = vhost_config.server_name.clone();
        
        // Try to get an existing handler from the cache
        {
            let handlers = self.static_handlers.read();
            if let Some(handler) = handlers.get(&host_key) {
                return handler.clone();
            }
        }
        
        // Create a new handler if none exists
        let new_handler = StaticFileHandler::new(
            &vhost_config.document_root, 
            &vhost_config.index_files
        );
        
        // Store it in the cache for future requests
        {
            let mut handlers = self.static_handlers.write();
            handlers.insert(host_key, new_handler.clone());
        }
        
        new_handler
    }
    
    /// Create a custom error page response if one is configured
    fn get_custom_error_page(&self, vhost_config: &VirtualHostConfig, status: StatusCode) -> Option<Response> {
        let status_code = status.as_u16();
        
        // Check if there's a custom error page for this status code
        if let Some(error_page_path) = vhost_config.error_pages.get(&status_code) {
            let full_path = vhost_config.document_root.join(error_page_path);
            
            // Try to read the custom error page
            if let Ok(mut file) = fs::File::open(&full_path) {
                let mut content = Vec::new();
                if file.read_to_end(&mut content).is_ok() {
                    let mime_type = get_mime_type(error_page_path.to_str().unwrap_or(""));
                    
                    return Some(Response::new()
                        .with_status(status)
                        .with_content_type(&mime_type)
                        .with_body(content));
                }
            }
        }
        
        None
    }
    
    /// Create a redirect response from HTTP to HTTPS
    fn create_https_redirect(&self, request: &Request, host: &str) -> Response {
        // Build the HTTPS URL
        let https_url = format!("https://{}{}", 
            host,
            request.path
        );
        
        Response::new()
            .with_status(StatusCode::MovedPermanently)
            .with_header("Location", &https_url)
            .with_content_type("text/plain")
            .with_body(format!("Redirecting to {}", https_url))
    }
}
```

### Step 3: Updating the Server Core with Virtual Host Support

Now we need to integrate the virtual host handler into our main server. This includes:
1. Updating the server initialization to use the virtual host handler
2. Modifying the request handling flow to route through virtual hosts
3. Ensuring proper error handling and logging

```rust
// src/server/mod.rs

use std::sync::Arc;
use std::net::{TcpListener, TcpStream};
use std::io;
use std::thread;
use std::time::Duration;

use log::{info, debug, error, warn};
use crate::config::Config;
use crate::http::{Request, Response, StatusCode};
use crate::error::{Result, ServerError};
use crate::server::vhost_handler::VirtualHostHandler;
use crate::server::connection::HttpConnection;
use crate::server::thread_pool::ThreadPool;
use crate::server::connection_pool::ConnectionPool;
use crate::logging::access_logger::AccessLogger;

pub mod static_handler;
pub mod vhost_handler;
pub mod thread_pool;
pub mod connection;
pub mod connection_pool;
pub mod tests;

/// Main HTTP server with virtual host support
pub struct Server {
    /// Server configuration
    config: Arc<Config>,
    
    /// Virtual host handler for routing requests
    vhost_handler: Arc<VirtualHostHandler>,
    
    /// Thread pool for handling requests
    thread_pool: ThreadPool,
    
    /// Connection pool for keep-alive connections
    connection_pool: Arc<ConnectionPool>,
    
    /// Access logger for recording requests
    access_logger: Arc<AccessLogger>,
    
    /// Flag to control server shutdown
    running: Arc<std::sync::atomic::AtomicBool>,
}

impl Server {
    /// Create a new server with the given configuration
    pub fn new(config: Config) -> Result<Self> {
        let config = Arc::new(config);
        
        // Create the virtual host handler
        let vhost_handler = Arc::new(VirtualHostHandler::new(Arc::clone(&config)));
        
        // Create the thread pool based on configuration
        let thread_count = config.worker_threads;
        let thread_pool = ThreadPool::new(thread_count)?;
        
        // Create the connection pool
        let connection_pool = Arc::new(ConnectionPool::new(
            config.connection_timeout,
            config.keep_alive_timeout
        ));
        
        // Create the access logger
        let access_logger = Arc::new(AccessLogger::new(None)?);
        
        // Create the running flag
        let running = Arc::new(std::sync::atomic::AtomicBool::new(true));
        
        Ok(Self {
            config,
            vhost_handler,
            thread_pool,
            connection_pool,
            access_logger,
            running,
        })
    }
    
    /// Handle an incoming HTTP request and route to the appropriate virtual host
    pub fn handle_request(&self, request: &Request) -> Response {
        // Log the incoming request
        debug!("Received {} request for {} (Host: {})",
            request.method,
            request.path,
            request.headers.get("host").unwrap_or(&"[no host]".to_string())
        );
        
        // Let the virtual host handler process the request
        self.vhost_handler.handle_request(request)
    }
    
    /// Run the server, listening for incoming connections
    pub fn run(&self) -> Result<()> {
        let address = format!("{}:{}", self.config.address, self.config.port);
        let listener = TcpListener::bind(&address)
            .map_err(|e| ServerError::Io(e))?;
            
        info!("Server listening on {} with virtual host support", address);
        info!("Configured virtual hosts: {}", 
            self.config.virtual_hosts.keys()
                .map(|k| k.to_string())
                .collect::<Vec<_>>()
                .join(", ")
        );
        
        // Accept connections in a loop while running flag is true
        while self.running.load(std::sync::atomic::Ordering::Relaxed) {
            // Set the accept timeout so we can periodically check if we should still be running
            listener.set_nonblocking(true)?;
            
            match listener.accept() {
                Ok((stream, addr)) => {
                    // Process the new connection
                    self.handle_connection(stream)?;
                },
                Err(e) if e.kind() == io::ErrorKind::WouldBlock => {
                    // No connection available, just wait a bit and continue
                    thread::sleep(Duration::from_millis(100));
                    continue;
                },
                Err(e) => {
                    error!("Error accepting connection: {}", e);
                    // Brief pause to avoid tight loop on persistent errors
                    thread::sleep(Duration::from_millis(100));
                }
            }
        }
        
        info!("Server shutdown complete");
        Ok(())
    }
    
    /// Handle a new TCP connection
    fn handle_connection(&self, stream: TcpStream) -> Result<()> {
        // Set up socket options
        stream.set_nonblocking(false)?;
        
        // Get a connection from the pool or create a new one
        let connection = self.connection_pool.get_connection(stream)?;
        
        // Clone Arc pointers for the new thread
        let vhost_handler = Arc::clone(&self.vhost_handler);
        let access_logger = Arc::clone(&self.access_logger);
        let connection_pool = Arc::clone(&self.connection_pool);
        
        // Submit the job to the thread pool
        self.thread_pool.execute(move || {
            // Process this connection with keep-alive support
            let peer_addr = connection.peer_addr().to_string();
            
            // Keep handling requests as long as the connection is alive
            Self::handle_keep_alive_connection(
                connection,
                &vhost_handler,
                &access_logger,
                &connection_pool
            );
        });
        
        Ok(())
    }
    
    // Other server methods...
}
```

### Step 4: Creating a Flexible Configuration Format

A well-designed configuration system is crucial for managing multiple virtual hosts. We'll use TOML for a clear, hierarchical configuration format that's easy to read and maintain.

#### Configuration File Structure

Our configuration file includes:
1. Global server settings (address, port, timeouts)
2. Default host configuration for fallback scenarios
3. An array of virtual host entries

Here's a comprehensive example with comments explaining each section:

```toml
# ==============================================================================
# GLOBAL SERVER CONFIGURATION
# ==============================================================================
# Network binding settings
address = "0.0.0.0"  # Listen on all interfaces
port = 80            # Standard HTTP port

# Performance settings
worker_threads = 8             # Number of worker threads in the thread pool
max_connections = 1000         # Maximum concurrent connections
connection_timeout = 60        # General connection timeout in seconds
keep_alive_timeout = 15        # Keep-alive connection idle timeout
keep_alive_max_requests = 1000 # Maximum requests per keep-alive connection

# Logging settings
log_level = "info"             # Global log level (debug, info, warn, error)
access_log = "logs/access.log" # Global access log path
error_log = "logs/error.log"   # Global error log path

# ==============================================================================
# DEFAULT HOST CONFIGURATION
# ==============================================================================
# This is used when a request doesn't match any configured virtual host
[default_host]
server_name = "default-site.example.com"
document_root = "./public/default"
index_files = ["index.html", "index.htm"]
error_pages = { 
    404 = "errors/404.html",
    500 = "errors/500.html",
    503 = "errors/maintenance.html"
}

# ==============================================================================
# VIRTUAL HOST CONFIGURATIONS
# ==============================================================================
# Main website
[[virtual_hosts]]
server_name = "example.com"
server_aliases = ["www.example.com"]  # Alternative domain names
document_root = "./public/main"
index_files = ["index.html", "index.htm"]
error_pages = { 
    404 = "errors/custom404.html", 
    500 = "errors/custom500.html" 
}
require_https = true  # Redirect HTTP to HTTPS

# Company blog
[[virtual_hosts]]
server_name = "blog.example.com"
document_root = "./public/blog"
index_files = ["index.html", "index.md"]
access_log = "logs/blog_access.log"  # Host-specific access log
error_log = "logs/blog_error.log"    # Host-specific error log

# API subdomain
[[virtual_hosts]]
server_name = "api.example.com"
document_root = "./public/api"
index_files = ["index.json"]
# Custom headers for API responses
response_headers = { 
    "Access-Control-Allow-Origin" = "*",
    "Cache-Control" = "no-store", 
    "X-Content-Type-Options" = "nosniff"
}

# Customer portal
[[virtual_hosts]]
server_name = "portal.example.com"
document_root = "./public/portal"
index_files = ["index.html", "home.html"]

# Wildcard subdomain for user content
[[virtual_hosts]]
server_name = "*.user.example.com"  # Matches any user subdomain
document_root = "./public/users"
index_files = ["index.html"]
```

### Step 5: Update the Configuration Parser

Now, we need to update our configuration parser to handle this extended format:

```rust
// src/config/mod.rs (continued)

impl Config {
    pub fn load_from_file(path: &str) -> Result<Self, std::io::Error> {
        use std::fs;
        use toml::from_str;
        
        let content = fs::read_to_string(path)?;
        let mut config: Config = from_str(&content)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?;
        
        // Convert the array of virtual hosts to a HashMap
        for vhost in config.virtual_hosts_array.iter() {
            config.virtual_hosts.insert(vhost.server_name.clone(), vhost.clone());
        }
        
        Ok(config)
    }
}
```

### Step 6: Testing Virtual Host Functionality

Let's write some tests to verify our virtual host implementation:

```rust
### Step 5: Comprehensive Virtual Host Testing

Testing virtual hosts is important to ensure proper routing behavior. Let's create a comprehensive test suite that covers:
1. Basic routing based on Host header
2. Wildcard subdomain matching
3. Missing Host header handling
4. Custom error pages per virtual host

```rust
// src/server/tests.rs

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{Config, VirtualHostConfig};
    use crate::http::{Method, Request, StatusCode, Version};
    use crate::server::vhost_handler::VirtualHostHandler;
    use std::collections::HashMap;
    use std::path::PathBuf;
    use std::sync::Arc;
    use std::fs;
    use tempfile::TempDir;    /// Helper function to create test files for virtual hosts
    fn setup_test_files() -> (TempDir, Config) {
        // Create a temporary directory for our test files
        let temp_dir = TempDir::new().expect("Failed to create temp directory");
        let base_path = temp_dir.path();
        
        // Create directory structure for different virtual hosts
        let paths = [
            "default/index.html",
            "example/index.html",
            "example/images/logo.png",
            "example/errors/404.html",
            "blog/index.html",
            "blog/posts/welcome.html",
            "wildcard/index.html"
        ];
        
        // Create all the test files with sample content
        for path in &paths {
            let full_path = base_path.join(path);
            fs::create_dir_all(full_path.parent().unwrap()).expect("Failed to create directories");
            let content = format!("<html><body><h1>{}</h1><p>This is the {} virtual host</p></body></html>", 
                path.split('/').next().unwrap(),
                path.split('/').next().unwrap());
            fs::write(&full_path, content).expect("Failed to create test file");
        }
        
        // Create special error page for testing
        let error_page_content = "<html><body><h1>Custom 404 Error</h1><p>File not found</p></body></html>";
        fs::write(base_path.join("example/errors/404.html"), error_page_content)
            .expect("Failed to create error page");
        
        // Set up virtual host configurations
        let mut config = Config::default();
        
        // Configure default host
        config.default_host.server_name = "default.example.com".to_string();
        config.default_host.document_root = base_path.join("default");
        config.default_host.index_files = vec!["index.html".to_string()];
        
        // Configure virtual hosts
        let mut virtual_hosts = HashMap::new();
        
        // example.com host
        let mut host1 = VirtualHostConfig::default();
        host1.server_name = "example.com".to_string();
        host1.server_aliases = vec!["www.example.com".to_string()];
        host1.document_root = base_path.join("example");
        host1.index_files = vec!["index.html".to_string()];
        
        // Add custom error pages
        let mut error_pages = HashMap::new();
        error_pages.insert(404, PathBuf::from("errors/404.html"));
        host1.error_pages = error_pages;
        
        // blog.example.com host
        let mut host2 = VirtualHostConfig::default();
        host2.server_name = "blog.example.com".to_string();
        host2.document_root = base_path.join("blog");
        host2.index_files = vec!["index.html".to_string()];
        
        // Wildcard subdomain host
        let mut host3 = VirtualHostConfig::default();
        host3.server_name = "*.user.example.com".to_string();
        host3.document_root = base_path.join("wildcard");
        host3.index_files = vec!["index.html".to_string()];
        
        // Add virtual hosts to configuration
        virtual_hosts.insert("example.com".to_string(), host1.clone());
        virtual_hosts.insert("www.example.com".to_string(), host1);
        virtual_hosts.insert("blog.example.com".to_string(), host2);
        virtual_hosts.insert("*.user.example.com".to_string(), host3);
        
        config.virtual_hosts = virtual_hosts;
        
        (temp_dir, config)
    }

    #[test]
    fn test_virtual_host_routing() {
        // Setup test environment with files
        let (_temp_dir, config) = setup_test_files();
        
        // Create virtual host handler
        let config = Arc::new(config);
        let vhost_handler = VirtualHostHandler::new(Arc::clone(&config));
        
        // Create test requests with different Host headers
        let mut req1 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        req1.headers.insert("host".to_string(), "example.com".to_string());
        
        let mut req2 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        req2.headers.insert("host".to_string(), "blog.example.com".to_string());
        
        let mut req3 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        req3.headers.insert("host".to_string(), "alice.user.example.com".to_string());
        
        let mut req4 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        req4.headers.insert("host".to_string(), "unknown.example.com".to_string());
        
        // Test that requests are routed to the correct virtual host
        let resp1 = vhost_handler.handle_request(&req1);
        let resp2 = vhost_handler.handle_request(&req2);
        let resp3 = vhost_handler.handle_request(&req3);
        let resp4 = vhost_handler.handle_request(&req4);
        
        // Verify responses
        assert_eq!(resp1.status, StatusCode::OK);
        assert!(String::from_utf8_lossy(&resp1.body).contains("example"));
        
        assert_eq!(resp2.status, StatusCode::OK);
        assert!(String::from_utf8_lossy(&resp2.body).contains("blog"));
        
        assert_eq!(resp3.status, StatusCode::OK);
        assert!(String::from_utf8_lossy(&resp3.body).contains("wildcard"));
        
        assert_eq!(resp4.status, StatusCode::OK);
        assert!(String::from_utf8_lossy(&resp4.body).contains("default"));
    }
    
    #[test]
    fn test_virtual_host_aliases() {
        // Setup test environment with files
        let (_temp_dir, config) = setup_test_files();
        
        // Create virtual host handler
        let config = Arc::new(config);
        let vhost_handler = VirtualHostHandler::new(Arc::clone(&config));
        
        // Create test requests with primary domain and alias
        let mut req1 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        req1.headers.insert("host".to_string(), "example.com".to_string());
        
        let mut req2 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        req2.headers.insert("host".to_string(), "www.example.com".to_string());
        
        // Test that both requests get the same content
        let resp1 = vhost_handler.handle_request(&req1);
        let resp2 = vhost_handler.handle_request(&req2);
        
        // Both should succeed and have the same content
        assert_eq!(resp1.status, StatusCode::OK);
        assert_eq!(resp2.status, StatusCode::OK);
        assert_eq!(resp1.body, resp2.body);
    }
    
    #[test]
    fn test_custom_error_pages() {
        // Setup test environment with files
        let (_temp_dir, config) = setup_test_files();
        
        // Create virtual host handler
        let config = Arc::new(config);
        let vhost_handler = VirtualHostHandler::new(Arc::clone(&config));
        
        // Create test requests for a non-existent file
        let mut req1 = Request::new(Method::GET, "/non-existent.html".to_string(), Version::HTTP1_1);
        req1.headers.insert("host".to_string(), "example.com".to_string());
        
        let mut req2 = Request::new(Method::GET, "/non-existent.html".to_string(), Version::HTTP1_1);
        req2.headers.insert("host".to_string(), "blog.example.com".to_string());
        
        // Test error handling
        let resp1 = vhost_handler.handle_request(&req1);
        let resp2 = vhost_handler.handle_request(&req2);
        
        // Both should return 404, but example.com should use the custom error page
        assert_eq!(resp1.status, StatusCode::NotFound);
        assert_eq!(resp2.status, StatusCode::NotFound);
        
        // example.com should have the custom error page content
        assert!(String::from_utf8_lossy(&resp1.body).contains("Custom 404 Error"));
        
        // blog.example.com should have the default error page
        assert!(!String::from_utf8_lossy(&resp2.body).contains("Custom 404 Error"));
    }    #[test]
    fn test_missing_host_header() {
        // Setup test environment with files
        let (_temp_dir, config) = setup_test_files();
        
        // Create virtual host handler
        let config = Arc::new(config);
        let vhost_handler = VirtualHostHandler::new(Arc::clone(&config));
        
        // Create a request with no Host header (HTTP/1.1)
        let req1 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        
        // Create a request with no Host header (HTTP/1.0)
        let req2 = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_0);
        
        // Test HTTP/1.1 (should fail without Host header)
        let resp1 = vhost_handler.handle_request(&req1);
        assert_eq!(resp1.status, StatusCode::BadRequest);
        assert!(String::from_utf8_lossy(&resp1.body).contains("Missing Host header"));
        
        // Test HTTP/1.0 (should work with default host)
        let resp2 = vhost_handler.handle_request(&req2);
        assert_eq!(resp2.status, StatusCode::OK);
        assert!(String::from_utf8_lossy(&resp2.body).contains("default"));
    }
    
    #[test]
    fn test_https_redirect() {
        // Setup test environment with files
        let (_temp_dir, mut config) = setup_test_files();
        
        // Configure one host to require HTTPS
        let example_host = config.virtual_hosts.get_mut("example.com").unwrap();
        example_host.require_https = true;
        
        // Create virtual host handler
        let config = Arc::new(config);
        let vhost_handler = VirtualHostHandler::new(Arc::clone(&config));
        
        // Create a request for the host requiring HTTPS
        let mut req = Request::new(Method::GET, "/index.html".to_string(), Version::HTTP1_1);
        req.headers.insert("host".to_string(), "example.com".to_string());
        
        // Test that the request gets redirected to HTTPS
        let resp = vhost_handler.handle_request(&req);
        assert_eq!(resp.status, StatusCode::MovedPermanently);
        assert_eq!(resp.headers.get("Location").unwrap(), "https://example.com/index.html");
    }
        
        // Create a request with no Host header
        let req = Request::new(Method::GET, "/index.html".to_string());
        
        // Test that a 400 Bad Request response is returned
        let resp = server.handle_request(&req);
        assert_eq!(resp.status, StatusCode::BadRequest);
    }
}
```

## Enhancing Virtual Host Functionality

Now that we have a basic virtual host implementation, let's add some more advanced features:

### Implementing Advanced Hostname Matching

Let's enhance our virtual host system with more advanced hostname matching capabilities. We'll implement:

1. **Wildcard Subdomain Support**: Match any subdomain with a pattern like `*.example.com`
2. **Hierarchical Resolution**: Try to find the most specific match first
3. **Efficient Implementation**: Using a HashMap for O(1) lookup time

```rust
// src/config/mod.rs

impl Config {
    /// Get virtual host configuration based on the provided hostname
    /// Implements a sophisticated matching algorithm with these priorities:
    /// 1. Exact hostname match (example.com)
    /// 2. Exact wildcard subdomain match (*.example.com)
    /// 3. Default host as fallback
    pub fn get_virtual_host(&self, host: &str) -> &VirtualHostConfig {
        // Remove port if present (example.com:8080 -> example.com)
        let host = match host.split_once(':') {
            Some((name, _)) => name,
            None => host,
        };
        
        // Try exact match first (most specific and fastest)
        if let Some(vhost) = self.virtual_hosts.get(host) {
            debug!("Found exact hostname match for '{}'", host);
            return vhost;
        }
        
        // Try wildcard subdomain match for multi-level domains
        if let Some((_, domain_part)) = host.split_once('.') {
            let wildcard = format!("*.{}", domain_part);
            
            if let Some(vhost) = self.virtual_hosts.get(&wildcard) {
                debug!("Found wildcard match '{}' for hostname '{}'", wildcard, host);
                return vhost;
            }
            
            // For deeper subdomains (e.g., dev.api.example.com), try to match *.api.example.com
            // by recursively checking each level
            let mut parts: Vec<&str> = host.split('.').collect();
            if parts.len() > 2 {
                // Start removing the leftmost parts one by one to try matching with wildcards
                for i in 1..parts.len()-1 {
                    let partial_wildcard = format!("*.{}", parts[i..].join("."));
                    
                    if let Some(vhost) = self.virtual_hosts.get(&partial_wildcard) {
                        debug!("Found partial wildcard match '{}' for hostname '{}'", 
                               partial_wildcard, host);
                        return vhost;
                    }
                }
            }
        }
        
        // Fall back to default host if no match found
        debug!("No hostname match found for '{}', using default host", host);
        &self.default_host
    }
}
```

### Per-Host Custom Error Pages

We already included an `error_pages` field in our `VirtualHostConfig`, but let's update our handler to use it:

```rust
// src/server/vhost_handler.rs

impl VirtualHostHandler {
    // ...

    pub fn handle_error(&self, request: &Request, status: StatusCode) -> Response {
        let host = request.headers.get("host").unwrap_or_default();
        let vhost_config = self.config.get_virtual_host(host);
        
        // Check if there's a custom error page for this status code
        if let Some(error_page) = vhost_config.error_pages.get(&(status as u16)) {
            let mut path = vhost_config.document_root.clone();
            path.push(error_page);
            
            if path.exists() {
                // Try to serve the custom error page
                match std::fs::read_to_string(path) {
                    Ok(content) => {
                        return Response::new(status)
                            .with_content_type("text/html")
                            .with_body(&content);
                    },
                    Err(_) => {} // Fall back to default error page
                }
            }
        }
        
        // Fall back to default error response
        Response::new(status)
            .with_content_type("text/html")
            .with_body(&format!("<html><body><h1>{}</h1><p>{}</p></body></html>", 
                      status as u16, status.reason_phrase()))
    }
}
```

### Virtual Host Aliases

Let's add support for server name aliases, allowing multiple domain names to point to the same virtual host:

```rust
// src/config/mod.rs

#[derive(Debug, Clone)]
pub struct VirtualHostConfig {
    pub server_name: String,
    pub server_aliases: Vec<String>, // New field for aliases
    // ... other fields
}

impl Default for VirtualHostConfig {
    fn default() -> Self {
        Self {
            server_name: String::from("localhost"),
            server_aliases: Vec::new(),
            // ... other defaults
        }
    }
}

impl Config {
    // When loading the configuration, register aliases in the virtual hosts map
    pub fn register_virtual_host_aliases(&mut self) {
        let hosts = self.virtual_hosts.clone(); // Clone to avoid borrowing issues
        
        for (_, vhost) in hosts.iter() {
            for alias in &vhost.server_aliases {
                self.virtual_hosts.insert(alias.clone(), vhost.clone());
            }
        }
    }
}
```

## Performance Considerations

When implementing virtual hosts, consider these performance considerations:

1. **Lookup Efficiency**: Using a `HashMap` for virtual host lookup provides O(1) time complexity, making it very efficient even with many virtual hosts.

2. **Memory Usage**: Each virtual host configuration requires memory. For servers with hundreds of virtual hosts, you might want to use `Arc` for sharing common data between configurations.

3. **Host Header Parsing**: Parsing the `Host` header for each request adds a small overhead. This is generally negligible but could be optimized in high-performance scenarios.

4. **Wildcard Matching**: Our wildcard matching is simple but can be extended with a more sophisticated matcher if needed.

## Security Considerations for Virtual Hosts

Implementing virtual hosts introduces several security challenges that must be addressed to prevent vulnerabilities. Let's explore these security considerations in depth:

### 1. Host Header Validation and Injection Attacks

The `Host` header is a crucial component for virtual host routing, making it a potential attack vector:

```mermaid
flowchart TD
    A[Client Request] -->|"Host: evil.com"| B[Web Server]
    B -->|"Lookup virtual host"| C{Host Validation}
    C -->|"Valid but malicious"| D[Security breach]
    C -->|"Invalid format"| E[Reject request]
    C -->|"Unknown host"| F[Default host]
    
    style D fill:#f99,stroke:#333
    style E fill:#9f9,stroke:#333
```

**Risks:**
- **Host Header Injection**: Attackers may inject manipulated Host headers to access unintended resources or bypass security controls
- **Host Header Spoofing**: An attacker might forge headers to impersonate trusted domains
- **Cache Poisoning**: Combined with caching mechanisms, Host header attacks can lead to cache poisoning

**Mitigation Strategies:**
- Implement strict Host header validation (format, length, character set)
- Use whitelisting approach for known valid hostnames
- Ensure consistent handling of invalid or malformed Host headers
- For HTTP/1.1, always require the Host header
- Configure a secure default virtual host for unknown Host values

### 2. Directory Traversal and Path Security

Each virtual host has its own document root, creating multiple points where directory traversal vulnerabilities could occur:

**Risks:**
- **Path Traversal**: Attackers might exploit path traversal vulnerabilities (e.g., `../../../etc/passwd`) to access files outside the virtual host's document root
- **Cross-Host Information Leakage**: Poor isolation between virtual hosts could allow one compromised virtual host to access files from another

**Mitigation Strategies:**
- Normalize and validate all paths before use
- Use absolute paths for document roots, not relative paths
- Apply a chroot-like environment for each virtual host when possible
- Implement strict path security that prevents escaping the document root
- Use filesystem permissions as an additional layer of defense

### 3. TLS/SSL Certificate Management

With multiple domains on a single server, TLS certificate management becomes more complex:

**Risks:**
- **Certificate Mismatch**: Using the wrong certificate for a domain triggers browser warnings
- **SNI Requirements**: Older clients might not support Server Name Indication (SNI) needed for multiple certificates
- **Certificate Information Leakage**: Incorrect certificate configuration could reveal information about other hosted domains

**Mitigation Strategies:**
- Implement SNI (Server Name Indication) support for hosting multiple SSL certificates
- Use wildcard certificates or multi-domain certificates when appropriate
- Configure proper fallback behavior for non-SNI clients
- Regularly audit certificate configurations for all virtual hosts

### 4. Resource Isolation and DoS Prevention

Without proper resource limits, one virtual host could exhaust resources for all others:

**Risks:**
- **Resource Exhaustion**: A busy or compromised virtual host could consume all server resources
- **Denial of Service**: Attackers might target one virtual host to affect all others on the same server
- **Noisy Neighbor Problem**: High traffic to one virtual host impacts performance of others

**Mitigation Strategies:**
- Implement per-virtual-host resource limits (connections, bandwidth, CPU)
- Configure separate logging for each virtual host
- Monitor resource usage by virtual host
- Consider running high-security or high-value virtual hosts on separate physical servers

## Implementation Best Practices

Beyond the basic functionality, here are some best practices for implementing production-ready virtual hosting:

### 1. Efficient Configuration Management

As the number of virtual hosts increases, configuration management becomes crucial:

```rust
// Load virtual hosts configurations from a directory
pub fn load_virtual_hosts_from_directory(dir_path: &str) -> Result<Vec<VirtualHostConfig>> {
    let mut configs = Vec::new();
    let entries = fs::read_dir(dir_path)?;
    
    for entry in entries {
        let entry = entry?;
        let path = entry.path();
        
        if path.extension().map_or(false, |ext| ext == "toml") {
            let config = VirtualHostConfig::load_from_file(path.to_str().unwrap())?;
            configs.push(config);
        }
    }
    
    Ok(configs)
}
```

This approach allows:
- Each virtual host to have its own configuration file
- Hot-reloading of configurations without server restart
- Better organization for large-scale deployments

### 2. Hierarchical Configuration with Inheritance

Implement a configuration inheritance system to avoid repetition:

```rust
pub fn apply_defaults(&mut self, defaults: &VirtualHostDefaults) {
    // Only apply defaults to fields that haven't been explicitly set
    if self.index_files.is_empty() {
        self.index_files = defaults.index_files.clone();
    }
    
    if self.error_pages.is_empty() {
        self.error_pages = defaults.error_pages.clone();
    }
    
    // Apply other defaults as needed
}
```

### 3. Advanced Virtual Host Features

Consider these advanced features for real-world applications:

**1. Per-Host Access Control:**
```rust
pub struct VirtualHostConfig {
    // ...existing fields
    
    /// IP-based access restrictions for this virtual host
    pub allowed_ips: Option<Vec<IpRange>>,
    
    /// Authentication requirements
    pub auth_required: bool,
    pub auth_realm: String,
}
```

**2. Per-Host Middleware Configuration:**
```rust
pub struct VirtualHostConfig {
    // ...existing fields
    
    /// Custom middleware chain for this host
    pub middleware: Vec<Box<dyn Middleware>>,
    
    /// Custom response headers
    pub response_headers: HashMap<String, String>,
}
```

**3. Traffic Monitoring:**
```rust
pub struct VirtualHostStats {
    /// Requests per second
    pub requests_per_second: AtomicUsize,
    
    /// Response time histogram
    pub response_times: Histogram,
    
    /// Status code counts
    pub status_codes: HashMap<u16, AtomicUsize>,
}
```

### 4. Testing and Monitoring

Implement comprehensive testing for your virtual host system:

1. **Performance Testing:**
   - Measure response time and throughput for each virtual host
   - Test resource isolation between virtual hosts
   - Benchmark hostname matching performance

2. **Security Testing:**
   - Test with malformed Host headers
   - Attempt path traversal across virtual hosts
   - Verify proper SSL/TLS certificate handling

3. **Monitoring:**
   - Track per-virtual-host metrics
   - Set up alerts for suspicious activity
   - Monitor resource usage by virtual host

## Conclusion

You've now implemented a robust virtual host system for your Rust web server, enabling it to efficiently serve multiple websites from a single IP address. This feature dramatically enhances the server's versatility and brings it closer to production-ready capabilities.

Virtual hosting is a fundamental component of modern web servers, allowing efficient resource sharing while maintaining logical separation between websites. Our implementation includes:

- Flexible configuration system for defining multiple virtual hosts
- Efficient hostname matching with support for wildcards
- Host header validation and security protections
- Custom error pages and per-host configurations
- Support for HTTPS redirection on a per-host basis

In the next module, we'll build upon this foundation to implement reverse proxy functionality, which will allow our server to forward requests to other backend servers - a key feature of modern web servers like Nginx and Apache.

## Knowledge Check

Test your understanding of virtual hosting concepts:

1. **Question**: What HTTP header is essential for name-based virtual hosting?
   - A) `Content-Type`
   - B) `Host`
   - C) `Server`
   - D) `Connection`

2. **Question**: Which of the following is NOT a valid virtual host matching strategy?
   - A) Exact hostname matching
   - B) Wildcard subdomain matching
   - C) Regular expression matching
   - D) Protocol-based matching

3. **Question**: What happens when a request is received with an unknown Host header?
   - A) The request is immediately rejected with a 404 error
   - B) The server forwards the request to the first configured virtual host
   - C) The server uses the default virtual host configuration
   - D) The server responds with a 400 Bad Request error

4. **Question**: Which of these is a security risk associated with virtual hosting?
   - A) Host header injection attacks
   - B) Increased latency due to hostname matching
   - C) Higher memory consumption
   - D) Limited SSL/TLS support

5. **Question**: In HTTP/1.1, the Host header is:
   - A) Optional but recommended
   - B) Required by the specification
   - C) Only required for HTTPS
   - D) Only needed when connecting to non-standard ports

### Answers

<details>
<summary>Click to reveal answers</summary>

1. B) `Host`
2. D) Protocol-based matching
3. C) The server uses the default virtual host configuration
4. A) Host header injection attacks
5. B) Required by the specification
</details>

## Exercise for the Reader

1. Extend the virtual host system to support regex patterns for host matching
2. Add support for per-virtual-host rate limiting to prevent abuse
3. Implement path-based routing within a virtual host (e.g., `/api/` routes to a different handler)
4. Create a web-based admin interface for managing virtual host configurations
5. Add support for virtual host templates that can be instantiated with different parameters

## Additional Resources

### Official Documentation
- [HTTP Host header (MDN)](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Host)
- [RFC 7230 - HTTP/1.1: Message Syntax and Routing](https://tools.ietf.org/html/rfc7230)
- [RFC 2616 - Section 14.23: Host](https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.23)

### Web Server Virtual Host Implementations
- [Nginx Server Names](https://nginx.org/en/docs/http/server_names.html)
- [Apache Virtual Host documentation](https://httpd.apache.org/docs/2.4/vhosts/)
- [Caddy Host Matcher](https://caddyserver.com/docs/caddyfile/matchers#host)

### Security Resources
- [OWASP - Testing for Host Header Injection](https://owasp.org/www-project-web-security-testing-guide/latest/4-Web_Application_Security_Testing/07-Input_Validation_Testing/17-Testing_for_Host_Header_Injection)
- [PortSwigger - Host header attacks](https://portswigger.net/web-security/host-header)

### Rust Implementation Guides
- [Hyper Server Documentation](https://hyper.rs/guides/server/hello-world/)
- [Actix-web Virtual Hosting](https://actix.rs/docs/url-dispatch/)
- [Tokio Ecosystem and Web Servers](https://tokio.rs/)

## Navigation
- [Previous: Connection Pooling and HTTP Keep-Alive](08-connection-pooling.md)
- [Next: Reverse Proxy](10-reverse-proxy.md)
