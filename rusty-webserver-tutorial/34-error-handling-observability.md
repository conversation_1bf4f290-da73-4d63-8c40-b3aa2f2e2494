# Advanced Error Handling and Observability

## Learning Objectives
- Design and implement comprehensive error handling strategies for web applications
- Set up structured logging systems to enable effective debugging and monitoring
- Implement distributed tracing to visualize request flows through your system
- Configure metrics collection for performance monitoring and alerting
- Develop a holistic observability solution for production-grade web applications

## Prerequisites
- Understanding of Rust's error handling mechanisms (Result, Error traits)
- Completion of previous modules, especially the async programming module
- Familiarity with HTTP status codes and error responses
- Basic knowledge of monitoring concepts

## Navigation
- [Previous: Asynchronous Programming and Tokio](33-async-tokio.md)
- [Next: Custom Protocols and Extensibility](35-custom-protocols.md)

## Introduction

Building production-grade web applications requires more than just functional code—it demands systems that can be monitored, diagnosed, and maintained even under challenging conditions. In this module, we'll explore comprehensive error handling approaches and implement a robust observability stack that will help you identify and resolve issues before they affect your users.

## The Observability Triad

Effective observability encompasses three key components:

1. **Logging**: Detailed event records for debugging and audit trails
2. **Metrics**: Quantitative data for performance monitoring and alerting
3. **Tracing**: Request flow visualization across distributed systems

Together, these components provide a complete view of your application's behavior and health.

## Error Handling in Rust Web Applications

### The Error Handling Philosophy

Effective error handling in web applications should:

1. Provide clear information to developers for debugging
2. Safeguard sensitive information from exposure to users
3. Return appropriate HTTP status codes and error messages
4. Log sufficient context for post-mortem analysis
5. Enable automated monitoring and alerting

### Custom Error Types with thiserror

The `thiserror` crate provides a convenient way to define custom error types with minimal boilerplate:

```rust
use thiserror::Error;
use std::path::PathBuf;

#[derive(Error, Debug)]
pub enum ServerError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("HTTP parsing error: {0}")]
    HttpParse(String),
    
    #[error("Invalid request: {0}")]
    BadRequest(String),
    
    #[error("Resource not found: {path}")]
    NotFound { path: PathBuf },
    
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("Authentication failed")]
    Unauthorized,
    
    #[error("Permission denied: {resource}")]
    Forbidden { resource: String },
    
    #[error("Rate limit exceeded")]
    RateLimited { retry_after: Option<u64> },
    
    #[error("Internal server error: {0}")]
    Internal(String),
}

// Map errors to HTTP status codes
impl ServerError {
    pub fn status_code(&self) -> u16 {
        match self {
            ServerError::BadRequest(_) => 400,
            ServerError::Unauthorized => 401,
            ServerError::Forbidden { .. } => 403,
            ServerError::NotFound { .. } => 404,
            ServerError::RateLimited { .. } => 429,
            _ => 500,
        }
    }
    
    pub fn is_internal_error(&self) -> bool {
        matches!(self, 
            ServerError::Io(_) | 
            ServerError::Database(_) | 
            ServerError::Internal(_))
    }
}
```

### Error Context with anyhow

For application code, especially during development, the `anyhow` crate provides a simpler approach with rich context:

```rust
use anyhow::{Context, Result, bail, ensure};

fn read_config_file(path: &str) -> Result<Config> {
    let config_path = std::path::PathBuf::from(path);
    
    // Validate input
    ensure!(config_path.exists(), "Config file does not exist: {}", path);
    
    // Open file with context for better error messages
    let file = std::fs::File::open(&config_path)
        .with_context(|| format!("Failed to open config file: {}", path))?;
    
    // Parse file
    let config = serde_json::from_reader(file)
        .with_context(|| format!("Failed to parse config file: {}", path))?;
    
    // Validate configuration
    if !config.is_valid() {
        bail!("Invalid configuration in {}: missing required fields", path);
    }
    
    Ok(config)
}
```

### Combining thiserror and anyhow

For larger applications, a common pattern is to use:
- `thiserror` for public API/library errors
- `anyhow` for application-internal error handling

```rust
// Public-facing API uses thiserror
#[derive(Error, Debug)]
pub enum ApiError {
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    
    #[error("Resource not found")]
    NotFound,
    
    #[error("Internal error")]
    Internal, // Hide implementation details from API users
}

// Internal function uses anyhow for rich context
fn internal_operation() -> anyhow::Result<String> {
    // Complex operations with context-rich errors
    let data = fetch_data().context("Failed to fetch data")?;
    Ok(data)
}

// API endpoint converts anyhow errors to ApiError
pub fn api_endpoint(input: &str) -> Result<String, ApiError> {
    // Validate input
    if input.is_empty() {
        return Err(ApiError::InvalidInput("Input cannot be empty".into()));
    }
    
    // Call internal function and map errors
    internal_operation().map_err(|e| {
        log::error!("Internal error in api_endpoint: {:#}", e);
        ApiError::Internal
    })
}
```

## Error Response Patterns

Converting errors to appropriate HTTP responses is crucial for a good API experience:

```rust
async fn handle_request(req: Request<Body>) -> Result<Response<Body>, Infallible> {
    let response = match process_request(req).await {
        Ok(data) => {
            Response::builder()
                .status(200)
                .header("Content-Type", "application/json")
                .body(Body::from(serde_json::to_vec(&data).unwrap()))
                .unwrap()
        }
        Err(e) => {
            let status = e.status_code();
            
            // Construct appropriate error payload
            let error_payload = ErrorResponse {
                status: status,
                message: if e.is_internal_error() {
                    // Don't expose internal error details to clients
                    "Internal server error".to_string()
                } else {
                    // Safe to expose error details for client errors
                    e.to_string()
                },
                request_id: get_request_id(),
                code: error_code_for_type(&e),
            };
            
            // Log internal errors with full context
            if e.is_internal_error() {
                error!(
                    error = ?e,
                    status_code = status,
                    request_id = get_request_id(),
                    "Request processing failed with internal error"
                );
            }
            
            Response::builder()
                .status(status)
                .header("Content-Type", "application/json")
                .body(Body::from(serde_json::to_vec(&error_payload).unwrap()))
                .unwrap()
        }
    };
    
    Ok(response)
}
```

## Structured Logging

Traditional logging with simple text messages is insufficient for complex applications. Structured logging provides machine-readable records with rich context:

### Setting up tracing

```rust
use tracing::{info, error, warn, debug, instrument};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};

pub fn init_logging() {
    // Create formatting layer - can output as text or JSON
    let formatting_layer = tracing_subscriber::fmt::layer()
        .with_target(true)
        .json();
    
    // Create filter layer based on RUST_LOG env var
    let filter_layer = EnvFilter::try_from_default_env()
        .or_else(|_| EnvFilter::try_new("info"))
        .unwrap();
    
    // Combine layers and install as global subscriber
    tracing_subscriber::registry()
        .with(filter_layer)
        .with(formatting_layer)
        .init();
    
    info!("Logging initialized");
}
```

### Effective Use of Structured Logging

```rust
// Use instrument to automatically create spans
#[instrument(skip(password), fields(user_id, client_ip = %req.remote_addr()))]
async fn authenticate(username: &str, password: &str, req: &Request) -> Result<User, AuthError> {
    debug!("Processing authentication request");
    
    // Record timing information
    let timer = std::time::Instant::now();
    
    match check_credentials(username, password).await {
        Ok(user) => {
            // Add field to current span
            tracing::Span::current().record("user_id", &user.id);
            
            info!(
                user_id = user.id,
                elapsed_ms = timer.elapsed().as_millis(),
                "Authentication successful"
            );
            Ok(user)
        }
        Err(e) => {
            // Record detailed error information
            warn!(
                error.type = %std::any::type_name::<AuthError>(),
                error.message = %e,
                error.code = e.code(),
                attempt_count = get_attempt_count(username),
                elapsed_ms = timer.elapsed().as_millis(),
                "Authentication failed"
            );
            
            // Record metric for failed login
            metrics::FAILED_LOGINS.inc();
            
            Err(e)
        }
    }
}
```

### Log Collection and Aggregation

Configure your application to send logs to a centralized system:

```rust
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_appender::non_blocking::NonBlocking;
use tracing_subscriber::fmt::writer::MakeWriterExt;

fn configure_production_logging() {
    // Log to rolling files
    let file_appender = RollingFileAppender::new(
        Rotation::DAILY,
        "/var/log/app",
        "application.log",
    );
    let (non_blocking, _guard) = NonBlocking::new(file_appender);
    
    // Create JSON layer for structured logging
    let file_layer = tracing_subscriber::fmt::layer()
        .with_writer(non_blocking)
        .json();
    
    // Create console layer for human-readable output
    let stdout_layer = tracing_subscriber::fmt::layer()
        .pretty()
        .with_writer(std::io::stdout);
    
    // Initialize with both layers
    tracing_subscriber::registry()
        .with(EnvFilter::from_env("LOG_LEVEL"))
        .with(file_layer)
        .with(stdout_layer)
        .init();
    
    // Store guard in global state to prevent premature flush
    // GUARD.store(Box::new(_guard));
}
```

## Distributed Tracing with OpenTelemetry

Distributed tracing allows you to follow request flows across service boundaries:

```rust
use opentelemetry::global;
use opentelemetry::sdk::trace::{self, Sampler};
use opentelemetry::sdk::Resource;
use opentelemetry_otlp::WithExportConfig;
use tracing_subscriber::{layer::SubscriberExt, Registry};

fn init_telemetry() {
    // Configure OpenTelemetry to export to collector
    let tracer = opentelemetry_otlp::new_pipeline()
        .tracing()
        .with_exporter(
            opentelemetry_otlp::new_exporter()
                .tonic()
                .with_endpoint("http://otel-collector:4317"),
        )
        .with_trace_config(
            trace::config()
                .with_sampler(Sampler::AlwaysOn)
                .with_resource(Resource::new(vec![
                    opentelemetry::KeyValue::new("service.name", "rusty-webserver"),
                    opentelemetry::KeyValue::new("service.version", env!("CARGO_PKG_VERSION")),
                ])),
        )
        .install_batch(opentelemetry::runtime::Tokio)
        .expect("Failed to initialize OpenTelemetry tracer");

    // Create OpenTelemetry tracing layer
    let telemetry_layer = tracing_opentelemetry::layer().with_tracer(tracer);

    // Add the layer to the subscriber
    let subscriber = Registry::default()
        .with(EnvFilter::from_default_env())
        .with(tracing_subscriber::fmt::layer())
        .with(telemetry_layer);

    // Set as global default
    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set global subscriber");
}
```

### Propagating Context in HTTP Requests

```rust
use opentelemetry::propagation::Extractor;
use opentelemetry::global;
use opentelemetry::trace::{TraceContextExt, Span as OtelSpan};

// Extract trace context from incoming request headers
struct HeaderExtractor<'a>(&'a http::HeaderMap);

impl<'a> Extractor for HeaderExtractor<'a> {
    fn get(&self, key: &str) -> Option<&str> {
        self.0.get(key).and_then(|v| v.to_str().ok())
    }
    
    fn keys(&self) -> Vec<&str> {
        self.0.keys().map(|k| k.as_str()).collect()
    }
}

async fn handle_request(req: Request<Body>) -> Response<Body> {
    // Extract the OpenTelemetry context from the request
    let parent_cx = global::get_text_map_propagator(|prop| {
        prop.extract(&HeaderExtractor(req.headers()))
    });

    // Attach the extracted context to a new span
    let span = tracing::info_span!(
        "handle_request", 
        method = %req.method(),
        uri = %req.uri(),
        version = ?req.version(),
    );
    
    // Propagate the OpenTelemetry context to the span
    span.set_parent(parent_cx);
    
    // Execute the request handler within the span
    async move {
        // Process request...
        Response::new(Body::from("Hello, World!"))
    }.instrument(span).await
}
```

### Injecting Context into Outgoing Requests

```rust
use opentelemetry::propagation::Injector;
use reqwest::{Client, RequestBuilder};

// Inject trace context into outgoing request headers
struct HeaderInjector<'a>(&'a mut http::HeaderMap);

impl<'a> Injector for HeaderInjector<'a> {
    fn set(&mut self, key: &str, value: String) {
        if let Ok(header_name) = http::header::HeaderName::from_bytes(key.as_bytes()) {
            if let Ok(header_value) = http::header::HeaderValue::from_str(&value) {
                self.0.insert(header_name, header_value);
            }
        }
    }
}

async fn call_external_service(client: &Client, url: &str) -> Result<String, reqwest::Error> {
    let mut req = client.get(url).build()?;
    
    // Inject the current OpenTelemetry context into the request headers
    global::get_text_map_propagator(|propagator| {
        propagator.inject_context(
            &tracing::Span::current().context(),
            &mut HeaderInjector(req.headers_mut())
        )
    });
    
    // Send the request with propagated context
    let response = client.execute(req).await?;
    Ok(response.text().await?)
}
```

## Metrics with Prometheus

Metrics provide quantitative data for monitoring system health and performance:

```rust
use lazy_static::lazy_static;
use prometheus::{
    Counter, CounterVec, Histogram, HistogramOpts, HistogramVec, 
    Encoder, Registry, TextEncoder, Opts
};
use std::convert::Infallible;
use std::net::SocketAddr;
use hyper::{Body, Request, Response, Server};
use hyper::service::{make_service_fn, service_fn};

// Define metrics
lazy_static! {
    static ref REGISTRY: Registry = Registry::new();
    
    // Request counters
    static ref HTTP_REQUESTS_TOTAL: CounterVec = CounterVec::new(
        Opts::new("http_requests_total", "Total number of HTTP requests"),
        &["method", "path", "status"]
    ).expect("metric can be created");
    
    // Request duration
    static ref HTTP_REQUEST_DURATION: HistogramVec = HistogramVec::new(
        HistogramOpts::new(
            "http_request_duration_seconds",
            "HTTP request duration in seconds"
        )
        .buckets(vec![0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]),
        &["method", "path"]
    ).expect("metric can be created");
    
    // Connection metrics
    static ref ACTIVE_CONNECTIONS: Counter = Counter::new(
        "http_active_connections",
        "Number of active HTTP connections"
    ).expect("metric can be created");
    
    // Business metrics
    static ref USER_LOGINS_TOTAL: CounterVec = CounterVec::new(
        Opts::new("user_logins_total", "Total number of user logins"),
        &["success"]
    ).expect("metric can be created");
}

// Initialize metrics
fn init_metrics() {
    // Register all metrics with the registry
    REGISTRY.register(Box::new(HTTP_REQUESTS_TOTAL.clone())).expect("collector can be registered");
    REGISTRY.register(Box::new(HTTP_REQUEST_DURATION.clone())).expect("collector can be registered");
    REGISTRY.register(Box::new(ACTIVE_CONNECTIONS.clone())).expect("collector can be registered");
    REGISTRY.register(Box::new(USER_LOGINS_TOTAL.clone())).expect("collector can be registered");
}

// Metrics endpoint handler
async fn metrics_handler(_req: Request<Body>) -> Result<Response<Body>, Infallible> {
    let encoder = TextEncoder::new();
    let metric_families = REGISTRY.gather();
    let mut buffer = Vec::new();
    encoder.encode(&metric_families, &mut buffer).unwrap();
    
    Ok(Response::builder()
        .header("Content-Type", encoder.format_type())
        .body(Body::from(buffer))
        .unwrap())
}

// Example of using metrics in request processing
async fn handle_request(req: Request<Body>, router: &Router) -> Response<Body> {
    let start = std::time::Instant::now();
    let method = req.method().as_str();
    let path = router.route_pattern(&req).unwrap_or("unknown");
    
    // Increment active connections
    ACTIVE_CONNECTIONS.inc();
    
    // Process request
    let response = router.handle(req).await;
    
    // Record request duration
    let duration = start.elapsed().as_secs_f64();
    HTTP_REQUEST_DURATION
        .with_label_values(&[method, path])
        .observe(duration);
    
    // Count requests by status
    let status = response.status().as_u16().to_string();
    HTTP_REQUESTS_TOTAL
        .with_label_values(&[method, path, &status])
        .inc();
    
    // Decrement active connections
    ACTIVE_CONNECTIONS.dec();
    
    response
}

// Start metrics server
async fn start_metrics_server() {
    let addr = SocketAddr::from(([127, 0, 0, 1], 9091));
    
    let make_svc = make_service_fn(|_conn| async {
        Ok::<_, Infallible>(service_fn(metrics_handler))
    });

    let server = Server::bind(&addr).serve(make_svc);
    println!("Metrics server running on http://{}", addr);
    
    if let Err(e) = server.await {
        eprintln!("Metrics server error: {}", e);
    }
}

// In main function
#[tokio::main]
async fn main() {
    // Initialize metrics
    init_metrics();
    
    // Start metrics server in a separate task
    tokio::spawn(start_metrics_server());
    
    // Start main application
    // ...
}
```

## Health Checks and Liveness Probes

Implementing health checks helps container orchestrators like Kubernetes manage your application:

```rust
async fn health_handler(req: Request<Body>) -> Result<Response<Body>, Infallible> {
    let path = req.uri().path();
    
    match path {
        "/health/liveness" => {
            // Basic check: is the application running?
            Ok(Response::new(Body::from("{\"status\":\"UP\"}")))
        },
        "/health/readiness" => {
            // Can the application accept traffic?
            match check_database_connection().await {
                Ok(_) => Ok(Response::new(Body::from("{\"status\":\"READY\"}"))),
                Err(e) => {
                    warn!("Readiness check failed: {}", e);
                    let response = Response::builder()
                        .status(503)
                        .body(Body::from("{\"status\":\"NOT_READY\"}"))
                        .unwrap();
                    Ok(response)
                }
            }
        },
        "/health/startup" => {
            // Has the application finished startup procedures?
            if APP_STARTUP_COMPLETE.load(Ordering::SeqCst) {
                Ok(Response::new(Body::from("{\"status\":\"STARTED\"}")))
            } else {
                let response = Response::builder()
                    .status(503)
                    .body(Body::from("{\"status\":\"STARTING\"}"))
                    .unwrap();
                Ok(response)
            }
        },
        _ => {
            let response = Response::builder()
                .status(404)
                .body(Body::empty())
                .unwrap();
            Ok(response)
        }
    }
}

async fn check_database_connection() -> Result<(), anyhow::Error> {
    let conn = get_db_pool().get().await?;
    conn.execute("SELECT 1").await?;
    Ok(())
}
```

## Building a Complete Observability Stack

Combining all components into a comprehensive observability solution:

```rust
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};

pub async fn init_observability(
    service_name: &str,
    version: &str,
) -> Result<ObservabilityGuard, anyhow::Error> {
    // 1. Initialize metrics
    init_metrics();
    
    // 2. Configure OpenTelemetry exporter
    let tracer = opentelemetry_otlp::new_pipeline()
        .tracing()
        .with_exporter(
            opentelemetry_otlp::new_exporter()
                .tonic()
                .with_endpoint(std::env::var("OTEL_EXPORTER_OTLP_ENDPOINT")
                    .unwrap_or_else(|_| "http://localhost:4317".to_string())),
        )
        .with_trace_config(
            trace::config()
                .with_sampler(Sampler::ParentBased(Box::new(Sampler::TraceIdRatioBased(0.1))))
                .with_resource(Resource::new(vec![
                    opentelemetry::KeyValue::new("service.name", service_name.to_string()),
                    opentelemetry::KeyValue::new("service.version", version.to_string()),
                    opentelemetry::KeyValue::new("deployment.environment", 
                        std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string())),
                ])),
        )
        .install_batch(opentelemetry::runtime::Tokio)?;
    
    // 3. Create OpenTelemetry tracing layer
    let telemetry_layer = tracing_opentelemetry::layer().with_tracer(tracer);
    
    // 4. Create file logging layer
    let (file_writer, file_guard) = if let Ok(log_dir) = std::env::var("LOG_DIRECTORY") {
        let file_appender = tracing_appender::rolling::daily(log_dir, "application.log");
        let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);
        let file_layer = tracing_subscriber::fmt::layer()
            .with_writer(non_blocking)
            .json();
        (Some(file_layer), Some(guard))
    } else {
        (None, None)
    };
    
    // 5. Create console logging layer
    let console_layer = tracing_subscriber::fmt::layer()
        .with_target(true)
        .with_writer(std::io::stdout);
    
    // 6. Create filter layer
    let filter_layer = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| {
            EnvFilter::new(
                std::env::var("RUST_LOG")
                    .unwrap_or_else(|_| format!("{}=info", service_name)),
            )
        });
    
    // 7. Initialize tracing
    let registry = tracing_subscriber::registry()
        .with(filter_layer)
        .with(console_layer);
    
    // Add file layer if configured
    let registry = if let Some(layer) = file_writer {
        registry.with(layer)
    } else {
        registry
    };
    
    // Add telemetry layer
    registry.with(telemetry_layer).init();
    
    // 8. Start metrics server
    tokio::spawn(start_metrics_server());
    
    info!(
        service_name,
        version,
        "Observability stack initialized successfully"
    );
    
    // Return guard to keep file writer open
    Ok(ObservabilityGuard { _file_guard: file_guard })
}

pub struct ObservabilityGuard {
    _file_guard: Option<tracing_appender::non_blocking::WorkerGuard>,
}

impl Drop for ObservabilityGuard {
    fn drop(&mut self) {
        // Shutdown OpenTelemetry tracer provider
        opentelemetry::global::shutdown_tracer_provider();
    }
}
```

## Best Practices for Error Handling and Observability

1. **Error Design**:
   - Define domain-specific error types that map to HTTP status codes
   - Hide internal errors from users, but log complete details
   - Include unique error codes for automated processing

2. **Logging Best Practices**:
   - Use structured logging with contextual fields
   - Log at appropriate levels (debug, info, warn, error)
   - Include request IDs in all log messages for correlation
   - Avoid logging sensitive information (passwords, tokens)

3. **Metrics Collection**:
   - Track "Four Golden Signals": latency, traffic, errors, saturation
   - Set up alerts for abnormal patterns
   - Include business-relevant metrics (conversions, usage patterns)
   - Add labels/dimensions for better filtering

4. **Tracing Strategy**:
   - Use sampling for high-volume services (e.g., 10% of requests)
   - Ensure context propagation across service boundaries
   - Capture important events within request processing
   - Record relevant attributes on spans (user ID, transaction type)

5. **Integration**:
   - Ship logs to a centralized system (ELK, Loki)
   - Send metrics to Prometheus and visualize with Grafana
   - Export traces to Jaeger or Zipkin
   - Set up comprehensive dashboards showing all three observability aspects

## Knowledge Check

1. Which of these is NOT typically part of the observability triad?
   - a) Logging
   - b) Testing
   - c) Metrics
   - d) Tracing

2. When using custom error types with `thiserror`, what attribute do you use to automatically implement the `From` trait for error conversion?
   - a) `#[error]`
   - b) `#[from]`
   - c) `#[derive]`
   - d) `#[convert]`

3. What is the primary benefit of structured logging compared to traditional text logging?
   - a) It's faster to write
   - b) It requires less disk space
   - c) It provides machine-readable formats for better querying and analysis
   - d) It automatically fixes errors in your code

4. Which of these metrics is NOT considered one of the "Four Golden Signals" for monitoring?
   - a) Latency
   - b) Memory usage
   - c) Traffic
   - d) Error rate

5. What does distributed tracing help you understand?
   - a) The sequence of function calls within a single service
   - b) The flow of a request across multiple services and components
   - c) The error rates of your application
   - d) The memory usage patterns in your code

## Diagram

```
┌───────────────────────────────────────────────────┐
│                  User Request                     │
└────────────────────────┬──────────────────────────┘
                         │
┌────────────────────────▼──────────────────────────┐
│                 Application Layer                 │
│  ┌─────────────┐    ┌─────────┐   ┌───────────┐  │
│  │ Error       │    │ Request │   │ Response  │  │
│  │ Handling    │◄───┤ Handler ├───►Generation │  │
│  └──────┬──────┘    └─────────┘   └─────┬─────┘  │
└─────────┼──────────────────────────────┬─┴────────┘
          │                              │
┌─────────┼──────────────────────────────┼─────────┐
│         │     Observability Layer      │         │
│  ┌──────▼──────┐   ┌──────────┐   ┌────▼─────┐   │
│  │ Structured  │   │ Metrics  │   │ Tracing  │   │
│  │ Logging     │   │ Collector│   │ System   │   │
│  └──────┬──────┘   └────┬─────┘   └────┬─────┘   │
└─────────┼───────────────┼──────────────┼─────────┘
          │               │              │
┌─────────┼───────────────┼──────────────┼─────────┐
│ ┌───────▼──────┐  ┌─────▼─────┐  ┌─────▼──────┐  │
│ │ Log          │  │ Prometheus│  │ Jaeger/    │  │
│ │ Aggregation  │  │           │  │ Zipkin     │  │
│ └───────┬──────┘  └─────┬─────┘  └─────┬──────┘  │
│         │               │              │         │
│  ┌──────▼───────────────▼──────────────▼───────┐ │
│  │                 Grafana                     │ │
│  │ ┌─────────────┐ ┌───────────┐ ┌──────────┐ │ │
│  │ │ Log         │ │ Metrics   │ │ Tracing  │ │ │
│  │ │ Dashboards  │ │ Dashboards│ │ Views    │ │ │
│  │ └─────────────┘ └───────────┘ └──────────┘ │ │
│  └─────────────────────────────────────────────┘ │
│          Observability Infrastructure           │
└───────────────────────────────────────────────────┘
```

## Additional Resources

- [The thiserror crate documentation](https://docs.rs/thiserror)
- [The anyhow crate documentation](https://docs.rs/anyhow)
- [Structured Logging with tracing](https://tokio.rs/tokio/topics/tracing)
- [OpenTelemetry Rust SDK](https://github.com/open-telemetry/opentelemetry-rust)
- [Prometheus Rust Client](https://github.com/prometheus/client_rust)
- [Google SRE Book: Monitoring Distributed Systems](https://sre.google/sre-book/monitoring-distributed-systems/)
- [Grafana Dashboard Examples](https://grafana.com/grafana/dashboards/)
- [Practical Guide to SRE: Implementing SLOs](https://landing.google.com/sre/workbook/chapters/implementing-slos/)
- [OpenTelemetry in Practice](https://www.honeycomb.io/blog/opentelemetry-in-practice)
- [Rust Error Handling Best Practices](https://nick.groenen.me/posts/rust-error-handling/)
