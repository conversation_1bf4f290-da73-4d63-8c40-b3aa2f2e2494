<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\16-plugin-system.md -->
# Implementing a Plugin System

## Navigation
- [Previous: Production Deployment](15-production-deployment.md)
- [Next: WebSockets & Real-Time](17-websockets-realtime.md)

## Table of Contents
- [Web Server Concepts](#web-server-concepts)
- [Rust Concepts Introduced](#rust-concepts-introduced)
- [Plugin System Architecture](#plugin-system-architecture)
- [Step-by-Step Implementation](#step-by-step-implementation)
- [Configuration Example](#configuration-example)
- [Security Considerations](#security-considerations)
- [Rust Learning Summary](#rust-learning-summary)
- [Next Steps](#next-steps)
- [Conclusion](#conclusion)

This tutorial covers implementing an extensible plugin system for your Rust web server, allowing third-party code to extend server functionality without modifying the core codebase.

## Web Server Concepts

- **Plugin Architecture**: Supporting dynamic extension without modifying core code
- **Middleware Pipeline**: Request/response processing pipeline with plugin hooks
- **Extension Points**: Well-defined interfaces for plugins to hook into
- **Plugin Lifecycle**: Loading, initialization, and graceful shutdown

## Rust Concepts Introduced

### 1. Dynamic Dispatch with Trait Objects
```rust
pub trait RequestHandler: Send + Sync {
    fn handle(&self, request: &Request) -> Result<Response>;
}

type HandlerBox = Box<dyn RequestHandler>;
```

### 2. Dynamic Loading with `libloading`
```rust
use libloading::{Library, Symbol};

struct DynamicPlugin {
    library: Library,
    instance: Box<dyn Plugin>,
}
```

### 3. Type Erasure Pattern
```rust
pub struct AnyPlugin {
    inner: Box<dyn PluginInner>,
}

trait PluginInner: Send + Sync {
    fn name(&self) -> &str;
    fn on_request(&self, request: &mut Request) -> Result<()>;
    fn on_response(&self, response: &mut Response) -> Result<()>;
}
```

## Plugin System Architecture

```mermaid
flowchart TD
    subgraph Server[Server Core]
        PL[Plugin Loader]
        Reg[Plugin Registry]
        PL --> Reg
    end

    subgraph Pipeline[Request Pipeline]
        Pre[Pre-Processing]
        Route[Routing]
        Handle[Handler]
        Post[Post-Processing]
    end
    
    subgraph Plugins[Plugins]
        P1[Authentication Plugin]
        P2[Compression Plugin]
        P3[Analytics Plugin]
        P4[Custom Plugin]
    end
    
    Client((Client)) --> Pipeline
    Pipeline --> Client
    
    Reg --> Pre
    Reg --> Route
    Reg --> Handle
    Reg --> Post
    
    P1 --> Reg
    P2 --> Reg
    P3 --> Reg
    P4 --> Reg
```

## Step-by-Step Implementation

### 1. Define the Plugin Trait

Create a new file at `src/plugin/mod.rs`:

```rust
use std::any::Any;
use std::fmt::{Debug, Formatter};

use crate::error::Result;
use crate::http::{Request, Response};

/// Core plugin trait that all plugins must implement
pub trait Plugin: Send + Sync + Debug {
    /// Unique name of the plugin
    fn name(&self) -> &str;
    
    /// Called when the plugin is loaded
    fn on_load(&self) -> Result<()>;
    
    /// Called when the plugin is unloaded
    fn on_unload(&self) -> Result<()>;
    
    /// Called for each request before routing
    fn pre_process(&self, request: &mut Request) -> Result<()>;
    
    /// Called for each response before sending
    fn post_process(&self, response: &mut Response) -> Result<()>;
    
    /// Called when the server is shutting down
    fn on_shutdown(&self) -> Result<()>;
    
    /// Access the plugin as Any for downcasting
    fn as_any(&self) -> &dyn Any;
}

// Helper for debugging plugins
impl Debug for dyn Plugin {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "Plugin {{ name: {} }}", self.name())
    }
}

// Implement plugin for a Box<dyn Plugin>
impl Plugin for Box<dyn Plugin> {
    fn name(&self) -> &str {
        (**self).name()
    }
    
    fn on_load(&self) -> Result<()> {
        (**self).on_load()
    }
    
    fn on_unload(&self) -> Result<()> {
        (**self).on_unload()
    }
    
    fn pre_process(&self, request: &mut Request) -> Result<()> {
        (**self).pre_process(request)
    }
    
    fn post_process(&self, response: &mut Response) -> Result<()> {
        (**self).post_process(response)
    }
    
    fn on_shutdown(&self) -> Result<()> {
        (**self).on_shutdown()
    }
    
    fn as_any(&self) -> &dyn Any {
        (**self).as_any()
    }
}
```

### 2. Create a Plugin Registry

Add a registry to manage loaded plugins in `src/plugin/registry.rs`:

```rust
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use log::{debug, error, info};

use crate::error::{Result, ServerError};
use crate::plugin::Plugin;

/// Registry for server plugins
pub struct PluginRegistry {
    /// All registered plugins
    plugins: RwLock<HashMap<String, Arc<dyn Plugin>>>,
}

impl PluginRegistry {
    /// Create a new empty plugin registry
    pub fn new() -> Self {
        Self {
            plugins: RwLock::new(HashMap::new()),
        }
    }
    
    /// Register a new plugin
    pub fn register(&self, plugin: Arc<dyn Plugin>) -> Result<()> {
        let name = plugin.name().to_string();
        let mut plugins = self.plugins.write().map_err(|_| {
            ServerError::Plugin(format!("Failed to acquire write lock for registering plugin: {}", name))
        })?;
        
        // Check if a plugin with this name is already registered
        if plugins.contains_key(&name) {
            return Err(ServerError::Plugin(format!("Plugin already registered: {}", name)));
        }
        
        // Initialize the plugin
        plugin.on_load()?;
        
        // Register the plugin
        plugins.insert(name.clone(), plugin);
        info!("Plugin registered: {}", name);
        
        Ok(())
    }
    
    /// Unregister a plugin by name
    pub fn unregister(&self, name: &str) -> Result<()> {
        let mut plugins = self.plugins.write().map_err(|_| {
            ServerError::Plugin(format!("Failed to acquire write lock for unregistering plugin: {}", name))
        })?;
        
        if let Some(plugin) = plugins.remove(name) {
            // Clean up the plugin
            plugin.on_unload()?;
            info!("Plugin unregistered: {}", name);
            Ok(())
        } else {
            Err(ServerError::Plugin(format!("Plugin not found: {}", name)))
        }
    }
    
    /// Get a plugin by name
    pub fn get(&self, name: &str) -> Option<Arc<dyn Plugin>> {
        if let Ok(plugins) = self.plugins.read() {
            plugins.get(name).cloned()
        } else {
            None
        }
    }
    
    /// Process a request through all plugins' pre-process hook
    pub fn pre_process(&self, request: &mut Request) -> Result<()> {
        if let Ok(plugins) = self.plugins.read() {
            for plugin in plugins.values() {
                if let Err(e) = plugin.pre_process(request) {
                    error!("Plugin '{}' pre-processing error: {}", plugin.name(), e);
                    return Err(e);
                }
            }
        }
        Ok(())
    }
    
    /// Process a response through all plugins' post-process hook
    pub fn post_process(&self, response: &mut Response) -> Result<()> {
        if let Ok(plugins) = self.plugins.read() {
            for plugin in plugins.values() {
                if let Err(e) = plugin.post_process(response) {
                    error!("Plugin '{}' post-processing error: {}", plugin.name(), e);
                    return Err(e);
                }
            }
        }
        Ok(())
    }
    
    /// Shutdown all plugins
    pub fn shutdown(&self) -> Result<()> {
        if let Ok(plugins) = self.plugins.read() {
            for plugin in plugins.values() {
                if let Err(e) = plugin.on_shutdown() {
                    error!("Plugin '{}' shutdown error: {}", plugin.name(), e);
                }
            }
        }
        Ok(())
    }
}
```

### 3. Add Dynamic Plugin Loading

Create a plugin loader in `src/plugin/loader.rs`:

```rust
use std::path::Path;
use std::sync::Arc;

use libloading::{Library, Symbol};
use log::{debug, error, info};

use crate::error::{Result, ServerError};
use crate::plugin::{Plugin, PluginRegistry};

type PluginCreate = unsafe fn() -> *mut dyn Plugin;

/// Handles loading plugins from dynamic libraries
pub struct PluginLoader {
    registry: Arc<PluginRegistry>,
    loaded_libraries: Vec<(String, Library)>,
}

impl PluginLoader {
    /// Create a new plugin loader
    pub fn new(registry: Arc<PluginRegistry>) -> Self {
        Self {
            registry,
            loaded_libraries: Vec::new(),
        }
    }
    
    /// Load a plugin from a dynamic library file
    pub fn load<P: AsRef<Path>>(&mut self, path: P) -> Result<()> {
        let path = path.as_ref();
        let path_str = path.to_string_lossy();
        
        // Try to load the library
        let lib = unsafe {
            Library::new(path).map_err(|e| 
                ServerError::Plugin(format!("Failed to load plugin '{}': {}", path_str, e))
            )?
        };
        
        // Look for the plugin creation function
        let constructor: Symbol<PluginCreate> = unsafe {
            lib.get(b"_create_plugin").map_err(|e| 
                ServerError::Plugin(format!("Failed to find plugin constructor in '{}': {}", path_str, e))
            )?
        };
        
        // Create the plugin instance
        let plugin = unsafe {
            let raw = constructor();
            if raw.is_null() {
                return Err(ServerError::Plugin(format!("Plugin constructor returned null for '{}'", path_str)));
            }
            Box::from_raw(raw)
        };
        
        // Register the plugin
        let name = plugin.name().to_string();
        self.registry.register(Arc::new(plugin))?;
        
        // Remember the library to keep it loaded
        self.loaded_libraries.push((name, lib));
        
        info!("Successfully loaded plugin from '{}'", path_str);
        Ok(())
    }
    
    /// Unload all dynamically loaded plugins
    pub fn unload_all(&mut self) -> Result<()> {
        for (name, _) in self.loaded_libraries.drain(..) {
            if let Err(e) = self.registry.unregister(&name) {
                error!("Error unregistering plugin '{}': {}", name, e);
            }
        }
        Ok(())
    }
}

impl Drop for PluginLoader {
    fn drop(&mut self) {
        if let Err(e) = self.unload_all() {
            error!("Error unloading plugins: {}", e);
        }
    }
}
```

### 4. Update Server to Use Plugins

Modify the server to integrate the plugin system:

```rust
use std::sync::Arc;

use crate::config::Config;
use crate::plugin::{PluginLoader, PluginRegistry};

pub struct Server {
    // ...existing fields...
    
    /// Plugin registry
    plugin_registry: Arc<PluginRegistry>,
    
    /// Plugin loader
    plugin_loader: PluginLoader,
}

impl Server {
    pub fn new(config: Config) -> Result<Self> {
        let plugin_registry = Arc::new(PluginRegistry::new());
        let plugin_loader = PluginLoader::new(plugin_registry.clone());
        
        // ...existing initialization...
        
        Ok(Self {
            // ...existing fields...
            plugin_registry,
            plugin_loader,
        })
    }
    
    // Add plugins to the request processing pipeline
    pub fn handle_request(&self, request: &Request) -> Result<Response> {
        // Clone the request for mutation
        let mut req = request.clone();
        
        // Run pre-processing plugins
        if let Err(e) = self.plugin_registry.pre_process(&mut req) {
            return Err(e);
        }
        
        // Process the request normally
        let mut response = self.process_request(&req)?;
        
        // Run post-processing plugins
        if let Err(e) = self.plugin_registry.post_process(&mut response) {
            return Err(e);
        }
        
        Ok(response)
    }
    
    // Load plugins from configuration
    pub fn load_plugins(&mut self) -> Result<()> {
        for plugin_path in &self.config.plugins {
            if let Err(e) = self.plugin_loader.load(plugin_path) {
                error!("Failed to load plugin '{}': {}", plugin_path, e);
            }
        }
        Ok(())
    }
}
```

### 5. Create a Built-in Plugin Example

Add a simple built-in plugin at `src/plugin/builtin/logger.rs`:

```rust
use std::any::Any;

use log::info;

use crate::error::Result;
use crate::http::{Request, Response};
use crate::plugin::Plugin;

/// A simple logger plugin
#[derive(Debug)]
pub struct LoggerPlugin;

impl LoggerPlugin {
    pub fn new() -> Self {
        Self
    }
}

impl Plugin for LoggerPlugin {
    fn name(&self) -> &str {
        "builtin:logger"
    }
    
    fn on_load(&self) -> Result<()> {
        info!("Logger plugin loaded");
        Ok(())
    }
    
    fn on_unload(&self) -> Result<()> {
        info!("Logger plugin unloaded");
        Ok(())
    }
    
    fn pre_process(&self, request: &mut Request) -> Result<()> {
        info!("Request: {} {}", request.method, request.path);
        Ok(())
    }
    
    fn post_process(&self, response: &mut Response) -> Result<()> {
        info!("Response: {}", response.status);
        Ok(())
    }
    
    fn on_shutdown(&self) -> Result<()> {
        info!("Logger plugin shutting down");
        Ok(())
    }
    
    fn as_any(&self) -> &dyn Any {
        self
    }
}
```

### 6. Create a Plugin Template for External Plugins

Create a template for third-party plugins:

```rust
// External plugin project structure:
//
// my_plugin/
// ├── Cargo.toml
// └── src/
//     └── lib.rs

// Example lib.rs:
use std::any::Any;

// Import the plugin trait from your web server crate
// You would publish this as a separate crate for plugin authors
use rusty_server_plugin_api::{Plugin, Request, Response, Result};

#[derive(Debug)]
pub struct MyPlugin {
    config: String,
}

impl MyPlugin {
    fn new(config: String) -> Self {
        Self {
            config,
        }
    }
}

impl Plugin for MyPlugin {
    fn name(&self) -> &str {
        "my_custom_plugin"
    }
    
    fn on_load(&self) -> Result<()> {
        println!("MyPlugin loaded with config: {}", self.config);
        Ok(())
    }
    
    fn on_unload(&self) -> Result<()> {
        println!("MyPlugin unloaded");
        Ok(())
    }
    
    fn pre_process(&self, request: &mut Request) -> Result<()> {
        // Add custom header
        request.headers.insert("X-Processed-By".to_string(), "my_custom_plugin".to_string());
        Ok(())
    }
    
    fn post_process(&self, response: &mut Response) -> Result<()> {
        // Add custom header to response
        response.headers.insert("X-Processed-By".to_string(), "my_custom_plugin".to_string());
        Ok(())
    }
    
    fn on_shutdown(&self) -> Result<()> {
        println!("MyPlugin shutting down");
        Ok(())
    }
    
    fn as_any(&self) -> &dyn Any {
        self
    }
}

// Export the plugin creation function
#[no_mangle]
pub extern "C" fn _create_plugin() -> *mut dyn Plugin {
    // Create the plugin with some default configuration
    let plugin = MyPlugin::new("default_config".to_string());
    
    // Convert to raw pointer, ownership is transferred to the caller
    Box::into_raw(Box::new(plugin))
}
```

## Advanced Rust Concepts Explained

### 1. Dynamic Dispatch and Trait Objects

Trait objects allow for dynamic dispatch, which is essential for plugins:

```rust
// Static dispatch (known at compile time)
fn process_static<T: Plugin>(plugin: &T) {
    plugin.pre_process(&mut request);
}

// Dynamic dispatch (determined at runtime)
fn process_dynamic(plugin: &dyn Plugin) {
    plugin.pre_process(&mut request);
}
```

Key aspects:
- **Trait Objects**: References to types implementing a specific trait
- **vtable**: Compiler-generated lookup table for method dispatch
- **Dynamic Dispatch**: Method resolution at runtime, not compile time
- **Performance**: Slight overhead compared to static dispatch, but enables extensibility

### 2. Foreign Function Interface (FFI)

The `libloading` crate allows loading dynamic libraries at runtime:

```rust
// Safety considerations when working with FFI:
unsafe {
    let lib = Library::new(path)?;
    let symbol: Symbol<PluginCreator> = lib.get(b"_create_plugin")?;
    let plugin_ptr = symbol();
    let plugin = Box::from_raw(plugin_ptr);
}
```

Important concepts:
- **Safety Boundary**: `unsafe` code must guarantee invariants
- **Symbol Loading**: Finding functions in dynamic libraries
- **Memory Management**: Transferring ownership across FFI boundary
- **ABI Compatibility**: Ensuring consistent calling conventions

### 3. Type Erasure Pattern

Type erasure hides concrete types behind trait interfaces:

```rust
pub struct AnyPlugin {
    inner: Box<dyn PluginInner>,
}

impl Plugin for AnyPlugin {
    fn name(&self) -> &str {
        self.inner.name()
    }
    
    // Other methods delegated to inner
}

// Create any plugin from compatible type
impl<T: Plugin + 'static> From<T> for AnyPlugin {
    fn from(plugin: T) -> Self {
        AnyPlugin {
            inner: Box::new(plugin),
        }
    }
}
```

Key benefits:
- **Abstraction**: Hide implementation details
- **Flexibility**: Different implementations behind same interface
- **Extensibility**: Add new plugin types without changing core code

## Plugin Lifecycle Flow

```mermaid
stateDiagram-v2
    [*] --> Loading
    Loading --> Loaded: on_load()
    Loaded --> Processing: server running
    Processing --> Processing: pre_process()/post_process()
    Processing --> Unloading: server stopping
    Unloading --> Unloaded: on_unload()
    Unloaded --> [*]: on_shutdown()
```

## Testing Plugin System

### 1. Unit Tests for Plugin Registry

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    // A simple test plugin
    #[derive(Debug)]
    struct TestPlugin {
        name: String,
        pre_called: std::cell::Cell<bool>,
        post_called: std::cell::Cell<bool>,
    }
    
    impl Plugin for TestPlugin {
        fn name(&self) -> &str {
            &self.name
        }
        
        fn pre_process(&self, _: &mut Request) -> Result<()> {
            self.pre_called.set(true);
            Ok(())
        }
        
        fn post_process(&self, _: &mut Response) -> Result<()> {
            self.post_called.set(true);
            Ok(())
        }
        
        // Other required methods...
    }
    
    #[test]
    fn test_plugin_registry() {
        let registry = PluginRegistry::new();
        let plugin = TestPlugin {
            name: "test".to_string(),
            pre_called: std::cell::Cell::new(false),
            post_called: std::cell::Cell::new(false),
        };
        
        // Register the plugin
        registry.register(Arc::new(plugin)).unwrap();
        
        // Get the plugin
        let retrieved = registry.get("test").unwrap();
        assert_eq!(retrieved.name(), "test");
        
        // Test pipeline
        let mut req = Request::new(Method::GET, "/test".to_string());
        let mut res = Response::new(StatusCode::OK);
        
        registry.pre_process(&mut req).unwrap();
        registry.post_process(&mut res).unwrap();
        
        // Check if methods were called
        let test_plugin = retrieved.as_any().downcast_ref::<TestPlugin>().unwrap();
        assert!(test_plugin.pre_called.get());
        assert!(test_plugin.post_called.get());
    }
}
```

### 2. Integration Tests with Mock Plugins

```rust
#[test]
fn test_server_with_plugins() {
    // Create a server with mock plugins
    let config = Config::default();
    let mut server = Server::new(config).unwrap();
    
    // Register a built-in plugin
    let logger = LoggerPlugin::new();
    server.plugin_registry.register(Arc::new(logger)).unwrap();
    
    // Create and process a request
    let request = Request::new(Method::GET, "/test".to_string());
    let response = server.handle_request(&request).unwrap();
    
    // Verify the result
    assert_eq!(response.status, StatusCode::OK);
    // Check for plugin-added headers, etc.
}
```

## Configuration Example

```toml
# In config.toml
[server]
# ...existing config...

[plugins]
# Built-in plugins
enabled_builtin = ["logger", "compression", "rate_limiter"]

# External plugin libraries
external = [
    "/path/to/plugin1.so",
    "/path/to/plugin2.so"
]

# Plugin-specific configuration
[plugins.compression]
level = 6
min_size = 1024

[plugins.rate_limiter]
requests_per_minute = 60
burst = 10
```

## Security Considerations

Plugin systems introduce unique security challenges, as they allow external code to execute within your web server. Implementing robust security measures is critical to prevent exploitation by malicious plugins or unexpected behavior.

### Plugin System Threat Model

Before implementing a plugin system, understand the primary threats and attack vectors:

```mermaid
flowchart TD
    A[Attacker] -->|1. Supply malicious plugin| PS[Plugin System]
    A -->|2. Exploit vulnerabilities in legitimate plugins| PS
    A -->|3. Supply malicious plugin configuration| PS
    A -->|4. Escalate privileges through plugins| PS
    A -->|5. Data exfiltration via plugins| PS
    A -->|6. Denial of service through resource exhaustion| PS
    
    PS -->|Executes| S[Server Core]
    PS -->|Accesses| D[(Server Data)]
    PS -->|Modifies| R[Responses]
    PS -->|Intercepts| Q[Requests]
    
    class A fill:#f96,stroke:#333
    class PS fill:#69f,stroke:#333
    class S,D,R,Q fill:#6d9,stroke:#333
```

### Plugin Isolation and Sandboxing

Implementing proper isolation boundaries ensures that plugins cannot negatively impact your server's stability or security:

```rust
/// Safe plugin execution environment with resource limits
pub struct PluginSandbox {
    // Memory usage limits
    memory_limit_kb: usize,
    // Max CPU time in milliseconds
    cpu_time_limit_ms: u64,
    // File system access restrictions
    allowed_paths: Vec<PathBuf>,
    // Network access restrictions
    allowed_hosts: Vec<String>,
    // Context for the sandbox
    context: SandboxContext,
}

impl PluginSandbox {
    /// Create a new sandbox with default restrictions
    pub fn new() -> Self {
        Self {
            memory_limit_kb: 10_000, // 10MB
            cpu_time_limit_ms: 100,  // 100ms
            allowed_paths: vec![],   // No FS access by default
            allowed_hosts: vec![],   // No network access by default
            context: SandboxContext::default(),
        }
    }
    
    /// Execute a plugin function within the sandbox
    pub fn execute<F, R>(&self, plugin_id: &str, func: F) -> Result<R> 
    where 
        F: FnOnce() -> Result<R> + Send + 'static,
        R: Send + 'static 
    {
        // Set up resource monitoring
        let start_time = std::time::Instant::now();
        let plugin_id = plugin_id.to_string();
        
        // Create a thread for the plugin with its own stack limit
        let result = std::thread::Builder::new()
            .name(format!("plugin-{}", plugin_id))
            .stack_size(self.memory_limit_kb * 1024)
            .spawn(move || {
                // Set up alarm for CPU time limit
                #[cfg(unix)]
                {
                    use std::time::Duration;
                    signal_hook::low_level::alarm(Duration::from_millis(self.cpu_time_limit_ms));
                }
                
                // Execute the plugin function
                func()
            })?
            .join()
            .map_err(|_| ServerError::Plugin(format!("Plugin {} panicked during execution", plugin_id)))?;
            
        // Check execution time
        let execution_time = start_time.elapsed().as_millis();
        if execution_time > self.cpu_time_limit_ms as u128 {
            log::warn!("Plugin {} exceeded CPU time limit: {}ms", plugin_id, execution_time);
        }
        
        result
    }
}
```

### Plugin Verification and Code Signing

Implement a robust verification system to ensure plugins come from trusted sources:

```rust
/// Plugin signature verification
pub struct PluginVerifier {
    // Trusted public keys
    trusted_keys: Vec<PublicKey>,
    // Verify plugin signatures before loading
    enforce_signatures: bool,
}

impl PluginVerifier {
    /// Create a new plugin verifier with trusted keys
    pub fn new(trusted_keys: Vec<PublicKey>) -> Self {
        Self {
            trusted_keys,
            enforce_signatures: true,
        }
    }
    
    /// Verify a plugin's digital signature
    pub fn verify_plugin(&self, plugin_path: &Path) -> Result<bool> {
        // Skip verification if not enforced
        if !self.enforce_signatures {
            return Ok(true);
        }
        
        // Read the plugin file
        let plugin_data = std::fs::read(plugin_path)?;
        
        // Extract the signature (assuming it's appended to the file)
        let signature_size = 512; // Example size for ed25519 signatures
        if plugin_data.len() <= signature_size {
            return Err(ServerError::Plugin(format!(
                "Plugin file too small: {:?}", plugin_path
            )));
        }
        
        let (plugin_code, signature_bytes) = plugin_data.split_at(plugin_data.len() - signature_size);
        
        // Verify with each trusted key
        for key in &self.trusted_keys {
            if key.verify(plugin_code, signature_bytes).is_ok() {
                log::info!("Plugin signature verified: {:?}", plugin_path);
                return Ok(true);
            }
        }
        
        // No keys matched
        log::warn!("Plugin signature verification failed: {:?}", plugin_path);
        Err(ServerError::Plugin(format!(
            "Plugin signature verification failed: {:?}", plugin_path
        )))
    }
}
```

### Secure Plugin Loading Process

Implement a secure loading process to prevent unauthorized plugins from executing:

```rust
/// Safe plugin loader with security checks
pub struct SecurePluginLoader {
    // Plugin directory
    plugin_dir: PathBuf,
    // Plugin verifier for signatures
    verifier: PluginVerifier,
    // Sandbox for plugin execution
    sandbox: PluginSandbox,
    // Plugin allowlist (only these plugins can be loaded)
    allowed_plugins: HashSet<String>,
    // Capability system
    capability_manager: PluginCapabilityManager,
}

impl SecurePluginLoader {
    /// Load a plugin with security checks
    pub fn load_plugin(&self, plugin_name: &str) -> Result<Box<dyn Plugin>> {
        // Check if plugin is allowed
        if !self.allowed_plugins.contains(plugin_name) {
            return Err(ServerError::Plugin(format!(
                "Plugin {} is not in the allowlist", plugin_name
            )));
        }
        
        // Build the full plugin path
        let plugin_path = self.plugin_dir.join(format!("{}.so", plugin_name));
        
        // Verify the plugin signature
        self.verifier.verify_plugin(&plugin_path)?;
        
        // Perform security scan on the plugin file
        self.scan_plugin(&plugin_path)?;
        
        // Load the plugin in a sandbox
        self.sandbox.execute(plugin_name, || {
            unsafe {
                // Loading a dynamic library is unsafe
                let lib = Library::new(&plugin_path)?;
                
                // Get the plugin create function
                let constructor: Symbol<unsafe extern "C" fn() -> *mut dyn Plugin> = 
                    lib.get(b"create_plugin")?;
                    
                // Call the constructor
                let raw_plugin = constructor();
                let plugin = Box::from_raw(raw_plugin);
                
                Ok(plugin)
            }
        })
    }
    
    /// Scan plugin binary for known security issues
    fn scan_plugin(&self, plugin_path: &Path) -> Result<()> {
        // Example implementation would scan the binary for:
        // 1. Known malware signatures
        // 2. Suspicious imports or symbols
        // 3. Dangerous system call patterns
        
        log::info!("Scanning plugin: {:?}", plugin_path);
        
        // In a real implementation, you might use tools like
        // ClamAV, Yara rules, or custom binary analysis
        
        Ok(())
    }
}
```

### Plugin Capability Management

Implement a capability-based security model to control plugin access to server resources:

```rust
/// Plugin capability types
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Capability {
    // Access to file system
    FileSystemRead(PathBuf),
    FileSystemWrite(PathBuf),
    
    // Network access
    NetworkConnect(String), // Host pattern
    NetworkListen(u16),     // Port number
    
    // Server API access
    ConfigurationRead,
    ConfigurationWrite,
    RequestIntercept,
    ResponseModify,
    HeadersModify,
    
    // Server state
    StateRead,
    StateWrite,
    
    // Administrative actions
    ServerControl,
    PluginManagement,
}

/// Manages plugin capabilities
pub struct PluginCapabilityManager {
    // Capabilities assigned to each plugin
    plugin_capabilities: HashMap<String, HashSet<Capability>>,
    // Default capabilities for new plugins
    default_capabilities: HashSet<Capability>,
}

impl PluginCapabilityManager {
    /// Check if a plugin has a specific capability
    pub fn has_capability(&self, plugin_id: &str, capability: &Capability) -> bool {
        if let Some(capabilities) = self.plugin_capabilities.get(plugin_id) {
            // Direct capability match
            if capabilities.contains(capability) {
                return true;
            }
            
            // Path-based capability checks for filesystem access
            match capability {
                Capability::FileSystemRead(path) => {
                    for cap in capabilities {
                        if let Capability::FileSystemRead(allowed_path) = cap {
                            if path.starts_with(allowed_path) {
                                return true;
                            }
                        }
                    }
                }
                Capability::FileSystemWrite(path) => {
                    for cap in capabilities {
                        if let Capability::FileSystemWrite(allowed_path) = cap {
                            if path.starts_with(allowed_path) {
                                return true;
                            }
                        }
                    }
                }
                Capability::NetworkConnect(host) => {
                    for cap in capabilities {
                        if let Capability::NetworkConnect(allowed_host) = cap {
                            if host == allowed_host || 
                               (allowed_host.starts_with('*') && 
                                host.ends_with(&allowed_host[1..])) {
                                return true;
                            }
                        }
                    }
                }
                _ => {}
            }
        }
        
        false
    }
    
    /// Enforce capability check and throw error if not allowed
    pub fn enforce_capability(&self, plugin_id: &str, capability: &Capability) -> Result<()> {
        if !self.has_capability(plugin_id, capability) {
            return Err(ServerError::Plugin(format!(
                "Plugin {} lacks required capability: {:?}", plugin_id, capability
            )));
        }
        
        Ok(())
    }
}
```

### Secure Plugin Configuration

Implement secure handling of plugin configuration to prevent misuse:

```rust
/// Plugin configuration sanitizer
pub struct PluginConfigSanitizer {
    // Max allowed configuration size
    max_config_size_kb: usize,
    // Allowed configuration types
    allowed_types: HashSet<String>,
    // Configuration validators
    validators: HashMap<String, Box<dyn Fn(&Value) -> bool + Send + Sync>>,
}

impl PluginConfigSanitizer {
    /// Sanitize plugin configuration
    pub fn sanitize_config(&self, plugin_id: &str, config: &Value) -> Result<Value> {
        // Check config size
        let config_str = serde_json::to_string(config)?;
        if config_str.len() > self.max_config_size_kb * 1024 {
            return Err(ServerError::Plugin(format!(
                "Plugin {} configuration exceeds size limit", plugin_id
            )));
        }
        
        // Deep copy to avoid mutations
        let mut sanitized = config.clone();
        
        // Recursively sanitize the configuration
        self.sanitize_value(&mut sanitized)?;
        
        // Run plugin-specific validator if available
        if let Some(validator) = self.validators.get(plugin_id) {
            if !validator(&sanitized) {
                return Err(ServerError::Plugin(format!(
                    "Plugin {} configuration failed validation", plugin_id
                )));
            }
        }
        
        Ok(sanitized)
    }
    
    /// Sanitize a single configuration value
    fn sanitize_value(&self, value: &mut Value) -> Result<()> {
        match value {
            Value::Object(map) => {
                // Remove disallowed keys
                map.retain(|key, _| {
                    let keep = !key.starts_with("__") && !key.contains("../");
                    if !keep {
                        log::warn!("Removed suspicious config key: {}", key);
                    }
                    keep
                });
                
                // Recursively sanitize all values
                for (_, v) in map.iter_mut() {
                    self.sanitize_value(v)?;
                }
            }
            Value::Array(arr) => {
                // Recursively sanitize array elements
                for v in arr.iter_mut() {
                    self.sanitize_value(v)?;
                }
            }
            Value::String(s) => {
                // Remove potentially harmful content from strings
                if s.contains("../") || s.contains("javascript:") || s.contains("<script") {
                    *s = s.replace("../", "")
                         .replace("javascript:", "")
                         .replace("<script", "&lt;script");
                    
                    log::warn!("Sanitized suspicious config string");
                }
            }
            _ => {} // Number, Boolean and Null types are safe as-is
        }
        
        Ok(())
    }
}
```

### Plugin Execution Monitoring

Implement runtime monitoring to detect and handle malicious or misbehaving plugins:

```rust
/// Real-time plugin monitor
pub struct PluginMonitor {
    // Execution statistics for each plugin
    stats: RwLock<HashMap<String, PluginStats>>,
    // Resource usage threshold for warnings
    warning_thresholds: PluginThresholds,
    // Resource usage threshold for blocking
    blocking_thresholds: PluginThresholds,
}

/// Plugin execution statistics
#[derive(Debug, Default, Clone)]
pub struct PluginStats {
    // Number of invocations
    invocation_count: AtomicUsize,
    // Average execution time (ms)
    avg_execution_time_ms: AtomicUsize,
    // Peak memory usage (KB)
    peak_memory_kb: AtomicUsize,
    // Error count
    error_count: AtomicUsize,
    // Last execution timestamp
    last_execution: AtomicU64,
}

impl PluginMonitor {
    /// Record plugin execution metrics
    pub fn record_execution(&self, plugin_id: &str, metrics: PluginExecutionMetrics) {
        let mut stats = self.stats.write().unwrap();
        let plugin_stats = stats.entry(plugin_id.to_owned())
                               .or_insert_with(PluginStats::default);
        
        // Update stats
        let inv_count = plugin_stats.invocation_count.fetch_add(1, Ordering::SeqCst) + 1;
        
        // Update average execution time
        let curr_avg = plugin_stats.avg_execution_time_ms.load(Ordering::SeqCst);
        let new_avg = ((curr_avg * (inv_count - 1)) + metrics.execution_time_ms) / inv_count;
        plugin_stats.avg_execution_time_ms.store(new_avg, Ordering::SeqCst);
        
        // Update peak memory
        plugin_stats.peak_memory_kb.fetch_max(metrics.memory_usage_kb, Ordering::SeqCst);
        
        // Update timestamp
        plugin_stats.last_execution.store(
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            Ordering::SeqCst
        );
        
        // Check thresholds and take action if needed
        self.check_thresholds(plugin_id, metrics);
    }
    
    /// Check if plugin exceeds resource thresholds
    fn check_thresholds(&self, plugin_id: &str, metrics: PluginExecutionMetrics) {
        // Check warning thresholds
        if metrics.execution_time_ms > self.warning_thresholds.max_execution_time_ms {
            log::warn!(
                "Plugin {} exceeded execution time warning threshold: {} ms", 
                plugin_id, metrics.execution_time_ms
            );
        }
        
        if metrics.memory_usage_kb > self.warning_thresholds.max_memory_kb {
            log::warn!(
                "Plugin {} exceeded memory usage warning threshold: {} KB", 
                plugin_id, metrics.memory_usage_kb
            );
        }
        
        // Check blocking thresholds
        if metrics.execution_time_ms > self.blocking_thresholds.max_execution_time_ms {
            log::error!(
                "Plugin {} exceeded maximum execution time, disabling: {} ms", 
                plugin_id, metrics.execution_time_ms
            );
            
            // Disable the plugin
            self.disable_plugin(plugin_id);
        }
        
        if metrics.memory_usage_kb > self.blocking_thresholds.max_memory_kb {
            log::error!(
                "Plugin {} exceeded maximum memory usage, disabling: {} KB", 
                plugin_id, metrics.memory_usage_kb
            );
            
            // Disable the plugin
            self.disable_plugin(plugin_id);
        }
    }
    
    /// Disable a misbehaving plugin
    fn disable_plugin(&self, plugin_id: &str) {
        // Implementation would:
        // 1. Remove plugin from active plugins list
        // 2. Prevent future invocations
        // 3. Notify administrators
        // 4. Log the incident with detailed metrics
        
        log::error!("Plugin {} has been disabled due to resource abuse", plugin_id);
    }
}
```

### Data Protection and Access Control

Implement controls to protect sensitive data from unauthorized access by plugins:

```rust
/// Protected data store with access controls
pub struct ProtectedDataStore {
    // Data storage
    data: RwLock<HashMap<String, Value>>,
    // Access control list
    acl: RwLock<HashMap<String, HashSet<String>>>,
}

impl ProtectedDataStore {
    /// Read data with plugin access check
    pub fn read(&self, plugin_id: &str, key: &str) -> Result<Option<Value>> {
        // Check read permission
        if !self.has_access(plugin_id, key) {
            return Err(ServerError::Plugin(format!(
                "Plugin {} has no access to data key: {}", plugin_id, key
            )));
        }
        
        let data = self.data.read().unwrap();
        Ok(data.get(key).cloned())
    }
    
    /// Write data with plugin access check
    pub fn write(&self, plugin_id: &str, key: &str, value: Value) -> Result<()> {
        // Check write permission
        if !self.has_access(plugin_id, key) {
            return Err(ServerError::Plugin(format!(
                "Plugin {} has no access to data key: {}", plugin_id, key
            )));
        }
        
        // Validate data size
        let value_str = serde_json::to_string(&value)?;
        if value_str.len() > 1024 * 1024 { // 1MB limit
            return Err(ServerError::Plugin("Data size limit exceeded".to_string()));
        }
        
        // Store the data
        let mut data = self.data.write().unwrap();
        data.insert(key.to_string(), value);
        
        Ok(())
    }
    
    /// Check if plugin has access to a data key
    fn has_access(&self, plugin_id: &str, key: &str) -> bool {
        let acl = self.acl.read().unwrap();
        
        if let Some(allowed_keys) = acl.get(plugin_id) {
            // Check for exact key match
            if allowed_keys.contains(key) {
                return true;
            }
            
            // Check for prefix access with wildcard
            for allowed_key in allowed_keys {
                if allowed_key.ends_with("*") {
                    let prefix = &allowed_key[0..allowed_key.len()-1];
                    if key.starts_with(prefix) {
                        return true;
                    }
                }
            }
        }
        
        false
    }
}
```

### Secure Plugin Communications

Implement secure communication channels between plugins:

```rust
/// Secure plugin messaging system
pub struct PluginMessenger {
    // Message queues for each plugin
    queues: RwLock<HashMap<String, mpsc::Receiver<PluginMessage>>>,
    // Message senders for each plugin
    senders: RwLock<HashMap<String, mpsc::Sender<PluginMessage>>>,
    // Access control for message sending
    acl: RwLock<HashMap<String, HashSet<String>>>,
}

/// Message between plugins
pub struct PluginMessage {
    // Sender plugin ID
    sender: String,
    // Message topic
    topic: String,
    // Message payload
    payload: Vec<u8>,
    // Message timestamp
    timestamp: u64,
    // Message signature
    signature: Option<Vec<u8>>,
}

impl PluginMessenger {
    /// Send a message from one plugin to another
    pub fn send_message(
        &self, 
        from_plugin: &str, 
        to_plugin: &str, 
        topic: &str,
        payload: Vec<u8>
    ) -> Result<()> {
        // Check if sender is allowed to send to receiver
        if !self.can_send(from_plugin, to_plugin, topic) {
            return Err(ServerError::Plugin(format!(
                "Plugin {} is not allowed to send {} messages to {}",
                from_plugin, topic, to_plugin
            )));
        }
        
        // Create the message
        let message = PluginMessage {
            sender: from_plugin.to_string(),
            topic: topic.to_string(),
            payload,
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            signature: None, // Would be populated with real signature
        };
        
        // Get the sender for the target plugin
        let senders = self.senders.read().unwrap();
        if let Some(sender) = senders.get(to_plugin) {
            sender.send(message).map_err(|_| {
                ServerError::Plugin(format!(
                    "Failed to send message to plugin {}", to_plugin
                ))
            })?;
            
            Ok(())
        } else {
            Err(ServerError::Plugin(format!(
                "Plugin {} not registered for messaging", to_plugin
            )))
        }
    }
    
    /// Check if a plugin can send a specific message type to another plugin
    fn can_send(&self, from_plugin: &str, to_plugin: &str, topic: &str) -> bool {
        let acl = self.acl.read().unwrap();
        
        if let Some(allowed_recipients) = acl.get(from_plugin) {
            // Check for specific plugin access
            if allowed_recipients.contains(to_plugin) {
                return true;
            }
            
            // Check for wildcard access
            if allowed_recipients.contains("*") {
                return true;
            }
        }
        
        false
    }
}
```

### Implementing Safe Plugin Updates

Implement a secure update mechanism to safely update plugins:

```rust
/// Plugin update manager
pub struct PluginUpdater {
    // Plugin directory
    plugin_dir: PathBuf,
    // Backup directory
    backup_dir: PathBuf,
    // Plugin verifier
    verifier: PluginVerifier,
    // Update source configurations
    update_sources: Vec<UpdateSource>,
}

impl PluginUpdater {
    /// Update a plugin safely
    pub fn update_plugin(&self, plugin_id: &str) -> Result<()> {
        log::info!("Updating plugin: {}", plugin_id);
        
        // Find update source for this plugin
        let source = self.find_update_source(plugin_id)?;
        
        // Download the plugin update to a temporary location
        let temp_path = self.download_plugin_update(plugin_id, &source)?;
        
        // Verify the plugin update
        self.verifier.verify_plugin(&temp_path)?;
        
        // Create a backup of the current plugin
        let plugin_path = self.plugin_dir.join(format!("{}.so", plugin_id));
        let backup_path = self.backup_dir.join(format!("{}-{}.so", 
            plugin_id,
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs()
        ));
        
        if plugin_path.exists() {
            std::fs::copy(&plugin_path, &backup_path)?;
            log::info!("Created plugin backup: {:?}", backup_path);
        }
        
        // Install the update
        std::fs::rename(temp_path, plugin_path)?;
        log::info!("Plugin {} updated successfully", plugin_id);
        
        Ok(())
    }
    
    /// Find update source for a plugin
    fn find_update_source(&self, plugin_id: &str) -> Result<&UpdateSource> {
        for source in &self.update_sources {
            if source.provides_plugin(plugin_id) {
                return Ok(source);
            }
        }
        
        Err(ServerError::Plugin(format!(
            "No update source found for plugin {}", plugin_id
        )))
    }
    
    /// Download plugin update to temporary location
    fn download_plugin_update(&self, plugin_id: &str, source: &UpdateSource) -> Result<PathBuf> {
        // Create a temporary file
        let temp_dir = std::env::temp_dir();
        let temp_path = temp_dir.join(format!("{}-update.so", plugin_id));
        
        // Download the plugin
        source.download_plugin(plugin_id, &temp_path)?;
        
        Ok(temp_path)
    }
}
```

### Best Practices for Plugin Security

1. **Implement Defense in Depth**:
   - Use multiple security layers (verification, sandboxing, monitoring)
   - Don't rely on a single security mechanism

2. **Use Least Privilege Principles**:
   - Grant plugins only the minimum permissions needed
   - Start with zero permissions and add as needed

3. **Enable Security by Default**:
   - Make secure configurations the default
   - Require explicit opt-out for less secure options

4. **Implement Robust Logging**:
   - Log all plugin actions for audit trails
   - Include detailed context in security-related logs

5. **Regular Security Reviews**:
   - Conduct code reviews specifically for plugin security
   - Test with malicious plugins to verify security measures

6. **Update and Patch Management**:
   - Keep all plugins updated to the latest secure versions
   - Implement automated vulnerability scanning for plugins

7. **User Education**:
   - Provide clear documentation about plugin security risks
   - Educate users about safe plugin installation practices

## Rust Learning Summary

In this tutorial, you've learned:

1. **Advanced Trait Usage**
   - Trait objects for polymorphism
   - Dynamic dispatch mechanisms
   - Type erasure patterns

2. **Foreign Function Interface**
   - Dynamic library loading
   - Symbol resolution
   - Memory safety across FFI boundaries

3. **Plugin Design Patterns**
   - Extension points
   - Registration mechanisms
   - Safe plugin lifecycle management

## Next Steps

In the next tutorial, we'll focus on adding WebSockets support to our server, exploring:
- Binary protocol handling
- Connection upgrade mechanisms
- Rust's async streaming capabilities
- Real-time application support

## Conclusion

Congratulations! You've completed all the tutorials in this series. You've built a fully-featured web server in Rust that includes everything from basic TCP handling to advanced features like security, load balancing, and a plugin system. You've also learned core Rust concepts along the way, from basic ownership to advanced topics like unsafe code and FFI.

We hope this series has given you a solid foundation in both web server development and Rust programming. You now have the knowledge to create your own high-performance, secure, and scalable web applications using Rust.

## Navigation
- [Previous: Production Deployment](15-production-deployment.md)
- [Back to Introduction](00-introduction.md)