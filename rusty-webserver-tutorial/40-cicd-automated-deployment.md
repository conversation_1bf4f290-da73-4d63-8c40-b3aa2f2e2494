# CI/CD and Automated Deployment

Continuous Integration and Continuous Deployment (CI/CD) streamlines the process of releasing your Rust webserver to production with confidence. This module explores how to set up robust pipelines for building, testing, and deploying your webserver.

## Learning Objectives
- Understand CI/CD concepts and benefits for Rust web applications
- Create automated pipelines using GitHub Actions and other CI/CD tools
- Build, test, and deploy Rust applications to various platforms
- Implement zero-downtime deployments and rollback strategies
- Automate security scanning and quality checks

## Prerequisites
- Basic understanding of version control with Git
- Knowledge of Rust testing frameworks
- Familiarity with containerization (Docker)

## CI/CD Fundamentals

### Continuous Integration (CI)
Continuous Integration is the practice of merging code changes into a shared repository frequently, followed by automated building and testing. CI helps detect issues early and ensures the codebase is always in a working state.

### Continuous Delivery (CD)
Continuous Delivery extends CI by automatically deploying all code changes to a testing or staging environment after the build stage. This ensures that code is always in a deployable state.

### Continuous Deployment
Continuous Deployment takes CD one step further by automatically deploying every change that passes all stages of the production pipeline to production, with no human intervention.

## Setting Up CI/CD for Rust Webservers

### GitHub Actions

GitHub Actions provides powerful automation for your development workflow. Here's a comprehensive workflow for a Rust webserver:

```yaml
name: Rust Webserver CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  # Check code formatting
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install latest stable Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy
      
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
      
      - name: Check formatting
        run: cargo fmt --all -- --check
      
      - name: Run clippy
        run: cargo clippy -- -D warnings

  # Run tests
  test:
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install latest stable Rust
        uses: dtolnay/rust-toolchain@stable
      
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
      
      - name: Run unit tests
        run: cargo test --all
      
      - name: Run integration tests
        run: cargo test --test '*' -- --ignored
      
      - name: Check for security vulnerabilities
        run: |
          cargo install cargo-audit
          cargo audit
  
  # Build and publish Docker image
  build:
    needs: test
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: yourusername/rusty-webserver
          tags: |
            type=ref,event=branch
            type=sha,format=short
            type=semver,pattern={{version}},suffix=-{{sha}}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      
      - name: Scan Docker image for vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: yourusername/rusty-webserver:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results.sarif'
  
  # Deploy to staging
  deploy-staging:
    needs: build
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.KNOWN_HOSTS }}
      
      - name: Deploy to staging
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.STAGING_HOST }} \
          "docker pull yourusername/rusty-webserver:${{ github.sha }} && \
           docker-compose -f docker-compose.staging.yml up -d --no-deps webserver"
      
      - name: Run smoke tests
        run: |
          # Wait for service to be available
          sleep 10
          curl -f https://staging-api.yourservice.com/health || exit 1
  
  # Deploy to production
  deploy-production:
    needs: build
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment:
      name: production
      url: https://api.yourservice.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      
      - name: Update ECS service
        run: |
          aws ecs update-service \
            --cluster production-cluster \
            --service rusty-webserver \
            --force-new-deployment \
            --task-definition $(aws ecs register-task-definition \
                                 --cli-input-json file://ecs-task-definition.json \
                                 --query "taskDefinition.taskDefinitionArn" \
                                 --output text)
      
      - name: Wait for deployment to complete
        run: |
          aws ecs wait services-stable \
            --cluster production-cluster \
            --services rusty-webserver
      
      - name: Run production smoke tests
        run: |
          curl -f https://api.yourservice.com/health || exit 1
      
      - name: Create release tag
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: `refs/tags/release-${context.sha.substring(0, 7)}`,
              sha: context.sha
            })
```

### Implementing a Docker Build System for Rust

Create a `Dockerfile` optimized for Rust webservers:

```Dockerfile
# Builder stage
FROM rust:1.72 as builder

# Create a new empty shell project
WORKDIR /usr/src/app
RUN USER=root cargo new --bin rusty-webserver
WORKDIR /usr/src/app/rusty-webserver

# Copy manifest files and build dependencies
COPY Cargo.toml Cargo.lock ./
RUN --mount=type=cache,target=/usr/local/cargo/registry \
    --mount=type=cache,target=/usr/src/app/rusty-webserver/target \
    cargo build --release && \
    rm -rf src/

# Copy source code
COPY src ./src

# Build the application
RUN --mount=type=cache,target=/usr/local/cargo/registry \
    --mount=type=cache,target=/usr/src/app/rusty-webserver/target \
    cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends ca-certificates tzdata && \
    rm -rf /var/lib/apt/lists/*

# Create a non-root user
RUN groupadd -r webserver && useradd -r -g webserver webserver

# Copy the binary from the builder stage
COPY --from=builder /usr/src/app/rusty-webserver/target/release/rusty-webserver /usr/local/bin/

# Copy configuration files
COPY config/ /etc/rusty-webserver/

# Set proper permissions
RUN chown -R webserver:webserver /etc/rusty-webserver

# Set the user
USER webserver

# Expose the server port
EXPOSE 8080

# Set the entrypoint
ENTRYPOINT ["/usr/local/bin/rusty-webserver"]
CMD ["--config", "/etc/rusty-webserver/config.toml"]
```

### Multi-Stage Optimization for Smaller Images

For even smaller images, use a multi-stage build with Alpine Linux:

```Dockerfile
# Builder stage
FROM rust:1.72-alpine as builder

# Install build dependencies
RUN apk add --no-cache musl-dev openssl-dev

# Create app directory
WORKDIR /usr/src/app

# Copy source and build
COPY . .
RUN cargo build --release

# Runtime stage (ultra-minimal)
FROM alpine:3.18

# Install runtime dependencies
RUN apk add --no-cache ca-certificates tzdata libgcc

# Create non-root user
RUN addgroup -S webserver && adduser -S webserver -G webserver

# Copy binary and config
COPY --from=builder /usr/src/app/target/release/rusty-webserver /usr/local/bin/
COPY --from=builder /usr/src/app/config/ /etc/rusty-webserver/

# Set permissions
RUN chown -R webserver:webserver /etc/rusty-webserver

# Switch to non-root user
USER webserver

# Expose port
EXPOSE 8080

# Run the application
ENTRYPOINT ["/usr/local/bin/rusty-webserver"]
CMD ["--config", "/etc/rusty-webserver/config.toml"]
```

## Deployment Strategies

### Blue-Green Deployment

Blue-green deployment reduces downtime by running two identical environments (blue and green). One serves production traffic while the other is updated:

```bash
#!/bin/bash
# Blue-Green deployment script

# Determine active environment
ACTIVE_ENV=$(aws ssm get-parameter --name "/webserver/active-environment" --query "Parameter.Value" --output text)
INACTIVE_ENV=$([ "$ACTIVE_ENV" == "blue" ] && echo "green" || echo "blue")

echo "Current active environment: $ACTIVE_ENV"
echo "Deploying to: $INACTIVE_ENV"

# Deploy to inactive environment
aws ecs update-service \
  --cluster webserver-cluster \
  --service "webserver-$INACTIVE_ENV" \
  --force-new-deployment \
  --task-definition $(aws ecs register-task-definition \
                       --cli-input-json file://ecs-task-definition.json \
                       --query "taskDefinition.taskDefinitionArn" \
                       --output text)

# Wait for deployment to complete
aws ecs wait services-stable \
  --cluster webserver-cluster \
  --services "webserver-$INACTIVE_ENV"

# Run health checks on new deployment
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$INACTIVE_ENV.internal.yourservice.com/health")

if [ "$HEALTH_STATUS" == "200" ]; then
  echo "Health check passed, switching traffic to $INACTIVE_ENV"
  
  # Update load balancer target group
  aws elbv2 modify-listener \
    --listener-arn $LISTENER_ARN \
    --default-actions Type=forward,TargetGroupArn=$INACTIVE_ENV_TARGET_GROUP
  
  # Update active environment parameter
  aws ssm put-parameter \
    --name "/webserver/active-environment" \
    --value "$INACTIVE_ENV" \
    --type "String" \
    --overwrite
  
  echo "Deployment successful, traffic now routed to $INACTIVE_ENV"
else
  echo "Health check failed with status $HEALTH_STATUS, aborting deployment"
  exit 1
fi
```

### Canary Deployment

Canary deployment gradually shifts traffic to the new version, allowing you to monitor for issues with a small percentage of users:

```rust
// Example code for a service mesh (e.g., Linkerd) configuration
// to implement canary deployment

use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
struct TrafficSplit {
    kind: String,
    apiVersion: String,
    metadata: Metadata,
    spec: TrafficSplitSpec,
}

#[derive(Serialize, Deserialize)]
struct Metadata {
    name: String,
    namespace: String,
}

#[derive(Serialize, Deserialize)]
struct TrafficSplitSpec {
    service: String,
    backends: Vec<Backend>,
}

#[derive(Serialize, Deserialize)]
struct Backend {
    service: String,
    weight: i32,
}

fn generate_canary_traffic_split(
    name: &str, 
    namespace: &str,
    service: &str, 
    stable_version: &str,
    canary_version: &str,
    canary_percentage: i32,
) -> TrafficSplit {
    TrafficSplit {
        kind: "TrafficSplit".to_string(),
        apiVersion: "split.smi-spec.io/v1alpha2".to_string(),
        metadata: Metadata {
            name: name.to_string(),
            namespace: namespace.to_string(),
        },
        spec: TrafficSplitSpec {
            service: service.to_string(),
            backends: vec![
                Backend {
                    service: stable_version.to_string(),
                    weight: 100 - canary_percentage,
                },
                Backend {
                    service: canary_version.to_string(),
                    weight: canary_percentage,
                },
            ],
        },
    }
}

// Example usage:
// let traffic_split = generate_canary_traffic_split(
//     "webserver-split", 
//     "default", 
//     "webserver-service", 
//     "webserver-stable", 
//     "webserver-canary", 
//     10
// );
// let yaml = serde_yaml::to_string(&traffic_split).unwrap();
// println!("{}", yaml);
```

## Infrastructure as Code (IaC)

### Terraform for Cloud Resources

Create infrastructure for your Rust webserver using Terraform:

```hcl
# main.tf

provider "aws" {
  region = "us-west-2"
}

# VPC and networking
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"
  
  name = "webserver-vpc"
  cidr = "10.0.0.0/16"
  
  azs             = ["us-west-2a", "us-west-2b", "us-west-2c"]
  private_subnets = ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
  public_subnets  = ["10.0.101.0/24", "10.0.102.0/24", "10.0.103.0/24"]
  
  enable_nat_gateway = true
  single_nat_gateway = true
  
  tags = {
    Environment = "production"
    Project     = "rusty-webserver"
  }
}

# ECS cluster
resource "aws_ecs_cluster" "webserver_cluster" {
  name = "webserver-cluster"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
  
  tags = {
    Environment = "production"
    Project     = "rusty-webserver"
  }
}

# ECS task definition
resource "aws_ecs_task_definition" "webserver_task" {
  family                   = "webserver"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = 256
  memory                   = 512
  execution_role_arn       = aws_iam_role.ecs_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn
  
  container_definitions = jsonencode([{
    name  = "webserver"
    image = "yourusername/rusty-webserver:latest"
    
    portMappings = [{
      containerPort = 8080
      hostPort      = 8080
      protocol      = "tcp"
    }]
    
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        "awslogs-group"         = aws_cloudwatch_log_group.webserver_logs.name
        "awslogs-region"        = "us-west-2"
        "awslogs-stream-prefix" = "webserver"
      }
    }
    
    environment = [
      { name = "RUST_LOG", value = "info" },
      { name = "SERVER_PORT", value = "8080" }
    ]
    
    healthCheck = {
      command     = ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval    = 30
      timeout     = 5
      retries     = 3
      startPeriod = 60
    }
  }])
}

# ECS service
resource "aws_ecs_service" "webserver_service" {
  name            = "webserver"
  cluster         = aws_ecs_cluster.webserver_cluster.id
  task_definition = aws_ecs_task_definition.webserver_task.arn
  desired_count   = 3
  launch_type     = "FARGATE"
  
  network_configuration {
    subnets         = module.vpc.private_subnets
    security_groups = [aws_security_group.webserver_sg.id]
  }
  
  load_balancer {
    target_group_arn = aws_lb_target_group.webserver_tg.arn
    container_name   = "webserver"
    container_port   = 8080
  }
  
  deployment_controller {
    type = "ECS"
  }
  
  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }
  
  tags = {
    Environment = "production"
    Project     = "rusty-webserver"
  }
}

# Application Load Balancer
resource "aws_lb" "webserver_alb" {
  name               = "webserver-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = module.vpc.public_subnets
  
  tags = {
    Environment = "production"
    Project     = "rusty-webserver"
  }
}

# Target group for ALB
resource "aws_lb_target_group" "webserver_tg" {
  name        = "webserver-tg"
  port        = 8080
  protocol    = "HTTP"
  vpc_id      = module.vpc.vpc_id
  target_type = "ip"
  
  health_check {
    path                = "/health"
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 5
    interval            = 30
    matcher             = "200"
  }
}

# ALB Listener
resource "aws_lb_listener" "http" {
  load_balancer_arn = aws_lb.webserver_alb.arn
  port              = 80
  protocol          = "HTTP"
  
  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.webserver_tg.arn
  }
}
```

### Kubernetes Manifests

For Kubernetes deployment:

```yaml
# kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rusty-webserver
  labels:
    app: rusty-webserver
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rusty-webserver
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: rusty-webserver
    spec:
      containers:
      - name: rusty-webserver
        image: yourusername/rusty-webserver:latest
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "500m"
            memory: "256Mi"
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        env:
        - name: RUST_LOG
          value: "info"
        volumeMounts:
        - name: config
          mountPath: /etc/rusty-webserver
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: rusty-webserver-config

---
# kubernetes/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: rusty-webserver
spec:
  selector:
    app: rusty-webserver
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP

---
# kubernetes/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rusty-webserver
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  rules:
  - host: api.yourservice.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rusty-webserver
            port:
              number: 80
  tls:
  - hosts:
    - api.yourservice.com
    secretName: api-tls-cert
```

## Monitoring and Observability in Production

### Metrics with Prometheus and Grafana

Add Prometheus metrics to your Rust webserver:

```rust
use prometheus::{
    Encoder, IntCounterVec, HistogramVec, Histogram, Registry, 
    TextEncoder, IntCounter,
};
use lazy_static::lazy_static;
use warp::{Filter, Reply};
use std::convert::Infallible;

// Initialize metrics
lazy_static! {
    pub static ref REGISTRY: Registry = Registry::new();
    
    pub static ref HTTP_REQUESTS_TOTAL: IntCounterVec = IntCounterVec::new(
        opts!("http_requests_total", "Total number of HTTP requests"),
        &["method", "path", "status"]
    ).expect("metric can be created");
    
    pub static ref HTTP_REQUEST_DURATION_SECONDS: HistogramVec = HistogramVec::new(
        histogram_opts!(
            "http_request_duration_seconds",
            "HTTP request duration in seconds"
        ),
        &["method", "path"]
    ).expect("metric can be created");
    
    pub static ref CONNECTED_CLIENTS: IntCounter = IntCounter::new(
        "connected_clients", "Number of currently connected clients"
    ).expect("metric can be created");
}

// Register metrics
pub fn register_metrics() {
    REGISTRY.register(Box::new(HTTP_REQUESTS_TOTAL.clone())).unwrap();
    REGISTRY.register(Box::new(HTTP_REQUEST_DURATION_SECONDS.clone())).unwrap();
    REGISTRY.register(Box::new(CONNECTED_CLIENTS.clone())).unwrap();
}

// Metrics endpoint for Prometheus to scrape
pub fn metrics_handler() -> impl Filter<Extract = impl Reply, Error = Infallible> + Clone {
    warp::path("metrics").map(|| {
        let encoder = TextEncoder::new();
        let mut buffer = vec![];
        encoder.encode(&REGISTRY.gather(), &mut buffer).unwrap();
        
        String::from_utf8(buffer).unwrap()
    })
}
```

## Zero-Downtime Deployment

### Graceful Shutdown

Implement graceful shutdown to handle in-flight requests before terminating:

```rust
use tokio::signal;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::time::{timeout, Duration};

// Graceful shutdown handler
async fn graceful_shutdown(
    server: hyper::Server<hyper::server::conn::AddrIncoming, hyper::service::make_service_fn<_, _>>,
    connections: Arc<AtomicBool>,
) {
    // Wait for termination signal
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("Failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("Failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    println!("Shutdown signal received, starting graceful shutdown");
    
    // Stop accepting new connections
    connections.store(false, Ordering::SeqCst);
    
    // Give active connections time to complete
    let shutdown_timeout = Duration::from_secs(30);
    match timeout(shutdown_timeout, server.with_graceful_shutdown(async {
        // Wait until all connections are done or timeout
        while connections.load(Ordering::SeqCst) {
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
    })).await {
        Ok(_) => println!("Graceful shutdown completed"),
        Err(_) => println!("Graceful shutdown timed out after {:?}", shutdown_timeout),
    }
}
```

## Best Practices for CI/CD

1. **Build Once, Deploy Many Times**
   - Create immutable artifacts (like Docker images)
   - Use the same artifact across all environments
   - Tag images with git commit SHA for traceability

2. **Security in the Pipeline**
   - Scan dependencies for vulnerabilities (cargo-audit)
   - Perform static analysis (clippy, SonarQube)
   - Scan container images for vulnerabilities (Trivy, Clair)

3. **Environment Promotion**
   - Start with development environments
   - Promote to testing/staging environments
   - Finally promote to production
   - Use the same deployment process for all environments

4. **Observability**
   - Collect metrics, logs, and traces
   - Monitor deployment success and failure
   - Implement automatic rollbacks for failed deployments

5. **Infrastructure as Code**
   - Define infrastructure through code (Terraform, CloudFormation)
   - Version control your infrastructure definitions
   - Review infrastructure changes like code changes

6. **Feature Flags**
   - Separate deployment from release
   - Control feature visibility with configuration
   - Gradually roll out features to users

## Knowledge Check

1. **What is the difference between Continuous Delivery and Continuous Deployment?**
   - Continuous Delivery automatically deploys changes to a staging environment, requiring manual approval for production, while Continuous Deployment automatically deploys every change that passes tests directly to production.

2. **What are the benefits of using a multi-stage Docker build for Rust applications?**
   - Smaller final image size, improved security by excluding build tools, and faster deployments.

3. **What is a Blue-Green deployment strategy?**
   - Maintaining two identical production environments (Blue and Green) where one receives traffic while the other is updated, allowing for zero-downtime deployments and easy rollbacks.

4. **Why should you implement graceful shutdown in your Rust webserver?**
   - To ensure in-flight requests are completed before terminating, preventing abrupt disconnections for users.

5. **What should be included in a Docker health check for a Rust webserver?**
   - A command that verifies the server is responsive (like checking an HTTP endpoint), with appropriate interval, timeout, and retry settings.

## Additional Resources

- [The Cargo Book](https://doc.rust-lang.org/cargo/)
- [Docker Documentation](https://docs.docker.com/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Terraform Documentation](https://www.terraform.io/docs/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)

## Next Steps

In the [next module](41-case-studies.md), we'll examine real-world case studies of Rust webservers in production.

[Previous: Distributed Systems](39-distributed-systems.md) | [Next: Case Studies](41-case-studies.md)
1. What does CI/CD stand for?
2. Name a CI/CD tool for Rust projects.
