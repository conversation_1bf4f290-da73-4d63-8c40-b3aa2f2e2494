# Request Validation and Input Sanitization

## Learning Objectives
- Implement robust request validation for different data types
- Apply proper input sanitization techniques to prevent injection attacks
- Design validation middleware for your Rust webserver
- Utilize Rust crates for efficient data validation
- Create custom validation rules and error reporting

## Prerequisites
- Basic understanding of HTTP request structure
- Knowledge of common web security vulnerabilities
- Familiarity with Rust error handling

## Introduction

Protecting your webserver against malicious or malformed input is critical for security and reliability. This module covers validation (ensuring input meets expected formats and constraints) and sanitization (removing or escaping potentially dangerous content).

## Validation Strategies

### 1. Schema-based Validation with Serde

[Serde](https://serde.rs/) combined with validation libraries provides powerful request validation:

```rust
use actix_web::{web, App, HttpResponse, Responder};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize, Validate)]
struct UserRegistration {
    #[validate(length(min = 3, max = 30, message = "Username must be between 3 and 30 characters"))]
    #[validate(regex(path = "USERNAME_REGEX", message = "Username must contain only alphanumeric characters"))]
    username: String,
    
    #[validate(email(message = "Invalid email format"))]
    email: String,
    
    #[validate(length(min = 8, message = "Password must be at least 8 characters"))]
    #[validate(custom = "validate_password_strength")]
    password: String,
    
    #[validate(must_match(other = "password", message = "Passwords do not match"))]
    password_confirmation: String,
    
    #[validate(range(min = 13, max = 120, message = "Age must be between 13 and 120"))]
    age: u8,
}

// Custom regex for username validation
lazy_static! {
    static ref USERNAME_REGEX: regex::Regex = regex::Regex::new(r"^[a-zA-Z0-9_]+$").unwrap();
}

// Custom validator for password strength
fn validate_password_strength(password: &str) -> Result<(), ValidationError> {
    // Check for at least one uppercase letter
    if !password.chars().any(|c| c.is_ascii_uppercase()) {
        return Err(ValidationError::new("Password must contain at least one uppercase letter"));
    }
    
    // Check for at least one number
    if !password.chars().any(|c| c.is_ascii_digit()) {
        return Err(ValidationError::new("Password must contain at least one number"));
    }
    
    // Check for at least one special character
    if !password.chars().any(|c| !c.is_ascii_alphanumeric()) {
        return Err(ValidationError::new("Password must contain at least one special character"));
    }
    
    Ok(())
}

// Registration handler with validation
async fn register(data: web::Json<UserRegistration>) -> impl Responder {
    // Validate the input data
    match data.validate() {
        Ok(_) => {
            // Process valid registration data
            HttpResponse::Ok().json(serde_json::json!({
                "status": "success",
                "message": "User registered successfully"
            }))
        }
        Err(errors) => {
            // Convert validation errors to a readable format
            let error_map = errors.field_errors().iter()
                .map(|(field, errors)| {
                    let messages: Vec<String> = errors.iter()
                        .map(|error| error.message.clone().unwrap_or_else(|| "Invalid value".into()).to_string())
                        .collect();
                    (field.clone(), messages)
                })
                .collect::<HashMap<_, _>>();
            
            HttpResponse::BadRequest().json(serde_json::json!({
                "status": "error",
                "errors": error_map
            }))
        }
    }
}
```

### 2. Type-level Validation with NewType Pattern

Create custom types that enforce validation at compile time:

```rust
use std::fmt;

// Email validation newtype
pub struct Email(String);

impl Email {
    pub fn new(email: String) -> Result<Self, String> {
        // Basic email validation
        if !email.contains('@') || email.len() < 3 {
            return Err(format!("Invalid email format: {}", email));
        }
        
        Ok(Email(email))
    }
    
    pub fn value(&self) -> &str {
        &self.0
    }
}

impl fmt::Display for Email {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl AsRef<str> for Email {
    fn as_ref(&self) -> &str {
        &self.0
    }
}

// Usage
fn process_user_email(email_str: &str) -> Result<(), String> {
    let email = Email::new(email_str.to_string())?;
    
    // Now we can safely use email knowing it's valid
    println!("Processing email: {}", email);
    
    Ok(())
}
```

### 3. Request Validation Middleware

Create middleware that validates all incoming requests:

```rust
use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage,
};
use futures::future::{ok, LocalBoxFuture, Ready};
use std::future::Future;
use std::pin::Pin;
use std::rc::Rc;

// Validation middleware
pub struct ValidateRequest;

impl<S, B> Transform<S, ServiceRequest> for ValidateRequest
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = ValidateRequestMiddleware<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ok(ValidateRequestMiddleware {
            service: Rc::new(service),
        })
    }
}

pub struct ValidateRequestMiddleware<S> {
    service: Rc<S>,
}

impl<S, B> Service<ServiceRequest> for ValidateRequestMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let svc = self.service.clone();
        
        Box::pin(async move {
            // Get content type
            let content_type = req
                .headers()
                .get("Content-Type")
                .and_then(|h| h.to_str().ok())
                .unwrap_or("");
            
            // Validate based on content type
            if content_type.contains("application/json") {
                // For JSON validation
                if let Err(e) = validate_json_payload(&req).await {
                    return Err(e);
                }
            } else if content_type.contains("application/x-www-form-urlencoded") {
                // For form validation
                if let Err(e) = validate_form_payload(&req).await {
                    return Err(e);
                }
            }
            
            // Allow the request to continue
            let res = svc.call(req).await?;
            Ok(res)
        })
    }
}

// Validation functions
async fn validate_json_payload(req: &ServiceRequest) -> Result<(), Error> {
    // Implement JSON validation logic
    // ...
    Ok(())
}

async fn validate_form_payload(req: &ServiceRequest) -> Result<(), Error> {
    // Implement form validation logic
    // ...
    Ok(())
}
```

## Input Sanitization Techniques

### 1. HTML Sanitization

Prevent XSS attacks by sanitizing HTML content:

```rust
use ammonia::clean;

// Sanitize user-provided HTML
fn sanitize_html(input: &str) -> String {
    // Configure ammonia for stricter sanitization
    let builder = ammonia::Builder::new()
        .tags(hashset!["b", "i", "u", "p", "span", "br"])
        .strip_comments(true)
        .link_rel(Some("nofollow noopener noreferrer"))
        .add_generic_attribute_prefixes(&["data-safe-"]);
    
    builder.clean(input).to_string()
}

// Usage in a comment system
async fn submit_comment(
    form: web::Form<CommentForm>,
    db: web::Data<DbPool>,
) -> Result<HttpResponse, Error> {
    // Sanitize comment content
    let safe_content = sanitize_html(&form.content);
    
    // Store sanitized content in database
    let comment = sqlx::query!(
        "INSERT INTO comments (user_id, post_id, content) VALUES ($1, $2, $3) RETURNING id",
        form.user_id,
        form.post_id,
        safe_content
    )
    .fetch_one(&db)
    .await?;
    
    Ok(HttpResponse::Created().json(comment))
}
```

### 2. SQL Injection Prevention

Use parameterized queries to prevent SQL injection:

```rust
use sqlx::PgPool;

// UNSAFE: Vulnerable to SQL injection
async fn unsafe_find_user(db: &PgPool, username: &str) -> Result<User, Error> {
    // DON'T DO THIS! This concatenates SQL with user input
    let query = format!("SELECT * FROM users WHERE username = '{}'", username);
    
    let user = sqlx::query_as::<_, User>(&query)
        .fetch_one(db)
        .await?;
    
    Ok(user)
}

// SAFE: Parameterized queries prevent SQL injection
async fn safe_find_user(db: &PgPool, username: &str) -> Result<User, Error> {
    // DO THIS: Use parameter binding
    let user = sqlx::query_as!(
        User,
        "SELECT * FROM users WHERE username = $1",
        username
    )
    .fetch_one(db)
    .await?;
    
    Ok(user)
}
```

### 3. Path Traversal Prevention

Sanitize file paths to prevent directory traversal:

```rust
use std::path::{Path, PathBuf};

// Sanitize file paths to prevent directory traversal
fn sanitize_path(base_dir: &Path, user_path: &str) -> Result<PathBuf, &'static str> {
    // Normalize the path by resolving ".." and "." components
    let path = PathBuf::from(user_path)
        .components()
        .filter(|component| match component {
            std::path::Component::Normal(_) => true,
            std::path::Component::ParentDir => false, // Disallow ".."
            std::path::Component::CurDir => false,    // Disallow "."
            _ => false,
        })
        .collect::<PathBuf>();
    
    // Join with base directory
    let full_path = base_dir.join(path);
    
    // Ensure the resulting path is still within the base directory
    if !full_path.starts_with(base_dir) {
        return Err("Path traversal attempt detected");
    }
    
    Ok(full_path)
}

// Usage for file access
async fn serve_file(
    path: web::Path<String>,
    config: web::Data<AppConfig>,
) -> Result<actix_files::NamedFile, Error> {
    let base_dir = Path::new(&config.static_files_path);
    
    let file_path = match sanitize_path(base_dir, &path.into_inner()) {
        Ok(p) => p,
        Err(err) => return Err(ErrorForbidden(err)),
    };
    
    // Serve the file if it exists
    match actix_files::NamedFile::open(file_path) {
        Ok(file) => Ok(file),
        Err(_) => Err(ErrorNotFound("File not found")),
    }
}
```

## Data Type-Specific Validation

### 1. JSON Schema Validation

Validate JSON data against a schema:

```rust
use jsonschema::{Draft, JSONSchema};
use serde_json::Value;

// Initialize JSON schema validator
fn init_schema_validator(schema_str: &str) -> Result<JSONSchema, Box<dyn std::error::Error>> {
    // Parse schema
    let schema: Value = serde_json::from_str(schema_str)?;
    
    // Compile schema
    let validator = JSONSchema::options()
        .with_draft(Draft::Draft7)
        .compile(&schema)?;
    
    Ok(validator)
}

// Validate JSON against schema
fn validate_json_schema(
    data: &Value,
    validator: &JSONSchema,
) -> Result<(), Vec<String>> {
    // Perform validation
    let result = validator.validate(data);
    
    if let Err(errors) = result {
        // Collect validation errors
        let error_messages = errors
            .map(|error| format!("{}", error))
            .collect::<Vec<String>>();
        
        return Err(error_messages);
    }
    
    Ok(())
}

// JSON schema validation in API endpoint
async fn handle_api_request(
    data: web::Json<Value>,
    schema: web::Data<JSONSchema>,
) -> Result<HttpResponse, Error> {
    // Validate against schema
    if let Err(errors) = validate_json_schema(&data, &schema) {
        return Ok(HttpResponse::BadRequest().json(serde_json::json!({
            "status": "error",
            "errors": errors
        })));
    }
    
    // Process valid data
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "success"
    })))
}
```

### 2. Form Data Validation

Validate form data with appropriate constraints:

```rust
use actix_web::{web, HttpResponse};
use serde::Deserialize;
use validator::Validate;

#[derive(Deserialize, Validate)]
struct ContactForm {
    #[validate(length(min = 2, max = 100))]
    name: String,
    
    #[validate(email)]
    email: String,
    
    #[validate(length(min = 10, max = 2000))]
    message: String,
    
    #[validate(custom = "validate_captcha")]
    captcha: String,
}

// Custom CAPTCHA validation
fn validate_captcha(captcha: &str) -> Result<(), validator::ValidationError> {
    // In a real application, verify captcha with a service like reCAPTCHA
    if captcha != "valid_captcha_token" {
        return Err(validator::ValidationError::new("Invalid CAPTCHA"));
    }
    
    Ok(())
}

// Handle form submission
async fn handle_contact_form(
    form: web::Form<ContactForm>,
) -> HttpResponse {
    // Validate form data
    if let Err(errors) = form.validate() {
        return HttpResponse::BadRequest().json(errors);
    }
    
    // Process form (send email, store in database, etc.)
    
    HttpResponse::Ok().json(serde_json::json!({
        "status": "success",
        "message": "Your message has been sent"
    }))
}
```

## Best Practices

### 1. Use Allow Lists, Not Block Lists

Always specify what is allowed rather than what is disallowed:

```rust
// BAD: Block list approach (easy to bypass)
fn validate_input_bad(input: &str) -> bool {
    !input.contains("script") && !input.contains("onclick")
}

// GOOD: Allow list approach (more secure)
fn validate_input_good(input: &str) -> bool {
    // Only allow alphanumeric characters and certain punctuation
    input.chars().all(|c| c.is_alphanumeric() || " ,.?!-".contains(c))
}
```

### 2. Validate on Both Client and Server

Implement validation on both client and server sides:

```rust
// Server-side validation (always required)
async fn process_server_validation(form: web::Json<UserData>) -> HttpResponse {
    // Server-side validation logic
    // ...
}

// Client-side validation examples (for reference)
// JavaScript data validation for improved UX
/*
function validateForm() {
    const username = document.getElementById('username').value;
    if (username.length < 3) {
        showError('Username must be at least 3 characters');
        return false;
    }
    return true;
}
*/
```

### 3. Normalize Data Before Validation

Standardize input data to ensure consistent validation:

```rust
// Normalize email before validation
fn normalize_email(email: &str) -> String {
    // Convert to lowercase
    let email = email.to_lowercase();
    
    // Remove any whitespace
    let email = email.trim().to_string();
    
    email
}

// Normalize URL before validation
fn normalize_url(url: &str) -> Result<String, &'static str> {
    // Trim whitespace
    let url = url.trim();
    
    // Ensure it has a scheme
    let url = if !url.starts_with("http://") && !url.starts_with("https://") {
        format!("https://{}", url)
    } else {
        url.to_string()
    };
    
    // Further URL validation...
    
    Ok(url)
}
```

### 4. Fail Securely

Always assume validation will fail and handle errors gracefully:

```rust
// Validation with proper error handling
fn process_user_input(input: &str) -> Result<(), Vec<String>> {
    let mut errors = Vec::new();
    
    // Length validation
    if input.len() < 5 || input.len() > 100 {
        errors.push("Input must be between 5 and 100 characters".into());
    }
    
    // Character validation
    if !input.chars().all(|c| c.is_alphanumeric() || c.is_whitespace()) {
        errors.push("Input must contain only letters, numbers, and spaces".into());
    }
    
    // Return result based on validation
    if errors.is_empty() {
        Ok(())
    } else {
        Err(errors)
    }
}
```

## Implementation Example: Full Validation Pipeline

Here's a complete example showing a validation pipeline for an API endpoint:

```rust
use actix_web::{web, App, HttpServer, HttpResponse, Responder};
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};

// Data types with validation
#[derive(Debug, Deserialize, Serialize, Validate)]
struct ProductCreate {
    #[validate(length(min = 3, max = 100))]
    name: String,
    
    #[validate(range(min = 0.01))]
    price: f64,
    
    #[validate(length(max = 1000))]
    description: Option<String>,
    
    #[validate(range(min = 0))]
    stock: u32,
    
    #[validate(custom = "validate_categories")]
    categories: Vec<String>,
}

// Custom validator for categories
fn validate_categories(categories: &[String]) -> Result<(), ValidationError> {
    if categories.is_empty() {
        return Err(ValidationError::new("At least one category is required"));
    }
    
    if categories.len() > 5 {
        return Err(ValidationError::new("Maximum 5 categories allowed"));
    }
    
    // Ensure categories meet naming requirements
    for category in categories {
        if category.len() < 2 || category.len() > 30 {
            return Err(ValidationError::new("Category names must be between 2-30 characters"));
        }
    }
    
    Ok(())
}

// API handler with validation
async fn create_product(
    data: web::Json<ProductCreate>,
    db: web::Data<DbPool>,
) -> impl Responder {
    // Validate request data
    if let Err(errors) = data.validate() {
        return HttpResponse::BadRequest().json(errors);
    }
    
    // Sanitize description if present
    let description = match &data.description {
        Some(desc) => Some(sanitize_html(desc)),
        None => None,
    };
    
    // Create product in database (example)
    let result = sqlx::query!(
        "INSERT INTO products (name, price, description, stock) VALUES ($1, $2, $3, $4) RETURNING id",
        data.name,
        data.price,
        description,
        data.stock
    )
    .fetch_one(&**db)
    .await;
    
    // Handle database errors
    match result {
        Ok(row) => {
            // Associate categories with product
            for category in &data.categories {
                let _ = sqlx::query!(
                    "INSERT INTO product_categories (product_id, category_name) VALUES ($1, $2)",
                    row.id,
                    category
                )
                .execute(&**db)
                .await;
            }
            
            HttpResponse::Created().json(serde_json::json!({
                "id": row.id,
                "message": "Product created successfully"
            }))
        }
        Err(e) => {
            // Log error
            log::error!("Database error: {}", e);
            
            HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to create product"
            }))
        }
    }
}

// Main application setup
#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Initialize logger
    env_logger::init();
    
    // Connect to database
    let db_pool = setup_database().await.expect("Failed to set up database");
    
    // Start HTTP server
    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(db_pool.clone()))
            .app_data(web::JsonConfig::default()
                .limit(1024 * 1024)  // 1MB max payload
                .error_handler(|err, _| {
                    actix_web::error::InternalError::from_response(
                        err,
                        HttpResponse::BadRequest().json(serde_json::json!({
                            "error": "Invalid JSON payload"
                        }))
                    ).into()
                })
            )
            .service(
                web::scope("/api")
                    .service(
                        web::resource("/products")
                            .route(web::post().to(create_product))
                    )
            )
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}
```

## Security Considerations

### Threat Model for Request Validation

Understanding the potential attacks targeting your validation system is crucial for effective defense:

```mermaid
flowchart TD
    A[Attacker] -->|1. Input Injection| V[Validation System]
    A -->|2. Schema Bypass| V
    A -->|3. Validation DoS| V
    A -->|4. Input Truncation| V
    A -->|5. Type Confusion| V
    A -->|6. Encoding Tricks| V
    
    V -->|Valid Input| P[Processing Logic]
    V -->|Invalid Input| R[Rejection Response]
    
    P -->|Untrusted Input| S[Security Vulnerability]
    
    class A fill:#f96,stroke:#333
    class V fill:#69f,stroke:#333
    class P fill:#6d9,stroke:#333
    class R fill:#6df,stroke:#333
    class S fill:#f66,stroke:#333
```

### 1. Preventing Injection Attacks with Strict Type Validation

Input validation is your first line of defense against injection attacks:

```rust
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};
use regex::Regex;
use lazy_static::lazy_static;

// Custom types with validation built-in
#[derive(Debug, Clone)]
struct SafeSqlIdentifier(String);

lazy_static! {
    // SQL identifier validation - only alphanumeric and underscore
    static ref SQL_IDENTIFIER_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_]+$").unwrap();
    
    // HTML attribute validation - prevent XSS vectors
    static ref HTML_ATTRIBUTE_REGEX: Regex = Regex::new(r#"^[^"'<>=;]+$"#).unwrap();
    
    // Path validation - prevent path traversal
    static ref SAFE_PATH_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_\-\.\/]+$").unwrap();
}

impl SafeSqlIdentifier {
    // Constructor that validates input
    pub fn new(identifier: &str) -> Result<Self, ValidationError> {
        if !SQL_IDENTIFIER_REGEX.is_match(identifier) {
            return Err(ValidationError::new("Invalid SQL identifier"));
        }
        
        // Additional security checks
        if identifier.len() > 64 {
            return Err(ValidationError::new("SQL identifier too long"));
        }
        
        // Check for SQL keywords that shouldn't be identifiers
        if ["SELECT", "UPDATE", "DELETE", "DROP", "INSERT", "ALTER", "CREATE"]
            .iter()
            .any(|keyword| identifier.to_uppercase() == *keyword)
        {
            return Err(ValidationError::new("SQL keyword not allowed as identifier"));
        }
        
        Ok(Self(identifier.to_string()))
    }
    
    // Safe access to the inner value
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

// Use the safe type in your database queries
fn safe_query(db: &DbConnection, table: SafeSqlIdentifier, column: SafeSqlIdentifier) -> Result<Vec<Row>, DbError> {
    // Prevents SQL injection as table and column are validated
    let query = format!("SELECT * FROM {} WHERE {} = ?", table.as_str(), column.as_str());
    
    db.execute(&query, params![user_input])
}

// Path traversal prevention
#[derive(Debug, Clone)]
struct SafePath(String);

impl SafePath {
    pub fn new(path: &str) -> Result<Self, ValidationError> {
        let normalized_path = path.replace("\\", "/");
        
        // Check for directory traversal attempts
        if normalized_path.contains("..") {
            return Err(ValidationError::new("Path traversal attempt detected"));
        }
        
        // Check for path validity
        if !SAFE_PATH_REGEX.is_match(&normalized_path) {
            return Err(ValidationError::new("Invalid path format"));
        }
        
        // Ensure path doesn't start with / (no absolute paths)
        if normalized_path.starts_with('/') {
            return Err(ValidationError::new("Absolute paths not allowed"));
        }
        
        Ok(Self(normalized_path))
    }
    
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

// XSS prevention through validation
#[derive(Debug, Clone)]
struct SafeHtml(String);

impl SafeHtml {
    pub fn new(html: &str) -> Result<Self, ValidationError> {
        // Use HTML sanitizer library
        let clean_html = ammonia::clean(html);
        
        // Additional checks for potentially harmful content
        if clean_html.contains("<script") || 
           clean_html.contains("javascript:") ||
           clean_html.contains("data:") || 
           clean_html.contains("vbscript:") {
            return Err(ValidationError::new("Potentially harmful HTML content detected"));
        }
        
        Ok(Self(clean_html))
    }
    
    pub fn as_str(&self) -> &str {
        &self.0
    }
}
```

### 2. Defense Against Validation Bypass Attacks

Attackers try to bypass validation using various techniques. Implement multiple layers of defense:

```rust
use actix_web::{web, HttpResponse, Error};
use serde::Deserialize;
use validator::Validate;

#[derive(Deserialize, Validate)]
struct UserInput {
    #[validate(length(min = 3, max = 50))
    username: String,
    
    #[validate(email)]
    email: String,
}

// Layer 1: Type-based validation using Serde
async fn input_handler(
    json_body: Result<web::Json<UserInput>, web::JsonPayloadError>,
) -> HttpResponse {
    // Handle JSON parsing errors securely
    let user_data = match json_body {
        Ok(json) => json.into_inner(),
        Err(err) => {
            // Log the error with sanitized input
            log::warn!("JSON parsing error: {:?}", err);
            return HttpResponse::BadRequest().json(json!({
                "error": "Invalid JSON payload"
            }));
        }
    };
    
    // Layer 2: Structural validation using validator
    if let Err(validation_errors) = user_data.validate() {
        log::info!("Validation failed: {:?}", validation_errors);
        return HttpResponse::BadRequest().json(json!({
            "error": "Validation failed",
            "details": format!("{}", validation_errors)
        }));
    }
    
    // Layer 3: Semantic validation with business logic
    if !is_username_available(&user_data.username) {
        return HttpResponse::BadRequest().json(json!({
            "error": "Username already taken"
        }));
    }
    
    // Layer 4: Post-processing validation
    let sanitized_email = sanitize_email(&user_data.email);
    if sanitized_email != user_data.email {
        log::warn!("Email sanitization changed value");
        return HttpResponse::BadRequest().json(json!({
            "error": "Email contains invalid characters"
        }));
    }
    
    // Process validated and sanitized input
    HttpResponse::Ok().json(json!({ "status": "success" }))
}

// Function to handle malformed Unicode and character normalization exploits
fn normalize_input_securely(input: &str) -> String {
    use unicode_normalization::UnicodeNormalization;
    
    // Convert to NFC normalization form to prevent unicode-based bypasses
    let normalized = input.nfc().collect::<String>();
    
    // Eliminate control characters
    normalized.chars()
        .filter(|&c| !c.is_control() && c != '\0')
        .collect()
}

// Implement content security policy
fn apply_security_headers(mut response: HttpResponse) -> HttpResponse {
    response
        .header("Content-Security-Policy", "default-src 'self'; script-src 'self'")
        .header("X-Content-Type-Options", "nosniff")
        .header("X-XSS-Protection", "1; mode=block")
        .finish()
}
```

### 3. Preventing Validation DoS Attacks

Validation logic itself can become a target for DoS attacks:

```rust
use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::{Instant, Duration};
use actix_web::{web, HttpResponse};
use regex::Regex;

// Track validation performance metrics
struct ValidationMetrics {
    validation_count: AtomicUsize,
    regex_timeouts: AtomicUsize,
    last_reset: std::sync::Mutex<Instant>,
    regex_patterns: std::sync::RwLock<Vec<(String, Duration, usize)>>, // Pattern, max duration, usage count
}

impl ValidationMetrics {
    fn new() -> Self {
        Self {
            validation_count: AtomicUsize::new(0),
            regex_timeouts: AtomicUsize::new(0),
            last_reset: std::sync::Mutex::new(Instant::now()),
            regex_patterns: std::sync::RwLock::new(Vec::new()),
        }
    }
    
    fn record_validation(&self) {
        self.validation_count.fetch_add(1, Ordering::Relaxed);
    }
    
    fn record_regex_usage(&self, pattern: &str, duration: Duration) {
        let mut patterns = self.regex_patterns.write().unwrap();
        
        // Find existing pattern or add new one
        if let Some(entry) = patterns.iter_mut().find(|(p, _, _)| p == pattern) {
            entry.1 = std::cmp::max(entry.1, duration);
            entry.2 += 1;
        } else {
            patterns.push((pattern.to_string(), duration, 1));
        }
        
        // Check for timeout
        if duration > Duration::from_millis(100) {
            self.regex_timeouts.fetch_add(1, Ordering::Relaxed);
            log::warn!("Slow regex pattern detected: {}, took {:?}", pattern, duration);
        }
    }
    
    fn reset_if_needed(&self) {
        let mut last_reset = self.last_reset.lock().unwrap();
        if last_reset.elapsed() > Duration::from_secs(3600) {
            self.validation_count.store(0, Ordering::Relaxed);
            self.regex_timeouts.store(0, Ordering::Relaxed);
            *last_reset = Instant::now();
            
            // Log pattern stats
            let patterns = self.regex_patterns.read().unwrap();
            for (pattern, max_duration, count) in patterns.iter() {
                log::info!(
                    "Regex pattern usage: {} (max: {:?}, count: {})",
                    pattern, max_duration, count
                );
            }
        }
    }
}

// Safe regex execution with timeouts
fn safe_regex_match(regex: &Regex, input: &str, metrics: &ValidationMetrics) -> bool {
    use std::panic::{catch_unwind, AssertUnwindSafe};
    
    // Limit input size to prevent ReDoS
    if input.len() > 10000 {
        return false;
    }
    
    // Record metrics
    metrics.record_validation();
    
    // Execute regex with timing
    let start = Instant::now();
    
    // Use catch_unwind to handle potential regex catastrophic backtracking
    let result = catch_unwind(AssertUnwindSafe(|| {
        regex.is_match(input)
    }));
    
    let duration = start.elapsed();
    metrics.record_regex_usage(regex.as_str(), duration);
    
    // Handle timeout or panic
    if duration > Duration::from_millis(500) {
        log::warn!("Regex timeout: {}", regex.as_str());
        false
    } else {
        result.unwrap_or(false)
    }
}

// Email validation with DoS protection
fn validate_email_safely(email: &str, metrics: &ValidationMetrics) -> bool {
    lazy_static! {
        // Simple regex first for quick rejection
        static ref SIMPLE_EMAIL_CHECK: Regex = Regex::new(r"^[^@\s]+@[^@\s]+\.[^@\s]+$").unwrap();
        
        // More comprehensive check only if simple check passes
        static ref FULL_EMAIL_CHECK: Regex = Regex::new(
            r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"
        ).unwrap();
    }
    
    // Reset metrics if needed
    metrics.reset_if_needed();
    
    // Quick check first
    if !safe_regex_match(&SIMPLE_EMAIL_CHECK, email, metrics) {
        return false;
    }
    
    // Full validation
    safe_regex_match(&FULL_EMAIL_CHECK, email, metrics)
}
```

### 4. Context-Aware Input Validation

Different contexts require different validation approaches:

```rust
// Database Context
fn validate_for_database(input: &str) -> Result<String, ValidationError> {
    // Check for SQL injection patterns
    if contains_sql_injection_pattern(input) {
        return Err(ValidationError::new("SQL injection attempt detected"));
    }
    
    // Use parameterized queries
    Ok(input.to_string())
}

// HTML Context
fn validate_for_html_attribute(input: &str) -> Result<String, ValidationError> {
    // Different escaping for different HTML contexts
    html_escape::encode_attribute(input)
        .pipe(|escaped| Ok(escaped))
}

fn validate_for_html_content(input: &str) -> Result<String, ValidationError> {
    html_escape::encode_text(input)
        .pipe(|escaped| Ok(escaped))
}

// URL Context
fn validate_for_url_parameter(input: &str) -> Result<String, ValidationError> {
    // URL encoding
    urlencoding::encode(input)
        .pipe(|encoded| Ok(encoded.to_string()))
}

// Shell Command Context
fn validate_for_shell_command(input: &str) -> Result<String, ValidationError> {
    // This is inherently dangerous - avoid if possible
    // If necessary, use a whitelist approach
    let allowed_chars = Regex::new(r"^[a-zA-Z0-9 _\-\.]+$").unwrap();
    
    if !allowed_chars.is_match(input) {
        return Err(ValidationError::new("Invalid shell command characters"));
    }
    
    if input.contains("&") || input.contains("|") || input.contains(";") || 
       input.contains(">") || input.contains("<") || input.contains("$") {
        return Err(ValidationError::new("Shell metacharacters not allowed"));
    }
    
    Ok(input.to_string())
}

// File Path Context
fn validate_for_file_path(input: &str) -> Result<String, ValidationError> {
    // Normalize the path
    let normalized = input.replace("\\", "/").replace("//", "/");
    
    // Check for directory traversal
    if normalized.contains("../") || normalized.contains("..\\") {
        return Err(ValidationError::new("Path traversal attempt detected"));
    }
    
    // Check for path validity
    if !SAFE_PATH_REGEX.is_match(&normalized) {
        return Err(ValidationError::new("Invalid path format"));
    }
    
    Ok(normalized)
}
```

### 5. Security Monitoring and Validation Bypass Detection

Implement monitoring to detect validation bypass attempts:

```rust
use serde_json::Value;
use std::sync::Arc;

struct ValidationAudit {
    // Track validation bypass attempts
    bypass_attempts: AtomicUsize,
    // Track validation errors
    validation_errors: AtomicUsize,
    // Suspicious patterns counter
    suspicious_patterns: std::sync::RwLock<HashMap<String, usize>>,
}

impl ValidationAudit {
    fn new() -> Self {
        Self {
            bypass_attempts: AtomicUsize::new(0),
            validation_errors: AtomicUsize::new(0),
            suspicious_patterns: std::sync::RwLock::new(HashMap::new()),
        }
    }
    
    // Record a validation error
    fn record_validation_error(&self, field: &str, value: &str, error: &str) {
        self.validation_errors.fetch_add(1, Ordering::Relaxed);
        
        // Check for suspicious patterns that might indicate bypassing attempts
        let suspicious_patterns = [
            (r"<[^>]*script", "xss-script-tag"),
            (r"javascript:", "xss-javascript-protocol"),
            (r"SELECT.+FROM", "sql-select"),
            (r"UNION.+SELECT", "sql-union"),
            (r"DROP.+TABLE", "sql-drop"),
            (r"--", "sql-comment"),
            (r"\.\./", "path-traversal"),
            (r"\\x[0-9a-f]{2}", "hex-encoding"),
            (r"%[0-9a-f]{2}", "url-encoding"),
            (r"&#[0-9]+;", "html-entity"),
        ];
        
        // Check if value matches any suspicious pattern
        for (pattern, name) in suspicious_patterns.iter() {
            if Regex::new(pattern).unwrap().is_match(value) {
                // Record the suspicious pattern
                let mut patterns = self.suspicious_patterns.write().unwrap();
                *patterns.entry(name.to_string()).or_insert(0) += 1;
                
                // If this is a potential bypass attempt, record it
                if error.contains("validation") || error.contains("format") {
                    self.bypass_attempts.fetch_add(1, Ordering::Relaxed);
                    
                    // Log the attempt
                    log::warn!(
                        "Possible validation bypass attempt: pattern={}, field={}, value={},
                        name, field, value
                    );
                }
                
                break;
            }
        }
    }
    
    // Get audit report
    fn get_report(&self) -> Value {
        let patterns = self.suspicious_patterns.read().unwrap();
        let mut patterns_json = serde_json::Map::new();
        
        for (pattern, count) in patterns.iter() {
            patterns_json.insert(pattern.clone(), json!(*count));
        }
        
        json!({
            "validation_errors": self.validation_errors.load(Ordering::Relaxed),
            "bypass_attempts": self.bypass_attempts.load(Ordering::Relaxed),
            "suspicious_patterns": patterns_json,
        })
    }
}

// Middleware to audit validation errors
struct ValidationAuditMiddleware {
    audit: Arc<ValidationAudit>,
}

impl<S> Transform<S, ServiceRequest> for ValidationAuditMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse, Error = Error>,
    S::Future: 'static,
{
    type Response = ServiceResponse;
    type Error = Error;
    type Transform = ValidationAuditMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ok(ValidationAuditMiddlewareService {
            service,
            audit: self.audit.clone(),
        })
    }
}

struct ValidationAuditMiddlewareService<S> {
    service: S,
    audit: Arc<ValidationAudit>,
}

impl<S> Service<ServiceRequest> for ValidationAuditMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse, Error = Error>,
    S::Future: 'static,
{
    type Response = ServiceResponse;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&self, ctx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.service.poll_ready(ctx)
    }

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let audit = self.audit.clone();
        
        let fut = self.service.call(req);
        
        Box::pin(async move {
            let res = fut.await?;
            
            // Check if response is a validation error
            if res.status() == StatusCode::BAD_REQUEST {
                // Try to extract validation error information
                if let Some(error_body) = res.response().extensions().get::<ValidationErrorBody>() {
                    for (field, value, error) in &error_body.errors {
                        audit.record_validation_error(field, value, error);
                    }
                }
            }
            
            Ok(res)
        })
    }
}
```

By implementing these security measures for request validation, you'll significantly reduce the risk of input-related vulnerabilities in your application.

[Previous: Rate Limiting and DoS Protection](21-rate-limiting-dos.md) | [Next: File Uploads](23-file-uploads.md)
