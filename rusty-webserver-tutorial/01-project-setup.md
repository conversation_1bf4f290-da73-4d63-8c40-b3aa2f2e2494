<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\01-project-setup.md -->
# Module 01: Project Setup and Structure

## Learning Objectives
- Set up a proper Rust development environment
- Create and structure a new web server project
- Understand project organization best practices in Rust
- Configure the necessary dependencies
- Consider key architectural and design decisions for web servers
- Create and manage a structured configuration system

## Prerequisites
- Basic familiarity with the command line
- A text editor or IDE installed (VS Code recommended)
- Basic understanding of web servers and HTTP

## Navigation
- [Previous: Introduction](00-introduction.md)
- [Next: Basic TCP Server](02-basic-tcp-server.md)

## Setting Up the Rust Development Environment

Before we begin coding our web server, let's ensure we have a proper Rust development environment.

### Installing Rust

If you haven't already installed Rust, use the official installer:

```bash
# For Unix/Linux/macOS:
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# For Windows PowerShell:
Invoke-WebRequest -Uri https://win.rustup.rs -OutFile rustup-init.exe
./rustup-init.exe
```

After installation, verify that Rust is correctly installed:

```bash
rustc --version
cargo --version
```

You should see version information for both the Rust compiler and Cargo, the Rust package manager.

### Creating a New Project

Let's create a new Rust binary project for our web server:

```bash
# Create a new directory for your project
mkdir rusty_server
cd rusty_server

# Initialize a new Rust binary project
cargo init --bin
```

This command creates a new binary project with the following basic structure:

```
rusty_server/
├── Cargo.toml  # Project configuration and dependencies
├── .gitignore  # Git ignore file
└── src/
    └── main.rs # Entry point for our application
```

## Designing Our Project Structure

A well-organized project structure will help us maintain our code as it grows. For a web server, we need to consider several components:

1. **HTTP Protocol Layer**: For handling HTTP requests and responses
2. **Server Core**: For managing connections and routing requests
3. **Configuration System**: For managing server settings
4. **Static File Handler**: For serving files from the filesystem
5. **Logging System**: For recording server events and access logs

Let's design a structure that accommodates these components:

```
rusty_server/
├── Cargo.toml         # Project configuration and dependencies
├── config.yaml        # Server configuration file
├── static/            # Directory for static files to serve
│   └── index.html     # Default index page
├── src/
│   ├── main.rs        # Entry point for our application
│   ├── config/        # Configuration management
│   │   └── mod.rs     # Configuration module
│   ├── http/          # HTTP protocol implementation
│   │   ├── mod.rs     # HTTP module definitions
│   │   ├── request.rs # HTTP request parsing
│   │   └── response.rs # HTTP response generation
│   ├── server/        # Server implementation
│   │   ├── mod.rs     # Server module core
│   │   └── tests.rs   # Server tests
│   ├── logging.rs     # Logging utilities
│   └── error.rs       # Error types
└── tests/             # Integration tests
```

Let's create these directories:

```bash
# Create the directory structure
mkdir -p src/config src/http src/server tests
touch src/logging.rs src/error.rs
```

## Setting Up Cargo.toml

The `Cargo.toml` file manages our project's metadata and dependencies. Let's examine each section:

```toml
[package]
name = "rusty_server"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A web server built in Rust, similar to nginx"
license = "MIT"
readme = "README.md"
repository = "https://github.com/yourusername/rusty_server"

[dependencies]
# Core dependencies - we'll add more as needed
log = "0.4"                # Logging facade for flexible logging implementation
env_logger = "0.10"        # Simple logger that can be configured via environment variables
anyhow = "1.0"             # Flexible error handling with context
thiserror = "1.0"          # Macros for defining custom error types
serde = { version = "1.0", features = ["derive"] }  # Serialization/deserialization framework
serde_yaml = "0.9"         # YAML support for configuration files
config = "0.13"            # Configuration management with layered sources

[dev-dependencies]
# Testing utilities
assert_matches = "1.5"     # Improved assertions for testing
tempfile = "3.4"           # Temporary file and directory creation for tests
criterion = "0.3"          # Benchmarking library for performance testing

[profile.release]
# Optimizations for release builds
opt-level = 3              # Maximum optimizations
lto = true                 # Link-time optimization
codegen-units = 1          # Slower compile but better optimization
panic = 'abort'            # Abort on panic in release mode
strip = true               # Strip symbols from binary
```

### Understanding the Dependencies

Let's understand the purpose of each dependency:

1. **Logging**:
   - `log`: A lightweight logging facade that allows us to change logging implementations without changing our code.
   - `env_logger`: A simple logger implementation that's configured via environment variables.

2. **Error Handling**:
   - `anyhow`: Provides a convenient `Error` type for error handling and context.
   - `thiserror`: Simplifies defining custom error types with derived traits.

3. **Configuration**:
   - `serde`: Handles serialization and deserialization of structured data.
   - `serde_yaml`: Allows reading YAML configuration files.
   - `config`: Provides a layered configuration system with file, environment, and default sources.

4. **Testing**:
   - `assert_matches`: Provides enhanced assertion macros for pattern matching.
   - `tempfile`: Creates temporary files and directories for testing.
   - `criterion`: A statistics-driven benchmarking library.

5. **Release Profile**:
   - The `[profile.release]` section configures optimizations for release builds.
   - These settings make our binary smaller and faster in production.

## Web Server Architecture and Design Considerations

When designing a web server, several critical architectural decisions must be made that will influence the server's performance, scalability, and maintainability. Let's explore these key decisions:

### 1. Process Model

The process model determines how our server handles concurrent connections.

**Options:**
- **Single Process, Single Thread**: 
  - *Advantages*: Simplest to implement, no synchronization issues
  - *Disadvantages*: Blocks on each request, poor resource utilization
  - *Best for*: Learning, extremely lightweight applications

- **Single Process, Multiple Threads**: 
  - *Advantages*: Good balance of simplicity and concurrency, shared memory
  - *Disadvantages*: Thread synchronization complexity, potential for race conditions
  - *Best for*: Medium-sized applications, balanced performance needs

- **Multiple Processes**: 
  - *Advantages*: Better isolation, crash resilience, scales across multiple cores
  - *Disadvantages*: More complex communication, higher memory usage
  - *Best for*: High-reliability services, systems with many CPU cores

**Decision for our project:** We'll implement an incremental approach, starting with a single thread for simplicity, then evolving to a multi-threaded model using Rust's thread pool. This progression allows us to focus on core functionality first while systematically introducing threading concepts.

### 2. I/O Model

The I/O model determines how our server handles input/output operations, particularly network communication.

**Options:**
- **Blocking I/O**: 
  - *Advantages*: Simpler to understand and implement, straightforward control flow
  - *Disadvantages*: Thread per connection doesn't scale well
  - *Best for*: Low-traffic servers, learning implementations

- **Non-blocking I/O**: 
  - *Advantages*: Better performance for high-concurrency scenarios
  - *Disadvantages*: More complex state management, callback patterns
  - *Best for*: Medium to high-traffic servers

- **Asynchronous I/O**: 
  - *Advantages*: Most efficient for high load, optimal resource utilization
  - *Disadvantages*: More complex implementation, advanced concepts
  - *Best for*: High-performance, high-concurrency applications

### 3. Connection Handling

**Options:**
- **Connection per request**: Simple but inefficient for high volume
- **Connection pooling**: Reuse connections for better performance
- **HTTP/1.1 keep-alive**: Maintain connections for multiple requests
- **HTTP/2 multiplexing**: Handle multiple requests on single connection

**Decision for our project:** We'll implement HTTP/1.1 with keep-alive support as a good balance between complexity and performance.

## Rust Design Considerations

For our Rust implementation, we'll make the following design choices:

### 1. Error Handling

**Options:**
- **Result and Option types**: Rust's built-in error handling
- **Custom error types**: More specific error information
- **Error handling libraries**: Simplified error management

**Decision for our project:** We'll use the `anyhow` crate for convenient error handling during development, combined with `thiserror` for defining our own error types where appropriate. This approach balances ergonomics with specificity.

### 2. Memory Management

**Options:**
- **Ownership model**: Rust's core feature for memory safety
- **Reference counting**: For shared ownership scenarios
- **Arena allocation**: For performance-critical components

**Decision for our project:** We'll primarily leverage Rust's ownership system with borrowing, introducing reference counting (`Rc`/`Arc`) only when necessary for shared data structures.

### 3. Concurrency Approach

**Options:**
- **Threads with mutex/channels**: Standard concurrency primitives
- **Thread pools**: Managed thread creation and task distribution
- **Async/await**: Cooperative multitasking

**Decision for our project:** We'll start with a basic thread-per-connection model using standard threads, then refine it with a thread pool implementation, before eventually exploring async/await as an advanced topic.

## Step-by-Step Setup

1. **Create the project:**
   ```powershell
   cargo new rusty_server
   cd rusty_server
   mkdir src/config src/http src/server tests benches
   ```
2. **Add dependencies:**
   - `log`, `env_logger` for logging
   - `serde`, `toml` for config parsing
   - `criterion` for benchmarks
   - `num_cpus` for thread pool sizing

3. **Create a sample `config.toml`:**
   ```toml
   address = "127.0.0.1"
   port = 8080
   doc_root = "./public"
   max_connections = 100
   ```

4. **Test the setup:**
   ```powershell
   cargo build
   ```

*You are now ready to start building your web server!*

In the next tutorial, we'll implement our first version of the HTTP server with basic functionality.

## Navigation
- [Previous: Introduction](00-introduction.md)
- [Next: Basic TCP Server](02-basic-tcp-server.md)
