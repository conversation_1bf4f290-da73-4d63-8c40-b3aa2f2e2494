# Capstone Project: Production-Ready Rust Webserver

Congratulations on reaching the final module of this tutorial series! It's time to apply everything you've learned to build a complete, production-ready webserver in Rust. This capstone project will challenge you to integrate various concepts and create a robust, flexible, and maintainable webserver.

## Learning Objectives
- Design and implement a complete webserver from scratch
- Apply advanced Rust patterns for robust error handling and performance
- Create a flexible plugin architecture for extensibility
- Build a secure, observable, and deployable application
- Demonstrate best practices in testing, documentation, and CI/CD

## Project Overview: RustyServe

You will build "RustyServe," a high-performance, plugin-based HTTP/WebSocket server designed for production use cases. RustyServe will support dynamic loading of plugins, comprehensive observability, and robust security features.

## Core Requirements

### 1. HTTP Server Foundation
- Implement an async HTTP server using Tokio and Hyper
- Support HTTP/1.1 and HTTP/2
- Handle connection management, keep-alive, and graceful shutdown

### 2. Routing and Middleware
- Create a flexible routing system with path and method matching
- Implement a middleware system with pre/post-processing
- Support middleware chaining and ordering

### 3. Plugin System
- Design a plugin API for extending functionality
- Support dynamic loading of plugins at runtime
- Implement sandbox isolation for plugins

### 4. Security Features
- TLS/HTTPS with modern cipher configuration
- Authentication and authorization framework
- Rate limiting and DoS protection
- Input validation and sanitization

### 5. Observability
- Structured logging with contextual information
- Metrics collection and export (Prometheus-compatible)
- Distributed tracing with OpenTelemetry
- Health checks and readiness probes

### 6. Performance Optimization
- Connection pooling for backend services
- Efficient resource usage
- Caching headers and content

### 7. WebSocket Support
- WebSocket protocol handling
- Pub/sub capabilities
- Connection management

### 8. Deployment
- Containerization with Docker
- CI/CD pipeline configuration
- Configuration management
- Infrastructure as code (optional)

## Project Structure

Here's a suggested project structure to guide your implementation:

```
rustyserve/
├── Cargo.toml
├── Cargo.lock
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── release.yml
├── src/
│   ├── main.rs              # Entry point
│   ├── config.rs            # Configuration loading
│   ├── server/              # Core HTTP server
│   │   ├── mod.rs
│   │   ├── connection.rs    # Connection handling
│   │   ├── http.rs          # HTTP protocol implementation
│   │   └── websocket.rs     # WebSocket implementation
│   ├── router/              # Routing system
│   │   ├── mod.rs
│   │   ├── matcher.rs       # Path and method matching
│   │   └── handler.rs       # Request handler traits
│   ├── middleware/          # Middleware system
│   │   ├── mod.rs
│   │   ├── chain.rs         # Middleware chaining
│   │   ├── logger.rs        # Request logging
│   │   ├── auth.rs          # Authentication
│   │   └── ratelimit.rs     # Rate limiting
│   ├── plugins/             # Plugin system
│   │   ├── mod.rs
│   │   ├── api.rs           # Plugin API definitions
│   │   ├── loader.rs        # Dynamic loading
│   │   └── sandbox.rs       # Plugin isolation
│   ├── security/            # Security features
│   │   ├── mod.rs
│   │   ├── tls.rs           # TLS configuration
│   │   ├── csrf.rs          # CSRF protection
│   │   └── headers.rs       # Security headers
│   └── observability/       # Observability
│       ├── mod.rs
│       ├── logging.rs       # Structured logging
│       ├── metrics.rs       # Metrics collection
│       └── tracing.rs       # Distributed tracing
├── examples/                # Example plugins and usage
│   ├── basic_server.rs
│   ├── auth_plugin.rs
│   └── static_files.rs
├── tests/                   # Integration tests
│   ├── http_tests.rs
│   ├── websocket_tests.rs
│   └── plugin_tests.rs
├── benches/                 # Performance benchmarks
│   ├── http_benchmark.rs
│   └── routing_benchmark.rs
└── docs/                    # Documentation
    ├── architecture.md
    ├── plugin_api.md
    └── deployment.md
```

## Implementation Guide

### Phase 1: Core HTTP Server

Start by implementing the basic HTTP server functionality:

```rust
// src/main.rs
use clap::{App, Arg};
use rustyserve::config::Config;
use rustyserve::server::Server;
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Parse command line arguments
    let matches = App::new("RustyServe")
        .version("1.0.0")
        .author("Your Name")
        .about("Production-ready Rust webserver with plugin support")
        .arg(
            Arg::with_name("config")
                .short("c")
                .long("config")
                .value_name("FILE")
                .help("Sets a custom config file")
                .takes_value(true),
        )
        .get_matches();

    // Load configuration
    let config_path = matches.value_of("config").unwrap_or("config.toml");
    let config = Config::from_file(config_path)?;
    
    // Initialize logging
    rustyserve::observability::init_logging(&config.logging)?;
    
    // Initialize metrics
    let metrics = rustyserve::observability::init_metrics(&config.metrics)?;
    
    // Create and start server
    let server = Server::new(config, Arc::new(metrics))?;
    server.run().await?;
    
    Ok(())
}

// src/server/mod.rs
use crate::config::Config;
use crate::observability::Metrics;
use crate::router::Router;
use hyper::server::conn::AddrIncoming;
use hyper::service::{make_service_fn, service_fn};
use hyper::{Body, Request, Response, Server as HyperServer};
use std::convert::Infallible;
use std::net::SocketAddr;
use std::sync::Arc;

pub struct Server {
    config: Config,
    router: Router,
    metrics: Arc<Metrics>,
}

impl Server {
    pub fn new(config: Config, metrics: Arc<Metrics>) -> Result<Self, Box<dyn std::error::Error>> {
        let router = Router::new();
        
        // Initialize default routes and middleware
        
        Ok(Server {
            config,
            router,
            metrics,
        })
    }
    
    pub async fn run(&self) -> Result<(), Box<dyn std::error::Error>> {
        let addr: SocketAddr = self.config.server.address.parse()?;
        
        // Create service function
        let router = self.router.clone();
        let metrics = self.metrics.clone();
        
        let make_svc = make_service_fn(move |_conn| {
            let router = router.clone();
            let metrics = metrics.clone();
            
            async move {
                Ok::<_, Infallible>(service_fn(move |req: Request<Body>| {
                    let router = router.clone();
                    let metrics = metrics.clone();
                    
                    async move {
                        metrics.increment_counter("http_requests_total", &["method", req.method().as_str()]);
                        let timer = metrics.start_timer("http_request_duration_seconds", &["method", req.method().as_str()]);
                        
                        let result = router.handle(req).await;
                        
                        timer.observe_duration();
                        Ok::<_, Infallible>(result)
                    }
                }))
            }
        });
        
        // Create and start server
        let server = HyperServer::bind(&addr).serve(make_svc);
        
        log::info!("Server started at http://{}", addr);
        
        // Graceful shutdown
        let server = server.with_graceful_shutdown(self.shutdown_signal());
        
        server.await?;
        log::info!("Server shutdown complete");
        
        Ok(())
    }
    
    async fn shutdown_signal(&self) {
        tokio::signal::ctrl_c()
            .await
            .expect("Failed to install CTRL+C signal handler");
        log::info!("Shutdown signal received, starting graceful shutdown");
    }
}
```

### Phase 2: Routing and Middleware

Implement the routing system that maps requests to handlers:

```rust
// src/router/mod.rs
use crate::middleware::MiddlewareChain;
use hyper::{Body, Method, Request, Response, StatusCode};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Clone)]
pub struct Router {
    routes: Arc<RwLock<HashMap<RouteKey, Box<dyn Handler>>>>,
    middleware: MiddlewareChain,
}

struct RouteKey {
    method: Method,
    path: String,
}

pub trait Handler: Send + Sync + 'static {
    fn handle(&self, req: Request<Body>) -> BoxFuture<'static, Response<Body>>;
}

impl Router {
    pub fn new() -> Self {
        Router {
            routes: Arc::new(RwLock::new(HashMap::new())),
            middleware: MiddlewareChain::new(),
        }
    }
    
    pub async fn add_route<H>(&self, method: Method, path: &str, handler: H)
    where
        H: Handler,
    {
        let key = RouteKey {
            method,
            path: path.to_string(),
        };
        
        let mut routes = self.routes.write().await;
        routes.insert(key, Box::new(handler));
    }
    
    pub fn with_middleware<M>(&mut self, middleware: M) -> &mut Self
    where
        M: Middleware,
    {
        self.middleware.add(middleware);
        self
    }
    
    pub async fn handle(&self, req: Request<Body>) -> Response<Body> {
        // Apply middleware chain
        let (req, abort) = self.middleware.apply(req).await;
        
        if abort {
            return Response::builder()
                .status(StatusCode::INTERNAL_SERVER_ERROR)
                .body(Body::from("Request aborted by middleware"))
                .unwrap();
        }
        
        // Find matching route
        let path = req.uri().path().to_string();
        let method = req.method().clone();
        
        let routes = self.routes.read().await;
        let key = RouteKey { method, path };
        
        match routes.get(&key) {
            Some(handler) => handler.handle(req).await,
            None => Response::builder()
                .status(StatusCode::NOT_FOUND)
                .body(Body::from("Not found"))
                .unwrap(),
        }
    }
}
```

### Phase 3: Plugin System

Create a flexible plugin architecture for extending the server:

```rust
// src/plugins/api.rs
use crate::router::Router;
use async_trait::async_trait;
use std::error::Error;

#[async_trait]
pub trait Plugin: Send + Sync + 'static {
    /// Get the name of the plugin
    fn name(&self) -> &str;
    
    /// Get the version of the plugin
    fn version(&self) -> &str;
    
    /// Initialize the plugin
    async fn init(&self) -> Result<(), Box<dyn Error>>;
    
    /// Register routes and middleware with the router
    async fn register(&self, router: &Router) -> Result<(), Box<dyn Error>>;
    
    /// Shutdown the plugin
    async fn shutdown(&self) -> Result<(), Box<dyn Error>>;
}

// src/plugins/loader.rs
use crate::plugins::api::Plugin;
use crate::router::Router;
use libloading::{Library, Symbol};
use std::error::Error;
use std::path::Path;

pub struct PluginLoader {
    plugins: Vec<Box<dyn Plugin>>,
    libraries: Vec<Library>,
}

impl PluginLoader {
    pub fn new() -> Self {
        PluginLoader {
            plugins: Vec::new(),
            libraries: Vec::new(),
        }
    }
    
    pub fn load<P: AsRef<Path>>(&mut self, path: P) -> Result<(), Box<dyn Error>> {
        unsafe {
            let lib = Library::new(path.as_ref())?;
            
            // Get the plugin creation function
            let func: Symbol<unsafe fn() -> *mut dyn Plugin> = lib.get(b"_create_plugin")?;
            
            // Call the function to create the plugin
            let plugin = Box::from_raw(func());
            
            self.plugins.push(plugin);
            self.libraries.push(lib);
            
            Ok(())
        }
    }
    
    pub async fn init_all(&self) -> Result<(), Box<dyn Error>> {
        for plugin in &self.plugins {
            plugin.init().await?;
        }
        Ok(())
    }
    
    pub async fn register_all(&self, router: &Router) -> Result<(), Box<dyn Error>> {
        for plugin in &self.plugins {
            plugin.register(router).await?;
        }
        Ok(())
    }
    
    pub async fn shutdown_all(&self) -> Result<(), Box<dyn Error>> {
        for plugin in &self.plugins {
            plugin.shutdown().await?;
        }
        Ok(())
    }
}

// Example plugin implementation
// plugins/static_files/src/lib.rs
use async_trait::async_trait;
use rustyserve::plugins::api::Plugin;
use rustyserve::router::{Handler, Router};
use hyper::{Body, Method, Request, Response, StatusCode};
use std::error::Error;
use std::path::{Path, PathBuf};

struct StaticFileHandler {
    root: PathBuf,
}

impl StaticFileHandler {
    fn new<P: AsRef<Path>>(root: P) -> Self {
        StaticFileHandler {
            root: root.as_ref().to_path_buf(),
        }
    }
}

#[async_trait]
impl Handler for StaticFileHandler {
    async fn handle(&self, req: Request<Body>) -> Response<Body> {
        let path = req.uri().path();
        let file_path = self.root.join(path.trim_start_matches('/'));
        
        // Safety check - ensure path is within root directory
        if !file_path.starts_with(&self.root) {
            return Response::builder()
                .status(StatusCode::FORBIDDEN)
                .body(Body::from("Access denied"))
                .unwrap();
        }
        
        match tokio::fs::read(&file_path).await {
            Ok(contents) => {
                // Determine content type
                let content_type = mime_guess::from_path(&file_path)
                    .first_or_octet_stream()
                    .as_ref()
                    .to_owned();
                
                Response::builder()
                    .header("Content-Type", content_type)
                    .body(Body::from(contents))
                    .unwrap()
            }
            Err(_) => Response::builder()
                .status(StatusCode::NOT_FOUND)
                .body(Body::from("File not found"))
                .unwrap(),
        }
    }
}

pub struct StaticFilesPlugin {
    root_dir: PathBuf,
}

impl StaticFilesPlugin {
    pub fn new<P: AsRef<Path>>(root_dir: P) -> Self {
        StaticFilesPlugin {
            root_dir: root_dir.as_ref().to_path_buf(),
        }
    }
}

#[async_trait]
impl Plugin for StaticFilesPlugin {
    fn name(&self) -> &str {
        "static_files"
    }
    
    fn version(&self) -> &str {
        "1.0.0"
    }
    
    async fn init(&self) -> Result<(), Box<dyn Error>> {
        // Verify the root directory exists
        if !self.root_dir.exists() {
            return Err("Root directory does not exist".into());
        }
        Ok(())
    }
    
    async fn register(&self, router: &Router) -> Result<(), Box<dyn Error>> {
        let handler = StaticFileHandler::new(&self.root_dir);
        
        // Register catch-all route for static files
        router.add_route(Method::GET, "/*", handler).await;
        
        Ok(())
    }
    
    async fn shutdown(&self) -> Result<(), Box<dyn Error>> {
        Ok(())
    }
}

#[no_mangle]
pub extern "C" fn _create_plugin() -> *mut dyn Plugin {
    // Read config from environment variable or default
    let root_dir = std::env::var("STATIC_FILES_ROOT")
        .unwrap_or_else(|_| "./static".to_string());
    
    let plugin = StaticFilesPlugin::new(root_dir);
    Box::into_raw(Box::new(plugin))
}
```

### Phase 4: Security Features

Implement security features to protect your server:

```rust
// src/security/tls.rs
use rustls::{Certificate, PrivateKey, ServerConfig};
use std::fs::File;
use std::io::BufReader;
use std::path::Path;

pub struct TlsConfig {
    cert_path: String,
    key_path: String,
    client_auth: bool,
    client_ca_path: Option<String>,
}

impl TlsConfig {
    pub fn new(
        cert_path: String,
        key_path: String,
        client_auth: bool,
        client_ca_path: Option<String>,
    ) -> Self {
        TlsConfig {
            cert_path,
            key_path,
            client_auth,
            client_ca_path,
        }
    }
    
    pub fn build_server_config(&self) -> Result<ServerConfig, Box<dyn std::error::Error>> {
        // Load server certificate and key
        let cert_file = File::open(&self.cert_path)?;
        let key_file = File::open(&self.key_path)?;
        
        let cert_chain = load_certificates(&cert_file)?;
        let mut keys = load_private_keys(&key_file)?;
        
        if keys.is_empty() {
            return Err("No private keys found".into());
        }
        
        let mut config_builder = ServerConfig::builder()
            .with_safe_defaults();
        
        // Configure client authentication if enabled
        if self.client_auth {
            if let Some(ca_path) = &self.client_ca_path {
                let ca_file = File::open(ca_path)?;
                let ca_certs = load_certificates(&ca_file)?;
                
                let mut client_auth_roots = rustls::RootCertStore::empty();
                for cert in ca_certs {
                    client_auth_roots.add(&cert)?;
                }
                
                config_builder = config_builder
                    .with_client_cert_verifier(
                        rustls::server::AllowAnyAuthenticatedClient::new(client_auth_roots)
                    );
            } else {
                return Err("Client authentication enabled but no CA certificate provided".into());
            }
        } else {
            config_builder = config_builder.with_no_client_auth();
        }
        
        let config = config_builder
            .with_single_cert(cert_chain, keys.remove(0))?;
        
        Ok(config)
    }
}

fn load_certificates(reader: &File) -> Result<Vec<Certificate>, Box<dyn std::error::Error>> {
    let mut reader = BufReader::new(reader);
    let certs = rustls_pemfile::certs(&mut reader)?
        .iter()
        .map(|v| Certificate(v.clone()))
        .collect();
    
    Ok(certs)
}

fn load_private_keys(reader: &File) -> Result<Vec<PrivateKey>, Box<dyn std::error::Error>> {
    let mut reader = BufReader::new(reader);
    
    // Try PKCS8 format first
    let keys = rustls_pemfile::pkcs8_private_keys(&mut reader)?
        .iter()
        .map(|v| PrivateKey(v.clone()))
        .collect::<Vec<_>>();
    
    if !keys.is_empty() {
        return Ok(keys);
    }
    
    // Reset reader and try RSA format
    reader.seek(std::io::SeekFrom::Start(0))?;
    let keys = rustls_pemfile::rsa_private_keys(&mut reader)?
        .iter()
        .map(|v| PrivateKey(v.clone()))
        .collect::<Vec<_>>();
    
    if !keys.is_empty() {
        return Ok(keys);
    }
    
    Err("No private keys found".into())
}

// src/middleware/ratelimit.rs
use async_trait::async_trait;
use dashmap::DashMap;
use hyper::{Body, Request, Response, StatusCode};
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;

struct RateLimiter {
    // Token bucket algorithm
    max_tokens: u32,
    tokens_per_second: f64,
    last_refill: Instant,
    tokens: u32,
}

impl RateLimiter {
    fn new(max_tokens: u32, tokens_per_second: f64) -> Self {
        RateLimiter {
            max_tokens,
            tokens_per_second,
            last_refill: Instant::now(),
            tokens: max_tokens,
        }
    }
    
    fn try_acquire(&mut self, count: u32) -> bool {
        self.refill();
        
        if self.tokens >= count {
            self.tokens -= count;
            true
        } else {
            false
        }
    }
    
    fn refill(&mut self) {
        let now = Instant::now();
        let elapsed = now.duration_since(self.last_refill);
        let new_tokens = (elapsed.as_secs_f64() * self.tokens_per_second) as u32;
        
        if new_tokens > 0 {
            self.tokens = (self.tokens + new_tokens).min(self.max_tokens);
            self.last_refill = now;
        }
    }
}

pub struct RateLimitMiddleware {
    limits: Arc<DashMap<IpAddr, Arc<Mutex<RateLimiter>>>>,
    requests_per_second: f64,
    burst_size: u32,
}

impl RateLimitMiddleware {
    pub fn new(requests_per_second: f64, burst_size: u32) -> Self {
        RateLimitMiddleware {
            limits: Arc::new(DashMap::new()),
            requests_per_second,
            burst_size,
        }
    }
    
    fn get_ip(req: &Request<Body>) -> Option<IpAddr> {
        // Try to get client IP from X-Forwarded-For header
        if let Some(header) = req.headers().get("X-Forwarded-For") {
            if let Ok(header_str) = header.to_str() {
                if let Some(ip_str) = header_str.split(',').next() {
                    if let Ok(ip) = ip_str.trim().parse() {
                        return Some(ip);
                    }
                }
            }
        }
        
        // Fall back to remote address from connection info
        req.extensions().get::<std::net::SocketAddr>()
            .map(|addr| addr.ip())
    }
}

#[async_trait]
impl Middleware for RateLimitMiddleware {
    async fn process(&self, req: Request<Body>) -> Result<Request<Body>, Response<Body>> {
        if let Some(ip) = Self::get_ip(&req) {
            // Get or create rate limiter for this IP
            let limiter = self.limits
                .entry(ip)
                .or_insert_with(|| {
                    Arc::new(Mutex::new(RateLimiter::new(
                        self.burst_size,
                        self.requests_per_second,
                    )))
                })
                .clone();
            
            // Try to acquire a token
            let allowed = {
                let mut limiter = limiter.lock().await;
                limiter.try_acquire(1)
            };
            
            if !allowed {
                return Err(Response::builder()
                    .status(StatusCode::TOO_MANY_REQUESTS)
                    .header("Retry-After", "1")
                    .body(Body::from("Rate limit exceeded"))
                    .unwrap());
            }
        }
        
        Ok(req)
    }
}
```

### Phase 5: Observability

Implement logging, metrics, and tracing:

```rust
// src/observability/metrics.rs
use prometheus::{
    register_counter_vec, register_histogram_vec,
    CounterVec, HistogramVec, Registry,
};
use std::error::Error;
use std::sync::Arc;

pub struct Metrics {
    registry: Registry,
    http_requests_total: CounterVec,
    http_request_duration_seconds: HistogramVec,
}

impl Metrics {
    pub fn new() -> Result<Self, Box<dyn Error>> {
        let registry = Registry::new();
        
        let http_requests_total = register_counter_vec!(
            "http_requests_total",
            "Total number of HTTP requests",
            &["method", "path", "status"]
        )?;
        
        let http_request_duration_seconds = register_histogram_vec!(
            "http_request_duration_seconds",
            "HTTP request duration in seconds",
            &["method", "path"],
            vec![0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        )?;
        
        registry.register(Box::new(http_requests_total.clone()))?;
        registry.register(Box::new(http_request_duration_seconds.clone()))?;
        
        Ok(Metrics {
            registry,
            http_requests_total,
            http_request_duration_seconds,
        })
    }
    
    pub fn increment_counter(&self, name: &str, labels: &[&str]) {
        match name {
            "http_requests_total" => {
                if labels.len() >= 3 {
                    self.http_requests_total
                        .with_label_values(&[labels[0], labels[1], labels[2]])
                        .inc();
                }
            }
            // Add other counters as needed
            _ => {}
        }
    }
    
    pub fn start_timer(&self, name: &str, labels: &[&str]) -> Timer {
        match name {
            "http_request_duration_seconds" => {
                if labels.len() >= 2 {
                    let timer = self.http_request_duration_seconds
                        .with_label_values(&[labels[0], labels[1]])
                        .start_timer();
                    
                    Timer::new(timer)
                } else {
                    Timer::noop()
                }
            }
            // Add other timers as needed
            _ => Timer::noop(),
        }
    }
    
    pub fn gather(&self) -> Vec<prometheus::proto::MetricFamily> {
        self.registry.gather()
    }
}

pub struct Timer {
    inner: Option<prometheus::HistogramTimer>,
}

impl Timer {
    fn new(timer: prometheus::HistogramTimer) -> Self {
        Timer { inner: Some(timer) }
    }
    
    fn noop() -> Self {
        Timer { inner: None }
    }
    
    pub fn observe_duration(self) {
        if let Some(timer) = self.inner {
            timer.observe_duration();
        }
    }
}
```

### Phase 6: WebSocket Support

Add WebSocket protocol support:

```rust
// src/server/websocket.rs
use futures::{SinkExt, StreamExt};
use hyper::upgrade::Upgraded;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, RwLock};
use tokio_tungstenite::{WebSocketStream, tungstenite::Message};

type PeerMap = Arc<RwLock<std::collections::HashMap<String, mpsc::Sender<Message>>>>;

pub struct WebSocketHandler {
    peer_map: PeerMap,
    broadcast_tx: broadcast::Sender<(String, Message)>,
}

impl WebSocketHandler {
    pub fn new() -> Self {
        let (broadcast_tx, _) = broadcast::channel(100);
        
        WebSocketHandler {
            peer_map: Arc::new(RwLock::new(std::collections::HashMap::new())),
            broadcast_tx,
        }
    }
    
    pub async fn handle_connection(
        &self,
        upgraded: Upgraded,
        client_id: String,
    ) {
        // Set up WebSocket connection
        let ws_stream = WebSocketStream::from_raw_socket(
            upgraded,
            tokio_tungstenite::tungstenite::protocol::Role::Server,
            None,
        ).await;
        
        // Split the WebSocket stream
        let (mut ws_tx, mut ws_rx) = ws_stream.split();
        
        // Create a channel for sending messages to this client
        let (tx, mut rx) = mpsc::channel::<Message>(32);
        
        // Store the sender in the peer map
        {
            let mut peer_map = self.peer_map.write().await;
            peer_map.insert(client_id.clone(), tx);
        }
        
        // Subscribe to broadcast messages
        let mut broadcast_rx = self.broadcast_tx.subscribe();
        
        // Forward broadcast messages to the WebSocket
        let client_id_clone = client_id.clone();
        let broadcast_task = tokio::spawn(async move {
            while let Ok((src_id, msg)) = broadcast_rx.recv().await {
                // Don't send messages back to the sender
                if src_id != client_id_clone {
                    if ws_tx.send(msg).await.is_err() {
                        break;
                    }
                }
            }
        });
        
        // Forward direct messages to the WebSocket
        let direct_task = tokio::spawn(async move {
            while let Some(msg) = rx.recv().await {
                if ws_tx.send(msg).await.is_err() {
                    break;
                }
            }
        });
        
        // Process incoming messages
        let broadcast_tx = self.broadcast_tx.clone();
        let client_id_clone = client_id.clone();
        let process_task = tokio::spawn(async move {
            while let Some(result) = ws_rx.next().await {
                match result {
                    Ok(msg) => {
                        // Handle different message types
                        match msg {
                            Message::Text(_) | Message::Binary(_) => {
                                // Broadcast the message to all clients
                                let _ = broadcast_tx.send((client_id_clone.clone(), msg));
                            }
                            Message::Ping(data) => {
                                // Respond with pong
                                if let Err(_) = ws_tx.send(Message::Pong(data)).await {
                                    break;
                                }
                            }
                            Message::Close(_) => {
                                break;
                            }
                            _ => {}
                        }
                    }
                    Err(_) => break,
                }
            }
        });
        
        // Wait for any task to complete
        tokio::select! {
            _ = broadcast_task => {},
            _ = direct_task => {},
            _ = process_task => {},
        }
        
        // Remove client from peer map
        {
            let mut peer_map = self.peer_map.write().await;
            peer_map.remove(&client_id);
        }
    }
    
    pub async fn send_to_client(
        &self,
        client_id: &str,
        message: Message,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let peers = self.peer_map.read().await;
        
        if let Some(tx) = peers.get(client_id) {
            tx.send(message)
                .await
                .map_err(|e| format!("Failed to send message: {}", e))?;
            Ok(())
        } else {
            Err("Client not found".into())
        }
    }
    
    pub async fn broadcast_message(
        &self,
        message: Message,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Use empty string as source to broadcast to all
        self.broadcast_tx
            .send(("".to_string(), message))
            .map_err(|e| format!("Failed to broadcast message: {}", e))?;
        Ok(())
    }
}
```

### Phase 7: Deployment Configuration

Set up Docker and CI/CD configuration:

```dockerfile
# Dockerfile
FROM rust:1.72 as builder

# Create empty project for dependency caching
WORKDIR /usr/src/app
RUN cargo new --bin rustyserve
WORKDIR /usr/src/app/rustyserve

# Copy manifests
COPY Cargo.toml Cargo.lock ./

# Build dependencies
RUN cargo build --release

# Copy source code
COPY src ./src
COPY examples ./examples

# Build the application
RUN cargo build --release

# Create the runtime image
FROM debian:bookworm-slim

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user
RUN groupadd -r rustyserve && useradd -r -g rustyserve rustyserve

# Copy the binary
COPY --from=builder /usr/src/app/rustyserve/target/release/rustyserve /usr/local/bin/

# Create directories
RUN mkdir -p /etc/rustyserve /var/lib/rustyserve /var/log/rustyserve \
    && chown -R rustyserve:rustyserve /etc/rustyserve /var/lib/rustyserve /var/log/rustyserve

# Copy configuration
COPY config/config.toml /etc/rustyserve/

# Switch to non-root user
USER rustyserve

# Expose ports
EXPOSE 8080 8443

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/rustyserve"]
CMD ["--config", "/etc/rustyserve/config.toml"]
```

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: clippy, rustfmt
      
      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
      
      - name: Check format
        run: cargo fmt -- --check
      
      - name: Clippy
        run: cargo clippy -- -D warnings
      
      - name: Run tests
        run: cargo test -- --nocapture
      
      - name: Security audit
        run: |
          cargo install cargo-audit
          cargo audit
  
  build:
    name: Build
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: yourusername/rustyserve
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=sha,format=short
      
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
```

## Evaluation Criteria

Your capstone project will be evaluated on the following criteria:

### 1. Functionality
- All core requirements are implemented
- Server handles HTTP and WebSocket protocols correctly
- Plugin system works as expected
- Security features are properly implemented

### 2. Code Quality
- Code follows Rust best practices
- Proper error handling throughout the codebase
- Good separation of concerns
- Clear and consistent naming conventions

### 3. Performance
- Server handles concurrent connections efficiently
- Resource usage is optimized
- Performance benchmarks show good results

### 4. Documentation
- Code is well-documented with comments
- API documentation is clear and complete
- Architecture diagrams explain system design
- README and user guides are provided

### 5. Testing
- Unit tests cover critical components
- Integration tests verify system behavior
- Benchmarks measure performance
- Security tests validate protection measures

### 6. Deployability
- Docker container works as expected
- CI/CD pipeline is configured
- Configuration management is flexible
- Observability features work in production

## Resources

- [Hyper Documentation](https://hyper.rs/guides/)
- [Tokio Documentation](https://tokio.rs/tokio/tutorial)
- [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/)
- [Rust Performance Book](https://nnethercote.github.io/perf-book/)
- [Secure Coding Guidelines](https://www.rust-lang.org/security.html)

## Conclusion

This capstone project brings together everything you've learned throughout the tutorial series. By building a production-ready webserver from scratch, you've demonstrated your mastery of Rust web development concepts and best practices.

Remember that real-world systems are never truly "finished." Even after completing this capstone project, there will be opportunities for improvement, optimization, and extension. The skills you've developed will serve you well as you continue your journey as a Rust web developer.

Congratulations on completing the Rusty Webserver Tutorial series!

[Previous: Case Studies](41-case-studies.md) | [Return to Tutorial Index](00-introduction.md)
