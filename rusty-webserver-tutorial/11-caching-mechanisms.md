<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\11-caching-mechanisms.md -->
# Implementing Caching Mechanisms

## Navigation
- [Previous: Reverse Proxy](10-reverse-proxy.md)
- [Next: Security Features](12-security-features.md)

In this tutorial, you'll add caching capabilities to your Rust web server to improve performance and reduce load on backend servers.

## Web Server Decisions

- **Cache Types:** In-memory cache vs. disk-based cache
- **Cache Control:** Honor HTTP cache headers
- **Cache Invalidation:** Time-based vs. manual invalidation

## Rust Decisions

- **Data Structures:** Use `HashMap` with LRU eviction policy
- **Concurrency:** Thread-safe access via `Arc<Mutex<>>` or `dashmap`
- **Memory Management:** Balance between memory usage and performance

## How Caching Works

```mermaid
flowchart TD
    Request[📡 Client Request] --> CacheCheck{🔍 In Cache?}
    CacheCheck -->|✅ Yes| CacheValid{⏰ Cache Valid?}
    CacheCheck -->|❌ No| Fetch[🌐 Fetch Resource]

    CacheValid -->|✅ Yes| Serve[⚡ Serve from Cache]
    CacheValid -->|❌ No| Fetch

    Fetch --> Store[💾 Store in Cache]
    Store --> ServeNew[📤 Serve New Response]

    Serve --> Response[📤 Send Response]
    ServeNew --> Response

    %% Enhanced styling for better contrast
    style Request fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    style CacheCheck fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    style CacheValid fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    style Fetch fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
    style Store fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    style Serve fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style ServeNew fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style Response fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
```

*This diagram shows the caching decision flow for each request.*

## Step-by-Step Implementation

### 1. Add Required Dependencies

First, add the necessary dependencies to `Cargo.toml`:

```toml
[dependencies]
# Existing dependencies...

# Caching related
lru = "0.10"
dashmap = "5.4"
chrono = { version = "0.4", features = ["serde"] }
```

### 2. Create a Cache Module

Create a new file at `src/cache/mod.rs` to implement the caching system:

```rust
use chrono::{DateTime, Duration, Utc};
use dashmap::DashMap;
use lru::LruCache;
use std::hash::Hash;
use std::num::NonZeroUsize;
use std::sync::{Arc, Mutex};

use crate::error::Result;
use crate::http::Response;

/// Cache entry with response and metadata
pub struct CacheEntry {
    /// The cached HTTP response
    pub response: Response,
    /// When this entry was added to the cache
    pub created_at: DateTime<Utc>,
    /// When this entry should expire (None = never)
    pub expires_at: Option<DateTime<Utc>>,
    /// ETag value (if available)
    pub etag: Option<String>,
}

/// Cache configuration
pub struct CacheConfig {
    /// Maximum number of items to store in the cache
    pub max_entries: usize,
    /// Default time-to-live for cache entries
    pub default_ttl: Duration,
    /// Whether to respect Cache-Control headers
    pub respect_cache_control: bool,
    /// Whether to enable ETag validation
    pub use_etags: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_entries: 10_000,
            default_ttl: Duration::minutes(5),
            respect_cache_control: true,
            use_etags: true,
        }
    }
}

/// Thread-safe cache implementation
pub struct Cache {
    /// The cached responses
    entries: Arc<DashMap<String, CacheEntry>>,
    /// LRU tracker to determine eviction order
    lru: Arc<Mutex<LruCache<String, ()>>>,
    /// Cache configuration
    config: CacheConfig,
}

impl Cache {
    /// Create a new cache with the given configuration
    pub fn new(config: CacheConfig) -> Self {
        let max_entries = NonZeroUsize::new(config.max_entries)
            .unwrap_or(NonZeroUsize::new(1).unwrap());
            
        Self {
            entries: Arc::new(DashMap::new()),
            lru: Arc::new(Mutex::new(LruCache::new(max_entries))),
            config,
        }
    }

    /// Get an entry from the cache if it exists and is valid
    pub fn get(&self, key: &str) -> Option<Response> {
        // Try to get the entry
        if let Some(entry) = self.entries.get(key) {
            // Check if it has expired
            if let Some(expires_at) = entry.expires_at {
                if Utc::now() > expires_at {
                    // Entry has expired, remove it
                    self.entries.remove(key);
                    return None;
                }
            }

            // Update LRU tracking
            if let Ok(mut lru) = self.lru.lock() {
                lru.put(key.to_string(), ());
            }
            
            // Clone the response for the caller
            return Some(entry.response.clone());
        }
        
        None
    }

    /// Put an entry into the cache
    pub fn put(&self, key: &str, response: Response, ttl: Option<Duration>) {
        // Calculate expiration time
        let ttl = ttl.unwrap_or(self.config.default_ttl);
        let expires_at = Utc::now() + ttl;
        
        // Extract ETag if present and enabled
        let etag = if self.config.use_etags {
            response.headers.get("etag").cloned()
        } else {
            None
        };
        
        // Create the cache entry
        let entry = CacheEntry {
            response,
            created_at: Utc::now(),
            expires_at: Some(expires_at),
            etag,
        };
        
        // Add to the cache and update LRU
        self.entries.insert(key.to_string(), entry);
        
        if let Ok(mut lru) = self.lru.lock() {
            // If we're at capacity, the LRU will automatically evict the oldest entry
            lru.put(key.to_string(), ());
            
            // Manually evict any entries over capacity
            if self.entries.len() > self.config.max_entries {
                if let Some((evicted_key, _)) = lru.pop_lru() {
                    self.entries.remove(&evicted_key);
                }
            }
        }
    }
    
    /// Check if an entry exists and is not expired
    pub fn contains_valid(&self, key: &str) -> bool {
        if let Some(entry) = self.entries.get(key) {
            if let Some(expires_at) = entry.expires_at {
                return Utc::now() <= expires_at;
            }
            return true; // No expiration means always valid
        }
        false
    }
    
    /// Remove an entry from the cache
    pub fn remove(&self, key: &str) {
        self.entries.remove(key);
    }
    
    /// Clear all entries from the cache
    pub fn clear(&self) {
        self.entries.clear();
        if let Ok(mut lru) = self.lru.lock() {
            lru.clear();
        }
    }
}
```

### 3. Create a Cache Handler

Create a file at `src/server/cache_handler.rs` to integrate caching with your server:

```rust
use std::sync::Arc;
use log::{debug, info};

use crate::cache::{Cache, CacheConfig, CacheEntry};
use crate::error::Result;
use crate::http::{Request, Response, StatusCode};

/// Handler for request caching
pub struct CacheHandler {
    /// The cache storage
    cache: Arc<Cache>,
}

impl CacheHandler {
    /// Create a new cache handler
    pub fn new(config: CacheConfig) -> Self {
        Self {
            cache: Arc::new(Cache::new(config)),
        }
    }
    
    /// Get a cache key for a request
    fn get_cache_key(&self, request: &Request) -> String {
        // Create a key that includes host and path
        let host = request.headers.get("host").cloned().unwrap_or_default();
        format!("{}:{}", host, request.path)
    }
    
    /// Process a request, using or updating the cache as appropriate
    pub fn process(&self, request: &Request, handler: impl Fn(&Request) -> Result<Response>) -> Result<Response> {
        // Only cache GET and HEAD requests
        if request.method.to_string() != "GET" && request.method.to_string() != "HEAD" {
            return handler(request);
        }
        
        // Get the cache key
        let key = self.get_cache_key(request);
        
        // Check if we have a cached response
        if let Some(cached_response) = self.cache.get(&key) {
            debug!("Cache hit for {}", request.path);
            
            // Check for If-None-Match header (ETag validation)
            if let Some(if_none_match) = request.headers.get("if-none-match") {
                if let Some(etag) = cached_response.headers.get("etag") {
                    if if_none_match == etag {
                        // Return 304 Not Modified
                        let mut not_modified = Response::new(StatusCode::NotModified);
                        not_modified.headers.insert("ETag".to_string(), etag.clone());
                        return Ok(not_modified);
                    }
                }
            }
            
            return Ok(cached_response);
        }
        
        // Cache miss, get the response from the handler
        debug!("Cache miss for {}", request.path);
        let response = handler(request)?;
        
        // Only cache successful responses
        if response.status.is_success() {
            // Check for Cache-Control header
            let mut should_cache = true;
            let mut ttl = None;
            
            if let Some(cache_control) = response.headers.get("cache-control") {
                if cache_control.contains("no-store") || cache_control.contains("private") {
                    should_cache = false;
                }
                
                // Parse max-age directive
                if let Some(max_age_str) = cache_control
                    .split(',')
                    .find(|s| s.trim().starts_with("max-age="))
                {
                    if let Some(max_age_str) = max_age_str.trim().strip_prefix("max-age=") {
                        if let Ok(seconds) = max_age_str.parse::<i64>() {
                            if seconds > 0 {
                                ttl = Some(chrono::Duration::seconds(seconds));
                            } else {
                                should_cache = false;
                            }
                        }
                    }
                }
            }
            
            // Store the response in the cache
            if should_cache {
                self.cache.put(&key, response.clone(), ttl);
            }
        }
        
        Ok(response)
    }
    
    /// Invalidate a specific cache entry
    pub fn invalidate(&self, path: &str) {
        self.cache.remove(path);
    }
    
    /// Clear the entire cache
    pub fn clear(&self) {
        info!("Clearing cache");
        self.cache.clear();
    }
}
```

### 4. Update the Virtual Host Config for Caching

Modify `src/config/mod.rs` to include cache configuration:

```rust
/// Virtual host configuration
#[derive(Debug, Clone)]
pub struct VirtualHostConfig {
    // Existing fields...
    
    /// Caching configuration
    pub cache_config: Option<CacheConfig>,
}
```

### 5. Integrate Caching into the Server

Update your server's request handler in `src/server/mod.rs` to use the cache:

```rust
/// Handle a request
pub fn handle_request(&self, request: &Request) -> Result<Response> {
    // Get the virtual host configuration based on the Host header
    let host = request.headers.get("host").unwrap_or(&String::from(""));
    let vhost = self.config.get_virtual_host(host);
    
    // Create a cache handler if caching is enabled for this host
    if let Some(ref cache_config) = vhost.cache_config {
        let cache_handler = CacheHandler::new(cache_config.clone());
        
        // Process the request through the cache handler
        return cache_handler.process(request, |req| {
            self.dispatch_request(req, vhost)
        });
    }
    
    // No caching, directly dispatch the request
    self.dispatch_request(request, vhost)
}

/// Dispatch a request to the appropriate handler
fn dispatch_request(&self, request: &Request, vhost: &VirtualHostConfig) -> Result<Response> {
    if let Some(ref proxy_config) = vhost.proxy {
        // Proxy handling logic...
    } else {
        // Static file handling logic...
    }
}
```

### 6. Add Cache Statistics and Monitoring

Create a file at `src/cache/stats.rs` to track cache performance:

```rust
use std::sync::atomic::{AtomicUsize, Ordering};

/// Cache statistics
#[derive(Debug, Default)]
pub struct CacheStats {
    /// Number of cache hits
    hits: AtomicUsize,
    /// Number of cache misses
    misses: AtomicUsize,
    /// Number of items in the cache
    entries: AtomicUsize,
    /// Number of evictions
    evictions: AtomicUsize,
}

impl CacheStats {
    /// Record a cache hit
    pub fn record_hit(&self) {
        self.hits.fetch_add(1, Ordering::Relaxed);
    }
    
    /// Record a cache miss
    pub fn record_miss(&self) {
        self.misses.fetch_add(1, Ordering::Relaxed);
    }
    
    /// Update the number of entries
    pub fn update_entries(&self, count: usize) {
        self.entries.store(count, Ordering::Relaxed);
    }
    
    /// Record an eviction
    pub fn record_eviction(&self) {
        self.evictions.fetch_add(1, Ordering::Relaxed);
    }
    
    /// Get the number of hits
    pub fn hits(&self) -> usize {
        self.hits.load(Ordering::Relaxed)
    }
    
    /// Get the number of misses
    pub fn misses(&self) -> usize {
        self.misses.load(Ordering::Relaxed)
    }
    
    /// Get the hit ratio
    pub fn hit_ratio(&self) -> f64 {
        let hits = self.hits();
        let total = hits + self.misses();
        if total > 0 {
            hits as f64 / total as f64
        } else {
            0.0
        }
    }
    
    /// Get the number of entries
    pub fn entries(&self) -> usize {
        self.entries.load(Ordering::Relaxed)
    }
    
    /// Get the number of evictions
    pub fn evictions(&self) -> usize {
        self.evictions.load(Ordering::Relaxed)
    }
}
```

## Testing and Benchmarking

Create a benchmark at `benches/cache_benchmark.rs`:

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

use rusty_server::cache::{Cache, CacheConfig};
use rusty_server::http::{Response, StatusCode};
use chrono::Duration;

fn benchmark_cache(c: &mut Criterion) {
    // Create a cache with default config
    let config = CacheConfig::default();
    let cache = Cache::new(config);
    
    // Create a sample response
    let response = Response::new(StatusCode::OK)
        .with_body("Hello, world!");
    
    c.bench_function("cache_put", |b| {
        b.iter(|| {
            for i in 0..100 {
                let key = format!("key-{}", i);
                cache.put(&key, response.clone(), None);
            }
        })
    });
    
    // Populate the cache
    for i in 0..1000 {
        let key = format!("key-{}", i);
        cache.put(&key, response.clone(), None);
    }
    
    c.bench_function("cache_get_hit", |b| {
        b.iter(|| {
            for i in 0..100 {
                let key = format!("key-{}", i);
                black_box(cache.get(&key));
            }
        })
    });
    
    c.bench_function("cache_get_miss", |b| {
        b.iter(|| {
            for i in 1000..1100 {
                let key = format!("key-{}", i);
                black_box(cache.get(&key));
            }
        })
    });
}

criterion_group!(benches, benchmark_cache);
criterion_main!(benches);
```

## Unit Testing

Create tests at `tests/cache_tests.rs`:

```rust
#[cfg(test)]
mod tests {
    use rusty_server::cache::{Cache, CacheConfig};
    use rusty_server::http::{Response, StatusCode};
    use chrono::Duration;
    
    #[test]
    fn test_cache_basic() {
        let config = CacheConfig::default();
        let cache = Cache::new(config);
        
        let response = Response::new(StatusCode::OK)
            .with_body("Test");
        
        // Put and get
        cache.put("test", response.clone(), None);
        let cached = cache.get("test");
        assert!(cached.is_some());
        assert_eq!(cached.unwrap().body, "Test".as_bytes());
        
        // Missing key
        assert!(cache.get("missing").is_none());
    }
    
    #[test]
    fn test_cache_expiration() {
        let config = CacheConfig::default();
        let cache = Cache::new(config);
        
        let response = Response::new(StatusCode::OK)
            .with_body("Test");
        
        // Add with short TTL
        cache.put("test", response.clone(), Some(Duration::milliseconds(10)));
        
        // Should be in cache immediately
        assert!(cache.get("test").is_some());
        
        // Wait for expiration
        std::thread::sleep(std::time::Duration::from_millis(20));
        
        // Should be gone now
        assert!(cache.get("test").is_none());
    }
    
    #[test]
    fn test_cache_eviction() {
        let mut config = CacheConfig::default();
        config.max_entries = 5;
        let cache = Cache::new(config);
        
        let response = Response::new(StatusCode::OK)
            .with_body("Test");
        
        // Fill the cache
        for i in 0..10 {
            cache.put(&format!("key-{}", i), response.clone(), None);
        }
        
        // Check that early entries were evicted
        for i in 0..5 {
            assert!(cache.get(&format!("key-{}", i)).is_none());
        }
        
        // Check that recent entries are still there
        for i in 5..10 {
            assert!(cache.get(&format!("key-{}", i)).is_some());
        }
    }
}
```

## Advanced Caching Techniques

### 1. Disk-Based Caching

For long-term caching, consider adding disk-based storage:

```rust
/// Store a response on disk
fn store_on_disk(&self, key: &str, response: &Response) -> Result<()> {
    let path = self.disk_cache_path.join(key.replace("/", "_"));
    let file = std::fs::File::create(path)?;
    let mut writer = std::io::BufWriter::new(file);
    
    // Serialize the response
    serde_json::to_writer(&mut writer, response)?;
    
    Ok(())
}

/// Load a response from disk
fn load_from_disk(&self, key: &str) -> Result<Option<Response>> {
    let path = self.disk_cache_path.join(key.replace("/", "_"));
    
    if !path.exists() {
        return Ok(None);
    }
    
    let file = std::fs::File::open(path)?;
    let reader = std::io::BufReader::new(file);
    
    // Deserialize the response
    let response = serde_json::from_reader(reader)?;
    Ok(Some(response))
}
```

### 2. Distributed Caching

For multi-server deployments, consider using Redis or Memcached:

```rust
#[cfg(feature = "redis")]
mod redis_cache {
    use redis::{Client, Commands};
    
    pub struct RedisCache {
        client: Client,
    }
    
    impl RedisCache {
        pub fn new(url: &str) -> Result<Self> {
            let client = redis::Client::open(url)?;
            Ok(Self { client })
        }
        
        pub fn get(&self, key: &str) -> Result<Option<Response>> {
            let mut conn = self.client.get_connection()?;
            let data: Option<String> = conn.get(key)?;
            
            match data {
                Some(json) => {
                    let response: Response = serde_json::from_str(&json)?;
                    Ok(Some(response))
                }
                None => Ok(None),
            }
        }
        
        pub fn put(&self, key: &str, response: &Response, ttl: Option<Duration>) -> Result<()> {
            let mut conn = self.client.get_connection()?;
            let json = serde_json::to_string(response)?;
            
            if let Some(ttl) = ttl {
                conn.set_ex(key, json, ttl.num_seconds() as usize)?;
            } else {
                conn.set(key, json)?;
            }
            
            Ok(())
        }    }
}
```

## Security Considerations

When implementing caching mechanisms, it's crucial to address several security concerns to prevent cache-based attacks and data leakage.

```mermaid
flowchart TD
    A[Cache Security Threats] --> B[Cache Poisoning]
    A --> C[Information Disclosure]
    A --> D[Cache Probing]
    A --> E[Cache Deception]
    A --> F[Side-channel Attacks]
    A --> G[Resource Exhaustion]
    
    B --> B1[Malicious Cache Keys]
    B --> B2[Header Injection]
    
    C --> C1[User Data Leakage]
    C --> C2[Sensitive Header Exposure]
    
    D --> D1[Cache Timing Analysis]
    
    E --> E1[Cache Key Manipulation]
    
    F --> F1[CPU Cache Timing]
    
    G --> G1[Memory Exhaustion]
    G --> G2[Cache Denial of Service]
```

### 1. Cache Key Security

Cache keys must be carefully constructed to prevent cache poisoning and path traversal:

```rust
/// Constants for cache key security
pub const MAX_CACHE_KEY_LENGTH: usize = 1024;
pub const FORBIDDEN_KEY_CHARS: [char; 5] = ['\\', '?', '*', ':', '|'];

/// Create a secure cache key
pub fn create_secure_cache_key(request: &Request) -> Result<String> {
    // Base key on method, path, and relevant headers
    let mut key_parts = vec![
        request.method.to_string(),
        sanitize_path(&request.uri.path),
    ];
    
    // Add query parameters if present
    if let Some(query) = &request.uri.query {
        if validate_query(query) {
            key_parts.push(sanitize_query(query));
        } else {
            return Err(Error::new("Invalid query parameters"));
        }
    }
    
    // Add vary headers
    for header in &["Accept-Encoding", "Accept-Language"] {
        if let Some(value) = request.headers.get(*header) {
            key_parts.push(format!("{}:{}", header, value));
        }
    }
    
    // Construct the key
    let key = key_parts.join("::");
    
    // Validate key length
    if key.len() > MAX_CACHE_KEY_LENGTH {
        return Err(Error::new("Cache key exceeds maximum length"));
    }
    
    // Hash the key to ensure fixed length and prevent manipulation
    let hashed_key = sha256_hash(&key);
    Ok(hashed_key)
}

/// Sanitize path for cache key
fn sanitize_path(path: &str) -> String {
    let normalized = path.trim_start_matches('/').trim_end_matches('/');
    // Remove path traversal attempts
    normalized
        .replace("../", "")
        .replace("./", "")
        .chars()
        .filter(|c| !FORBIDDEN_KEY_CHARS.contains(c))
        .collect()
}

/// Sanitize query string for cache key
fn sanitize_query(query: &str) -> String {
    // Sort parameters to ensure consistent cache keys
    let mut params: Vec<(String, String)> = vec![];
    
    for param in query.split('&') {
        if let Some((key, value)) = param.split_once('=') {
            params.push((
                key.to_string(),
                value.chars()
                    .filter(|c| !FORBIDDEN_KEY_CHARS.contains(c))
                    .collect(),
            ));
        }
    }
    
    params.sort_by(|a, b| a.0.cmp(&b.0));
    
    params
        .into_iter()
        .map(|(k, v)| format!("{}={}", k, v))
        .collect::<Vec<_>>()
        .join("&")
}

/// Validate query string for potential attacks
fn validate_query(query: &str) -> bool {
    // Check for suspicious patterns
    let suspicious_patterns = [
        "script", "eval(", "javascript:", "<script", "onerror=", "onload=",
        "SELECT", "UNION", "INSERT", "DELETE", "UPDATE", "DROP", "exec(",
    ];
    
    for pattern in suspicious_patterns {
        if query.to_lowercase().contains(pattern) {
            return false;
        }
    }
    
    true
}
```

### 2. Cache Content Validation

Implement validation for cached content to prevent security issues:

```rust
/// Validate response before caching
pub fn validate_cacheable_response(response: &Response) -> bool {
    // Only cache successful responses
    if response.status_code < 200 || response.status_code >= 300 {
        return false;
    }
    
    // Don't cache responses with personal or sensitive data
    let has_sensitive_headers = response.headers.iter().any(|(k, _)| {
        k == "Authorization" || k == "Set-Cookie" || k == "WWW-Authenticate"
    });
    
    if has_sensitive_headers {
        return false;
    }
    
    // Check cache-control headers
    if let Some(cache_control) = response.headers.get("Cache-Control") {
        if cache_control.contains("no-store") || cache_control.contains("private") {
            return false;
        }
    }
    
    // Additional security validations
    let content_type = response.headers.get("Content-Type").unwrap_or("");
    
    // Avoid caching executable content
    let unsafe_types = ["application/x-executable", "application/octet-stream"];
    for unsafe_type in unsafe_types {
        if content_type.starts_with(unsafe_type) {
            return false;
        }
    }
    
    true
}

/// Detect potentially unsafe content in responses
fn detect_unsafe_content(body: &[u8], content_type: &str) -> bool {
    // For text content, check for script injections
    if content_type.starts_with("text/html") || content_type.starts_with("application/javascript") {
        let content = String::from_utf8_lossy(body);
        let dangerous_patterns = [
            "<script>eval(", "javascript:alert(", "document.cookie",
            "XMLHttpRequest", "fetch(", "execScript(",
        ];
        
        for pattern in dangerous_patterns {
            if content.contains(pattern) {
                return true;
            }
        }
    }
    
    false
}
```

### 3. Cache Partitioning

Implement cache partitioning to prevent information leakage between users:

```rust
/// Cache partitioning configuration
pub struct CachePartitionConfig {
    /// Whether to use per-user caching
    pub enable_user_partitioning: bool,
    /// Whether to partition by TLS session
    pub enable_tls_partitioning: bool,
    /// Whether to partition by origin
    pub enable_origin_partitioning: bool,
}

/// Create a partitioned cache key
pub fn create_partitioned_cache_key(
    request: &Request, 
    config: &CachePartitionConfig
) -> Result<String> {
    let mut partition_components = vec![];
    
    // Base cache key
    let base_key = create_secure_cache_key(request)?;
    
    // Add user identity for per-user caching (if enabled)
    if config.enable_user_partitioning {
        if let Some(user_id) = extract_user_id(request) {
            partition_components.push(format!("user:{}", user_id));
        }
    }
    
    // Add TLS session ID for TLS partitioning (if enabled)
    if config.enable_tls_partitioning {
        if let Some(tls_id) = request.tls_session_id() {
            partition_components.push(format!("tls:{}", tls_id));
        }
    }
    
    // Add origin for origin partitioning (if enabled)
    if config.enable_origin_partitioning {
        if let Some(origin) = request.headers.get("Origin") {
            partition_components.push(format!("origin:{}", origin));
        } else if let Some(referer) = request.headers.get("Referer") {
            if let Some(origin_from_referer) = extract_origin_from_referer(referer) {
                partition_components.push(format!("origin:{}", origin_from_referer));
            }
        }
    }
    
    // If no partitioning is applied, just use the base key
    if partition_components.is_empty() {
        return Ok(base_key);
    }
    
    // Combine base key with partition components
    let combined = format!("{}:{}", base_key, partition_components.join(":"));
    
    // Hash the combined key for consistent length and security
    Ok(sha256_hash(&combined))
}

/// Extract user ID from request (e.g., from cookie or auth header)
fn extract_user_id(request: &Request) -> Option<String> {
    // Extract from Authorization header
    if let Some(auth) = request.headers.get("Authorization") {
        if auth.starts_with("Bearer ") {
            let token = &auth[7..];
            if let Some(user_id) = extract_user_from_jwt(token) {
                return Some(user_id);
            }
        }
    }
    
    // Extract from session cookie
    if let Some(cookie) = request.headers.get("Cookie") {
        if let Some(session_id) = extract_session_from_cookie(cookie) {
            return Some(format!("session:{}", session_id));
        }
    }
    
    None
}
```

### 4. Cache Purging and Invalidation Security

Secure cache invalidation to prevent unauthorized cache manipulation:

```rust
/// Cache purge authentication config
struct PurgeAuthConfig {
    enabled: bool,
    tokens: Vec<String>,
    allowed_ips: Vec<String>,
}

/// Authenticate cache purge request
fn authenticate_cache_purge(request: &Request, config: &PurgeAuthConfig) -> bool {
    if !config.enabled {
        return true;
    }
    
    // Check client IP
    if let Some(client_ip) = request.client_ip() {
        if config.allowed_ips.contains(&client_ip.to_string()) {
            return true;
        }
    }
    
    // Check purge token
    if let Some(purge_token) = request.headers.get("X-Purge-Token") {
        if config.tokens.contains(&purge_token.to_string()) {
            return true;
        }
    }
    
    false
}

/// Secure cache invalidation
pub fn secure_cache_invalidation(
    request: &Request,
    cache: &dyn CacheBackend,
    auth_config: &PurgeAuthConfig
) -> Result<bool> {
    // Authenticate the purge request
    if !authenticate_cache_purge(request, auth_config) {
        return Err(Error::new("Unauthorized cache purge attempt"));
    }
    
    let path_pattern = request.headers.get("X-Purge-Pattern").unwrap_or("*");
    
    // Log the purge attempt
    log::info!(
        "Cache purge requested for pattern '{}' by {}",
        path_pattern,
        request.client_ip().unwrap_or("unknown".to_string())
    );
    
    // Perform the actual purge
    let purged = match path_pattern {
        "*" => cache.clear_all()?,
        _ => cache.clear_matching(path_pattern)?,
    };
    
    Ok(purged)
}
```

### 5. Cache-Specific DoS Protection

Implement protections against cache-based denial of service attacks:

```rust
/// Cache DoS protection configuration
pub struct CacheDosConfig {
    /// Maximum number of cache entries
    pub max_entries: usize,
    /// Maximum size per entry (bytes)
    pub max_entry_size: usize,
    /// Maximum memory usage (bytes)
    pub max_memory_usage: usize,
    /// Rate limit for cache operations
    pub rate_limits: CacheRateLimits,
}

/// Rate limits for cache operations
pub struct CacheRateLimits {
    /// Maximum cache operations per client per minute
    pub max_ops_per_minute: usize,
    /// Maximum invalidation requests per minute
    pub max_invalidations_per_minute: usize,
}

/// Track cache usage stats to prevent DoS
pub struct CacheUsageTracker {
    current_entries: AtomicUsize,
    current_memory_usage: AtomicUsize,
    client_ops: dashmap::DashMap<String, (usize, Instant)>,
    config: CacheDosConfig,
}

impl CacheUsageTracker {
    /// Check if a cache operation should be allowed
    pub fn allow_operation(&self, request: &Request, operation: &str, size: usize) -> bool {
        // Check entry size limit
        if size > self.config.max_entry_size {
            log::warn!(
                "Cache entry size ({} bytes) exceeds limit ({} bytes)",
                size,
                self.config.max_entry_size
            );
            return false;
        }
        
        // Check total memory usage
        let current_usage = self.current_memory_usage.load(Ordering::Relaxed);
        if current_usage + size > self.config.max_memory_usage {
            log::warn!("Cache memory usage would exceed limit");
            return false;
        }
        
        // Check entry count limit
        let current_entries = self.current_entries.load(Ordering::Relaxed);
        if current_entries >= self.config.max_entries {
            log::warn!("Cache entry count would exceed limit");
            return false;
        }
        
        // Rate limiting by client IP
        if let Some(client_ip) = request.client_ip() {
            let now = Instant::now();
            let mut is_rate_limited = false;
            
            // Update and check rate limits
            self.client_ops.entry(client_ip).and_modify(|(count, timestamp)| {
                // Reset counter if minute has passed
                if timestamp.elapsed() > Duration::from_secs(60) {
                    *timestamp = now;
                    *count = 1;
                } else {
                    *count += 1;
                    
                    // Check if rate limited
                    let limit = if operation == "invalidate" {
                        self.config.rate_limits.max_invalidations_per_minute
                    } else {
                        self.config.rate_limits.max_ops_per_minute
                    };
                    
                    if *count > limit {
                        is_rate_limited = true;
                    }
                }
            }).or_insert((1, now));
            
            if is_rate_limited {
                log::warn!("Client {} rate limited for cache operations", client_ip);
                return false;
            }
        }
        
        true
    }
    
    /// Update stats after cache operation
    pub fn update_stats(&self, operation: &str, size: usize) {
        match operation {
            "put" => {
                self.current_entries.fetch_add(1, Ordering::Relaxed);
                self.current_memory_usage.fetch_add(size, Ordering::Relaxed);
            }
            "delete" => {
                self.current_entries.fetch_sub(1, Ordering::Relaxed);
                self.current_memory_usage.fetch_sub(size, Ordering::Relaxed);
            }
            _ => {}
        }
    }
}
```

### 6. Cache Response Variance Security

Handle cache response variation securely:

```rust
/// Secure handling of Vary header
pub fn process_vary_header(request: &Request, response: &Response) -> Vec<String> {
    let mut vary_keys = vec![];
    
    // Process standard Vary header if present
    if let Some(vary) = response.headers.get("Vary") {
        for header in vary.split(',').map(|h| h.trim()) {
            if let Some(value) = request.headers.get(header) {
                vary_keys.push(format!("{}:{}", header, value));
            }
        }
    }
    
    // Always add these for security, even if not explicitly varied
    let security_headers = ["User-Agent", "Accept-Encoding", "Authorization"];
    for header in &security_headers {
        if !vary_keys.iter().any(|k| k.starts_with(&format!("{}:", header))) {
            if let Some(value) = request.headers.get(*header) {
                vary_keys.push(format!("{}:{}", header, value));
            }
        }
    }
    
    vary_keys
}

/// Generate cache vary ID
pub fn generate_cache_vary_id(request: &Request, response: &Response) -> String {
    let vary_keys = process_vary_header(request, response);
    
    if vary_keys.is_empty() {
        return "default".to_string();
    }
    
    // Sort for consistency
    let mut sorted_keys = vary_keys.clone();
    sorted_keys.sort();
    
    // Hash the combined keys for security
    sha256_hash(&sorted_keys.join("::"))
}
```

### 7. Client-Side Cache Security

Send appropriate headers to ensure client-side cache security:

```rust
/// Add security headers for client-side caching
pub fn add_cache_security_headers(response: &mut Response, cache_policy: &CachePolicy) {
    // For sensitive content, set strict no-cache directives
    if cache_policy.content_sensitivity == ContentSensitivity::High {
        response.headers.insert(
            "Cache-Control".to_string(),
            "no-store, no-cache, must-revalidate, max-age=0, private".to_string()
        );
        response.headers.insert(
            "Pragma".to_string(),
            "no-cache".to_string()
        );
        response.headers.insert(
            "Expires".to_string(),
            "0".to_string()
        );
    } else {
        // For non-sensitive content, set appropriate cache directives
        let mut directives = vec![];
        
        if cache_policy.allow_public_caching {
            directives.push("public");
        } else {
            directives.push("private");
        }
        
        if cache_policy.must_revalidate {
            directives.push("must-revalidate");
        }
        
        if cache_policy.max_age_seconds > 0 {
            directives.push(&format!("max-age={}", cache_policy.max_age_seconds));
        }
        
        // Add security directives
        if cache_policy.prevent_cache_poisoning {
            // Set strict transport security if using HTTPS
            response.headers.insert(
                "Strict-Transport-Security".to_string(),
                "max-age=31536000; includeSubDomains".to_string()
            );
        }
        
        response.headers.insert(
            "Cache-Control".to_string(),
            directives.join(", ")
        );
    }
    
    // Set appropriate Vary header
    let mut vary_headers = vec!["Accept-Encoding", "Accept-Language"];
    if !cache_policy.allow_public_caching {
        vary_headers.push("Authorization");
        vary_headers.push("Cookie");
    }
    
    response.headers.insert(
        "Vary".to_string(),
        vary_headers.join(", ")
    );
}
```

### 8. Web Cache Deception Prevention

Implement protections against web cache deception attacks:

```rust
/// Config for web cache deception prevention
pub struct WebCacheDeceptionConfig {
    /// Allowed path extensions for caching
    pub allowed_cache_extensions: Vec<String>,
    /// Allowed content types for caching
    pub allowed_cache_content_types: Vec<String>,
    /// Prevent caching of paths containing these segments
    pub never_cache_path_segments: Vec<String>,
}

impl Default for WebCacheDeceptionConfig {
    fn default() -> Self {
        Self {
            allowed_cache_extensions: vec![
                "css".to_string(), "js".to_string(), "jpg".to_string(), "jpeg".to_string(),
                "png".to_string(), "gif".to_string(), "ico".to_string(), "svg".to_string(),
                "woff".to_string(), "woff2".to_string(), "ttf".to_string(), "eot".to_string(),
            ],
            allowed_cache_content_types: vec![
                "text/css".to_string(), "application/javascript".to_string(),
                "image/".to_string(), "font/".to_string(),
                "application/font".to_string(), "text/plain".to_string(),
            ],
            never_cache_path_segments: vec![
                "admin".to_string(), "account".to_string(), "profile".to_string(),
                "dashboard".to_string(), "settings".to_string(), "user".to_string(),
            ],
        }
    }
}

/// Determine if a response should be cached to prevent web cache deception
pub fn is_safe_to_cache(
    request: &Request,
    response: &Response,
    config: &WebCacheDeceptionConfig
) -> bool {
    let path = &request.uri.path;
    
    // Never cache authentication or personal info paths
    for segment in &config.never_cache_path_segments {
        if path.contains(segment) {
            return false;
        }
    }
    
    // Check file extension if present
    if let Some(extension) = path.split('.').last() {
        if !config.allowed_cache_extensions.contains(&extension.to_string()) {
            return false;
        }
    }
    
    // Check content type
    if let Some(content_type) = response.headers.get("Content-Type") {
        if !config.allowed_cache_content_types.iter().any(|allowed| content_type.starts_with(allowed)) {
            return false;
        }
    }
    
    // Additional security checks
    
    // Don't cache if authenticated
    if request.headers.contains_key("Authorization") || request.headers.get("Cookie").is_some() {
        return false;
    }
    
    // Don't cache personalized content
    if response.headers.contains_key("Set-Cookie") {
        return false;
    }
    
    true
}
```

### 9. Cache Timing Attack Protection

Implement protections against timing attacks:

```rust
/// Add random timing variance to prevent cache timing attacks
pub struct TimingProtection {
    /// Whether to add random delays
    pub enabled: bool,
    /// Minimum delay in milliseconds
    pub min_delay_ms: u64,
    /// Maximum delay in milliseconds
    pub max_delay_ms: u64,
}

impl TimingProtection {
    /// Add timing protection delay
    pub async fn delay(&self) {
        if !self.enabled {
            return;
        }
        
        let range = self.max_delay_ms - self.min_delay_ms;
        let delay = if range > 0 {
            self.min_delay_ms + rand::random::<u64>() % range
        } else {
            self.min_delay_ms
        };
        
        tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;
    }
}

/// Access cache with timing protection
pub async fn protected_cache_access<T>(
    cache_fn: impl Future<Output = Result<T>>,
    timing_protection: &TimingProtection
) -> Result<T> {
    // Record start time
    let start = std::time::Instant::now();
    
    // Perform the actual cache operation
    let result = cache_fn.await;
    
    // Calculate actual operation time
    let elapsed = start.elapsed();
    
    // Add random delay for timing attack protection
    if timing_protection.enabled {
        // Determine minimum required delay
        let min_time = std::time::Duration::from_millis(timing_protection.min_delay_ms);
        
        if elapsed < min_time {
            let additional_delay = min_time - elapsed;
            tokio::time::sleep(additional_delay).await;
        }
        
        // Add random component if configured
        if timing_protection.max_delay_ms > timing_protection.min_delay_ms {
            let max_extra = timing_protection.max_delay_ms - timing_protection.min_delay_ms;
            let random_ms = rand::random::<u64>() % max_extra;
            tokio::time::sleep(tokio::time::Duration::from_millis(random_ms)).await;
        }
    }
    
    result
}
```

### 10. Secure Logging for Cache Operations

Implement secure logging for cache operations to aid in security monitoring:

```rust
/// Log levels for cache operations
pub enum CacheLogLevel {
    Debug,
    Info,
    Warning,
    Error,
}

/// Log a cache operation securely
pub fn log_cache_operation(
    level: CacheLogLevel,
    operation: &str,
    key: &str,
    client_ip: Option<&str>,
    success: bool,
    error_message: Option<&str>
) {
    // Sanitize the cache key for logging
    let sanitized_key = if key.len() > 32 {
        // Only log a prefix of the key to avoid logging sensitive data
        format!("{}...", &key[0..32])
    } else {
        key.to_string()
    };
    
    let ip_info = if let Some(ip) = client_ip {
        format!("from IP {}", ip)
    } else {
        "from unknown IP".to_string()
    };
    
    let status = if success { "succeeded" } else { "failed" };
    
    let message = format!(
        "Cache operation '{}' for key '{}' {} {} {}",
        operation, 
        sanitized_key,
        ip_info,
        status,
        error_message.unwrap_or("")
    );
    
    match level {
        CacheLogLevel::Debug => log::debug!("{}", message),
        CacheLogLevel::Info => log::info!("{}", message),
        CacheLogLevel::Warning => log::warn!("{}", message),
        CacheLogLevel::Error => log::error!("{}", message),
    }
}

/// Log regular cache statistics for security monitoring
pub fn log_cache_statistics(cache: &dyn CacheBackend) -> Result<()> {
    let stats = cache.get_statistics()?;
    
    log::info!(
        "Cache statistics: {} entries, {} hits, {} misses, {} evictions, memory usage: {}KB",
        stats.entry_count,
        stats.hit_count,
        stats.miss_count,
        stats.eviction_count,
        stats.memory_usage_kb
    );
    
    // Alert on suspicious patterns
    if stats.eviction_count > 10000 && stats.hit_ratio < 0.2 {
        log::warn!("Suspicious cache behavior detected: high eviction rate with low hit ratio. Possible DoS attack.");
    }
    
    Ok(())
}
```

## Tradeoffs

### Web Server Tradeoffs

- **In-Memory vs Disk Cache:** In-memory is faster but limited by RAM, while disk caching is slower but has more capacity.
- **Global vs Per-Path Caching:** Global caching is simpler but less flexible than per-path configurations.
- **Cache Headers:** Respecting cache control headers adds complexity but follows standards better.

### Rust Tradeoffs

- **DashMap vs Mutex<HashMap>:** DashMap provides better concurrency but may use more memory.
- **Custom vs Existing Cache Implementations:** Building our own cache gives control, but using a library like `moka` would be quicker to implement.
- **Serialization:** Using `serde` adds a dependency but makes disk caching easier.

## Performance Considerations

- **Memory Usage:** Monitor memory usage and adjust cache size limits accordingly.
- **Eviction Policy:** LRU works well for most cases, but consider alternatives like FIFO or LFU for specific workloads.
- **Concurrency:** Ensure thread-safe access to avoid contention.

## Next Steps

In the next tutorial, we'll implement security features to protect our web server against common attacks and ensure proper authentication and authorization mechanisms.

## Navigation
- [Previous: Reverse Proxy](10-reverse-proxy.md)
- [Next: Security Features](12-security-features.md)
