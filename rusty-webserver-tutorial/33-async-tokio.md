# Asynchronous Programming and Tokio

## Learning Objectives
- Understand the core concepts of asynchronous programming in Rust
- Master the Tokio runtime and its components for building high-performance webservers
- Implement efficient concurrent request handling using async/await patterns
- Design and optimize resource management in async contexts
- Build scalable web applications using modern async libraries and patterns

## Prerequisites
- Completion of previous modules in the Rusty Webserver Tutorial
- Understanding of Rust ownership, borrowing, and lifetimes
- Familiarity with basic networking concepts
- Working knowledge of threads and concurrency

## Navigation
- [Previous: Advanced Webserver Development](32-advanced-webserver-development.md) 
- [Next: Error Handling and Observability](34-error-handling-observability.md)

## Introduction
Asynchronous programming is a cornerstone of high-performance web applications, allowing servers to handle thousands of concurrent connections with minimal resource usage. In this module, we'll explore Rust's async/await syntax, the Tokio runtime, and techniques for building efficient web servers that can scale to meet demanding workloads.

## Understanding the Asynchronous Programming Model

### The Problem with Blocking I/O
Traditional synchronous code blocks the entire thread when waiting for I/O operations:

```rust
fn handle_connection(stream: TcpStream) {
    // This blocks the thread until reading is complete
    let mut buffer = [0; 1024];
    stream.read(&mut buffer).unwrap();
    
    // Thread is blocked again during write
    stream.write(response.as_bytes()).unwrap();
}
```

This approach requires one thread per connection, which limits scalability due to:
- Memory overhead (each thread requires stack allocation)
- Context switching costs
- Operating system thread limits

### The Async Solution
Asynchronous programming allows a single thread to manage many connections by:
1. Initiating I/O operations without blocking
2. Switching to other tasks while waiting
3. Resuming when the I/O operation completes

## Rust's Async/Await Model

Async/await in Rust provides a familiar synchronous-looking syntax while enabling non-blocking execution:

```rust
// An async function returns a Future
async fn say_hello() -> String {
    // This could be an operation that takes time
    tokio::time::sleep(Duration::from_millis(100)).await;
    "Hello, world!".to_string()
}

// To execute the Future, you need to .await it
async fn greet() {
    let message = say_hello().await;
    println!("{}", message);
}
```

### How Futures Work
- A `Future` represents a value that will be available at some point
- Futures are lazy - they do nothing until polled
- The runtime (like Tokio) handles polling futures to completion
- `await` yields control back to the runtime when a future isn't ready

## The Tokio Runtime

Tokio is the most widely-used async runtime in the Rust ecosystem, providing:

### Core Components

1. **Multi-threaded Scheduler**:
   - Distributes tasks across a thread pool
   - Automatically scales to available CPU cores

2. **I/O Driver**:
   - Efficient event notification system (epoll/kqueue/IOCP)
   - Non-blocking network operations

3. **Timer System**:
   - Efficient timing wheels for deadline and interval operations
   - Enables timeouts and periodic tasks

4. **Synchronization Primitives**:
   - Async-aware mutex, RwLock, and Semaphore
   - Channels for task communication

### Setting Up Tokio

```rust
use tokio::runtime::Runtime;

// Method 1: Using #[tokio::main]
#[tokio::main]
async fn main() {
    println!("Running in async context");
    tokio::time::sleep(std::time::Duration::from_secs(1)).await;
}

// Method 2: Manual runtime creation
fn main() {
    // Create a multi-threaded runtime
    let rt = Runtime::new().unwrap();
    
    // Execute an async block on the runtime
    rt.block_on(async {
        println!("Running in async context");
        tokio::time::sleep(std::time::Duration::from_secs(1)).await;
    });
}

// Method 3: Runtime with custom configuration
fn main() {
    let rt = Runtime::builder()
        .worker_threads(4)           // Set number of worker threads
        .enable_io()                 // Enable I/O driver
        .enable_time()               // Enable time driver
        .build()                     // Build the runtime
        .unwrap();
        
    rt.block_on(async { /* ... */ });
}
```

## Building an Async HTTP Server with Tokio

Let's implement a complete asynchronous HTTP server using Tokio:

```rust
use std::error::Error;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::{TcpListener, TcpStream};
use std::sync::{Arc, atomic::{AtomicUsize, Ordering}};
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // Track active connections
    let connections = Arc::new(AtomicUsize::new(0));
    
    // Bind to the address
    let listener = TcpListener::bind("127.0.0.1:8080").await?;
    println!("Server running on http://127.0.0.1:8080");

    // Spawn a task to log stats periodically
    let stats_connections = connections.clone();
    tokio::spawn(async move {
        loop {
            tokio::time::sleep(Duration::from_secs(5)).await;
            println!("Active connections: {}", stats_connections.load(Ordering::Relaxed));
        }
    });

    // Accept incoming connections
    loop {
        let (socket, addr) = listener.accept().await?;
        println!("Connection from: {}", addr);
        
        // Update connection counter
        let conn_counter = connections.clone();
        conn_counter.fetch_add(1, Ordering::SeqCst);

        // Spawn a new task for each connection
        tokio::spawn(async move {
            let result = handle_connection(socket).await;
            
            // Update counter when connection is done
            conn_counter.fetch_sub(1, Ordering::SeqCst);
            
            if let Err(e) = result {
                eprintln!("Error handling connection: {}", e);
            }
        });
    }
}

async fn handle_connection(mut socket: TcpStream) -> Result<(), Box<dyn Error>> {
    // Read the request
    let mut buffer = [0; 8192];
    let n = socket.read(&mut buffer).await?;
    
    // Simple HTTP parsing
    let request = String::from_utf8_lossy(&buffer[..n]);
    let request_line = request.lines().next().unwrap_or("");
    
    // Simulate async database lookup or processing
    let response = if request_line.contains("GET / HTTP") {
        // Simulate some async work
        tokio::time::sleep(Duration::from_millis(50)).await;
        format!("HTTP/1.1 200 OK\r\nContent-Type: text/html\r\nContent-Length: 98\r\n\r\n<html><body><h1>Hello from Async Rust!</h1><p>Your request: {}</p></body></html>", request_line)
    } else if request_line.contains("GET /slow HTTP") {
        // Simulate a slow response
        tokio::time::sleep(Duration::from_millis(500)).await;
        "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: 25\r\n\r\nThis was a slow response!".into()
    } else {
        "HTTP/1.1 404 Not Found\r\nContent-Type: text/plain\r\nContent-Length: 9\r\n\r\nNot Found".into()
    };
    
    // Write response with timeout
    let write_fut = socket.write_all(response.as_bytes());
    
    // Add a timeout for the write operation
    match tokio::time::timeout(Duration::from_secs(5), write_fut).await {
        Ok(result) => result?,
        Err(_) => return Err("Write operation timed out".into()),
    }
    
    Ok(())
}
```

## Advanced Tokio Features

### Task Management

```rust
// Spawning tasks
let handle = tokio::spawn(async {
    // Some async work
    42
});

// Awaiting the result
let result = handle.await.expect("Task panicked");
assert_eq!(result, 42);

// Cancellation
handle.abort();
```

### Concurrency Patterns

**Running Tasks Concurrently**:
```rust
use futures::future;

async fn process_multiple_requests() {
    // Execute futures concurrently and collect results
    let results = future::join_all(vec![
        fetch_data(1),
        fetch_data(2),
        fetch_data(3),
    ]).await;
    
    println!("All results: {:?}", results);
}
```

**Select Pattern** for racing futures:
```rust
use tokio::select;

async fn timeout_operation() {
    select! {
        _ = tokio::time::sleep(Duration::from_secs(5)) => {
            println!("Operation timed out");
        },
        result = perform_operation() => {
            println!("Operation completed with: {:?}", result);
        }
    }
}
```

### Channels for Task Communication

```rust
use tokio::sync::mpsc;

async fn channel_example() {
    // Create a bounded channel
    let (tx, mut rx) = mpsc::channel(100);
    
    // Spawn task that sends messages
    let tx_clone = tx.clone();
    tokio::spawn(async move {
        for i in 0..10 {
            tx_clone.send(format!("Message {}", i)).await.unwrap();
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
    });
    
    // Another sender
    tokio::spawn(async move {
        for i in 10..20 {
            tx.send(format!("Message {}", i)).await.unwrap();
            tokio::time::sleep(Duration::from_millis(150)).await;
        }
    });
    
    // Receive messages
    while let Some(message) = rx.recv().await {
        println!("Got: {}", message);
    }
}
```

## Resource Management in Async Code

### CPU-bound Work

Avoid blocking the async runtime with CPU-intensive operations:

```rust
async fn process_data(data: Vec<u8>) -> Result<Vec<u8>, Box<dyn Error>> {
    // Offload CPU-intensive work to a dedicated thread pool
    tokio::task::spawn_blocking(move || {
        // This runs on a separate thread
        // Perform CPU-heavy computation here
        let mut result = data;
        result.sort();
        result
    }).await?
}
```

### Connection Pooling

Using connection pooling for database access:

```rust
use bb8_postgres::{bb8, PostgresConnectionManager};
use tokio_postgres::NoTls;

async fn database_example() -> Result<(), Box<dyn std::error::Error>> {
    // Set up connection manager
    let manager = PostgresConnectionManager::new(
        "host=localhost user=postgres".parse()?,
        NoTls,
    );
    
    // Create a connection pool
    let pool = bb8::Pool::builder()
        .max_size(20)
        .build(manager)
        .await?;
    
    // Use a connection from the pool
    let conn = pool.get().await?;
    let rows = conn.query("SELECT 1", &[]).await?;
    
    println!("Query returned {} rows", rows.len());
    Ok(())
}
```

### Backpressure and Load Shedding

Managing request flow when under heavy load:

```rust
use tokio::sync::{Semaphore, OwnedSemaphorePermit};

struct RequestLimiter {
    semaphore: Arc<Semaphore>,
}

impl RequestLimiter {
    fn new(max_concurrent: usize) -> Self {
        Self {
            semaphore: Arc::new(Semaphore::new(max_concurrent)),
        }
    }
    
    async fn acquire(&self) -> Option<OwnedSemaphorePermit> {
        // Try to acquire permission, return None if cannot (implements load shedding)
        self.semaphore.clone().acquire_owned().await.ok()
    }
}

// Usage in server
async fn handle_request(limiter: &RequestLimiter, req: Request) -> Response {
    // Try to acquire a permit
    let _permit = match limiter.acquire().await {
        Some(permit) => permit,
        None => return Response::new().status(503).body("Server too busy"),
    };
    
    // Process the request (permit is automatically released when dropped)
    process_request(req).await
}
```

## Integration with HTTP Frameworks

### Hyper

```rust
use std::convert::Infallible;
use hyper::{Body, Request, Response, Server};
use hyper::service::{make_service_fn, service_fn};

async fn handle(req: Request<Body>) -> Result<Response<Body>, Infallible> {
    Ok(Response::new(Body::from("Hello from Hyper!")))
}

#[tokio::main]
async fn main() {
    // Create the service
    let make_svc = make_service_fn(|_conn| async {
        Ok::<_, Infallible>(service_fn(handle))
    });

    // Bind and serve
    let addr = ([127, 0, 0, 1], 3000).into();
    let server = Server::bind(&addr).serve(make_svc);
    
    if let Err(e) = server.await {
        eprintln!("server error: {}", e);
    }
}
```

### Axum

```rust
use axum::{
    routing::{get, post},
    http::StatusCode,
    response::IntoResponse,
    Json, Router,
};
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;

#[derive(Serialize, Deserialize)]
struct User {
    name: String,
    email: String,
}

async fn create_user(Json(user): Json<User>) -> impl IntoResponse {
    // Create user in database
    (StatusCode::CREATED, Json(user))
}

async fn get_users() -> impl IntoResponse {
    Json(vec![
        User { name: "Alice".into(), email: "<EMAIL>".into() },
        User { name: "Bob".into(), email: "<EMAIL>".into() },
    ])
}

#[tokio::main]
async fn main() {
    // Build our application with routes
    let app = Router::new()
        .route("/users", get(get_users))
        .route("/users", post(create_user));

    // Run it
    let addr = SocketAddr::from(([127, 0, 0, 1], 3000));
    println!("listening on {}", addr);
    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await
        .unwrap();
}
```

## Best Practices for Async Web Servers

1. **Use Async Throughout the Stack**:
   - Ensure all I/O operations (network, filesystem, database) are async
   - Use compatible async libraries and drivers

2. **Properly Manage Blocking Operations**:
   - Use `spawn_blocking` for CPU-intensive or synchronous code
   - Isolate blocking operations to separate thread pools

3. **Set Timeouts Everywhere**:
   - Apply timeouts to all external service calls
   - Implement client-side timeouts
   - Add per-route timeouts for different endpoints

4. **Handle Backpressure**:
   - Implement rate limiting
   - Use bounded queues and channels
   - Apply load shedding during peak times

5. **Optimize Task Creation**:
   - Avoid spawning tasks for trivial operations
   - Consider task locality and data sharing
   - Use structured concurrency patterns

## Performance Considerations

- **Runtime Configuration**: Tune worker threads based on workload
- **I/O Buffering**: Use appropriate buffer sizes for your workload
- **Task Granularity**: Balance between too many small tasks and too few large ones
- **Memory Usage**: Monitor and optimize memory allocation patterns
- **Contention Points**: Identify and minimize mutex and channel contention

## Debugging Async Code

Debugging asynchronous code can be challenging. Here are some techniques:

1. **Tracing**: Use the `tracing` crate for structured, async-aware logging
   ```rust
   use tracing::{info, instrument};
   
   #[instrument]
   async fn process_request(id: u32) {
       info!(request_id = id, "Processing request");
       // ...
   }
   ```

2. **Task Local Storage**: Use task-local values for request context
   ```rust
   tokio::task_local! {
       static REQUEST_ID: u32;
   }
   
   async fn with_request_id<F, R>(id: u32, f: F) -> R
   where
       F: Future<Output = R>,
   {
       REQUEST_ID::scope(id, f).await
   }
   ```

3. **Cancellation Handling**: Properly handle task cancellation
   ```rust
   async fn handle_with_cleanup() {
       // Use drop guard pattern
       struct Cleanup;
       impl Drop for Cleanup {
           fn drop(&mut self) {
               println!("Cleaning up resources");
           }
       }
       
       let _cleanup = Cleanup;
       
       // If this task is cancelled, Cleanup's drop will run
       do_work().await;
   }
   ```

## Knowledge Check

1. What is the primary benefit of asynchronous programming for web servers?
   - a) Improved CPU utilization
   - b) Higher memory efficiency with many concurrent connections
   - c) Simpler programming model
   - d) Automatic parallelization of all code

2. In Tokio, what should you do with CPU-intensive operations?
   - a) Use `async`/`await` directly
   - b) Use `tokio::spawn_blocking` to offload to a separate thread pool
   - c) Create a dedicated Tokio runtime for them
   - d) Use the `block_in_place` method

3. Which of these is NOT a component of the Tokio runtime?
   - a) Task scheduler
   - b) I/O driver
   - c) Timer system
   - d) Memory allocator

4. What is backpressure in the context of async web servers?
   - a) Physical pressure on server hardware from high traffic
   - b) A mechanism to slow down request acceptance when the system is overloaded
   - c) Network packet congestion
   - d) CPU throttling during high load

5. Which pattern allows handling multiple futures concurrently and taking action when the first one completes?
   - a) `tokio::join!`
   - b) `tokio::select!`
   - c) `future::join_all`
   - d) `tokio::spawn`

## Diagram

```
┌─────────────────────────────────────────┐
│             Client Requests              │
└───────────────┬─────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────┐
│           Tokio Runtime                  │
│  ┌─────────┐  ┌──────┐  ┌────────────┐  │
│  │ I/O     │  │ Task │  │ Resource   │  │
│  │ Driver  │◄─┤ Pool │◄─┤ Management │  │
│  └─────────┘  └──────┘  └────────────┘  │
│        ▲          │           ▲         │
└────────┼──────────┼───────────┼─────────┘
         │          │           │
         │          ▼           │
┌────────┼──────────────────────┼─────────┐
│ ┌──────┴──────┐  ┌────────────┴───────┐ │
│ │ Network I/O │  │  Async Resources   │ │
│ │ Operations  │  │  - DB Connections  │ │
│ │             │  │  - Files           │ │
│ │             │  │  - Caches          │ │
│ └─────────────┘  └──────────────────┬─┘ │
│         Your Application            │   │
└───────────────────────────────────┬─┘   │
                                    │     │
┌───────────────────────────────────┼─────┘
│         External Services         │
│  ┌─────────┐ ┌────────┐ ┌────────┤
│  │ Database│ │ Cache  │ │ Other  │
│  └─────────┘ └────────┘ └────────┘
└─────────────────────────────────────────┘
```

## Additional Resources

- [Tokio Documentation](https://tokio.rs/tokio/tutorial) - Official Tokio tutorial and documentation
- [Asynchronous Programming in Rust Book](https://rust-lang.github.io/async-book/) - Comprehensive guide to async Rust
- [Tokio GitHub Repository](https://github.com/tokio-rs/tokio) - Source code and examples
- [Hyper Documentation](https://hyper.rs/guides/) - Async HTTP library built on Tokio
- [Axum Framework](https://github.com/tokio-rs/axum) - Ergonomic web framework built on Tokio
- [Tokio Best Practices](https://github.com/LukeMathWalker/zero-to-production/) - Examples of production-grade async Rust code
- [100 Days of Rust: Asynchronous Programming](https://www.youtube.com/playlist?list=PLai5B987bZ9CoVR-QEIN9foz4QCJ0H2Y8) - Video series on async Rust
