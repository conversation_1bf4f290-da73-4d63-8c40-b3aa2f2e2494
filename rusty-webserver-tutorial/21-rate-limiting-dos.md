# Rate Limiting and DoS Protection

Protect your webserver from abuse and denial-of-service attacks by implementing robust rate limiting and traffic control mechanisms. This module covers techniques to identify and mitigate excessive traffic and malicious actors.

## Learning Objectives
- Understand different rate limiting strategies and algorithms
- Implement rate limiting at multiple levels of your application
- Detect and mitigate DoS and DDoS attacks
- Configure graceful degradation under heavy load
- Monitor traffic patterns to identify abuse

## Prerequisites
- Basic understanding of HTTP
- Familiarity with middleware concepts
- Understanding of concurrent programming in Rust

## Rate Limiting Strategies

### 1. Fixed Window Counter

The simplest rate limiting approach counts requests within a fixed time window:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Mutex;
use std::time::{Instant, Duration};

struct FixedWindowRateLimiter {
    // Maps IP address to (window start time, request count)
    counters: Mutex<HashMap<IpAddr, (Instant, u32)>>,
    window_size: Duration,
    max_requests: u32,
}

impl FixedWindowRateLimiter {
    fn new(window_seconds: u64, max_requests: u32) -> Self {
        FixedWindowRateLimiter {
            counters: Mutex::new(HashMap::new()),
            window_size: Duration::from_secs(window_seconds),
            max_requests,
        }
    }
    
    fn is_rate_limited(&self, ip: IpAddr) -> bool {
        let mut counters = self.counters.lock().unwrap();
        let now = Instant::now();
        
        if let Some((window_start, count)) = counters.get_mut(&ip) {
            // Check if window has expired
            if now.duration_since(*window_start) > self.window_size {
                // Start a new window
                *window_start = now;
                *count = 1;
                false
            } else if *count >= self.max_requests {
                // Rate limit exceeded
                true
            } else {
                // Increment count in current window
                *count += 1;
                false
            }
        } else {
            // First request from this IP
            counters.insert(ip, (now, 1));
            false
        }
    }
}

// Usage in a middleware
async fn rate_limit_middleware(
    req: ServiceRequest,
    rate_limiter: web::Data<FixedWindowRateLimiter>,
) -> Result<ServiceRequest, Error> {
    let ip = req.peer_addr().map(|addr| addr.ip())
        .unwrap_or_else(|| "0.0.0.0".parse().unwrap());
    
    if rate_limiter.is_rate_limited(ip) {
        Err(ErrorTooManyRequests("Rate limit exceeded"))
    } else {
        Ok(req)
    }
}
```

### 2. Token Bucket Algorithm

More sophisticated rate limiting with support for bursts:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};

struct TokenBucket {
    capacity: u32,
    tokens: f64,
    refill_rate: f64, // tokens per second
    last_refill: Instant,
}

impl TokenBucket {
    fn new(capacity: u32, refill_rate: f64) -> Self {
        TokenBucket {
            capacity,
            tokens: capacity as f64,
            refill_rate,
            last_refill: Instant::now(),
        }
    }
    
    fn refill(&mut self) {
        let now = Instant::now();
        let elapsed = now.duration_since(self.last_refill).as_secs_f64();
        self.last_refill = now;
        
        // Add tokens based on elapsed time
        self.tokens += elapsed * self.refill_rate;
        
        // Cap at capacity
        if self.tokens > self.capacity as f64 {
            self.tokens = self.capacity as f64;
        }
    }
    
    fn try_consume(&mut self, tokens: u32) -> bool {
        self.refill();
        
        if self.tokens >= tokens as f64 {
            self.tokens -= tokens as f64;
            true
        } else {
            false
        }
    }
}

struct TokenBucketRateLimiter {
    buckets: RwLock<HashMap<IpAddr, TokenBucket>>,
    capacity: u32,
    refill_rate: f64,
}

impl TokenBucketRateLimiter {
    fn new(capacity: u32, refill_rate: f64) -> Self {
        TokenBucketRateLimiter {
            buckets: RwLock::new(HashMap::new()),
            capacity,
            refill_rate,
        }
    }
    
    fn is_rate_limited(&self, ip: IpAddr, cost: u32) -> bool {
        let mut buckets = self.buckets.write().unwrap();
        
        // Get or create bucket for IP
        let bucket = buckets.entry(ip).or_insert_with(|| {
            TokenBucket::new(self.capacity, self.refill_rate)
        });
        
        // Try to consume tokens
        !bucket.try_consume(cost)
    }
}

// Usage with different costs per endpoint
async fn api_rate_limit_middleware(
    req: ServiceRequest,
    limiter: web::Data<TokenBucketRateLimiter>,
) -> Result<ServiceRequest, Error> {
    let ip = req.peer_addr().map(|addr| addr.ip())
        .unwrap_or_else(|| "0.0.0.0".parse().unwrap());
    
    // Assign different token costs based on endpoint
    let cost = match req.path() {
        "/api/expensive" => 5,  // Expensive operation costs more tokens
        "/api/moderate" => 3,   // Moderate operation
        _ => 1,                 // Default cost
    };
    
    if limiter.is_rate_limited(ip, cost) {
        let retry_after = (cost as f64 / limiter.refill_rate).ceil() as u64;
        
        Err(HttpResponse::TooManyRequests()
            .append_header(("Retry-After", retry_after.to_string()))
            .finish().into())
    } else {
        Ok(req)
    }
}
```

### 3. Sliding Window Log

More accurate rate limiting that avoids boundary issues:

```rust
use std::collections::{HashMap, VecDeque};
use std::net::IpAddr;
use std::sync::Mutex;
use std::time::{Instant, Duration};

struct SlidingWindowLog {
    requests: VecDeque<Instant>,
    window_size: Duration,
    max_requests: usize,
}

impl SlidingWindowLog {
    fn new(window_seconds: u64, max_requests: usize) -> Self {
        SlidingWindowLog {
            requests: VecDeque::with_capacity(max_requests),
            window_size: Duration::from_secs(window_seconds),
            max_requests,
        }
    }
    
    fn is_rate_limited(&mut self) -> bool {
        let now = Instant::now();
        
        // Remove expired timestamps
        while let Some(timestamp) = self.requests.front() {
            if now.duration_since(*timestamp) > self.window_size {
                self.requests.pop_front();
            } else {
                break;
            }
        }
        
        // Check if limit is reached
        if self.requests.len() >= self.max_requests {
            true
        } else {
            // Record new request
            self.requests.push_back(now);
            false
        }
    }
}

struct SlidingWindowRateLimiter {
    logs: Mutex<HashMap<IpAddr, SlidingWindowLog>>,
    window_size: Duration,
    max_requests: usize,
}

impl SlidingWindowRateLimiter {
    fn new(window_seconds: u64, max_requests: usize) -> Self {
        SlidingWindowRateLimiter {
            logs: Mutex::new(HashMap::new()),
            window_size: Duration::from_secs(window_seconds),
            max_requests,
        }
    }
    
    fn is_rate_limited(&self, ip: IpAddr) -> bool {
        let mut logs = self.logs.lock().unwrap();
        
        let log = logs.entry(ip).or_insert_with(|| {
            SlidingWindowLog::new(
                self.window_size.as_secs(),
                self.max_requests,
            )
        });
        
        log.is_rate_limited()
    }
}
```

## Distributed Rate Limiting

For multiple server instances, use Redis to coordinate rate limits:

```rust
use redis::{Client, Commands, RedisResult};
use std::net::IpAddr;
use std::time::Duration;

struct RedisRateLimiter {
    redis: Client,
    window_seconds: u64,
    max_requests: usize,
}

impl RedisRateLimiter {
    fn new(redis_url: &str, window_seconds: u64, max_requests: usize) -> RedisResult<Self> {
        let redis = Client::open(redis_url)?;
        
        Ok(RedisRateLimiter {
            redis,
            window_seconds,
            max_requests,
        })
    }
    
    async fn is_rate_limited(&self, ip: IpAddr) -> RedisResult<bool> {
        let mut conn = self.redis.get_async_connection().await?;
        let key = format!("ratelimit:{}", ip);
        
        // Pipeline for atomic operations
        let pipeline = redis::pipe()
            // Add current timestamp to sorted set
            .zadd(&key, Instant::now().elapsed().as_millis() as u64, uuid::Uuid::new_v4().to_string())
            // Remove timestamps outside the window
            .zremrangebyscore(&key, 0, (Instant::now() - Duration::from_secs(self.window_seconds)).elapsed().as_millis() as u64)
            // Count remaining entries
            .zcard(&key)
            // Set expiry on the key
            .expire(&key, self.window_seconds)
            // Execute all commands
            .query_async(&mut conn)
            .await?;
        
        // Get the count from pipeline result
        let count: usize = pipeline[2];
        
        Ok(count > self.max_requests)
    }
}

// Usage in an async middleware
async fn redis_rate_limit_middleware(
    req: ServiceRequest,
    limiter: web::Data<RedisRateLimiter>,
) -> Result<ServiceRequest, Error> {
    let ip = req.peer_addr().map(|addr| addr.ip())
        .unwrap_or_else(|| "0.0.0.0".parse().unwrap());
    
    match limiter.is_rate_limited(ip).await {
        Ok(limited) if limited => {
            Err(ErrorTooManyRequests("Rate limit exceeded"))
        }
        Ok(_) => {
            Ok(req)
        }
        Err(e) => {
            // Redis error, log and allow (fail open)
            log::error!("Redis rate limiting error: {}", e);
            Ok(req)
        }
    }
}
```

## DoS Protection Techniques

### 1. Connection Limiting

Limit the number of concurrent connections per client:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use tokio::sync::RwLock;

struct ConnectionLimiter {
    connections: RwLock<HashMap<IpAddr, AtomicUsize>>,
    max_connections: usize,
}

impl ConnectionLimiter {
    fn new(max_connections: usize) -> Self {
        ConnectionLimiter {
            connections: RwLock::new(HashMap::new()),
            max_connections,
        }
    }
    
    async fn acquire(&self, ip: IpAddr) -> bool {
        // Get or create counter for IP
        let mut connections = self.connections.write().await;
        let counter = connections.entry(ip).or_insert_with(|| {
            Arc::new(AtomicUsize::new(0))
        });
        
        // Increment and check
        let current = counter.fetch_add(1, Ordering::SeqCst);
        
        if current >= self.max_connections {
            // Limit reached, decrement and return false
            counter.fetch_sub(1, Ordering::SeqCst);
            false
        } else {
            true
        }
    }
    
    async fn release(&self, ip: IpAddr) {
        let connections = self.connections.read().await;
        
        if let Some(counter) = connections.get(&ip) {
            counter.fetch_sub(1, Ordering::SeqCst);
        }
    }
}

// Usage in a connection handler
async fn handle_connection(
    stream: TcpStream,
    limiter: Arc<ConnectionLimiter>,
) {
    let ip = match stream.peer_addr() {
        Ok(addr) => addr.ip(),
        Err(_) => return, // Can't get peer address
    };
    
    // Try to acquire connection slot
    if !limiter.acquire(ip).await {
        // Connection limit reached, close the connection
        return;
    }
    
    // Use connection wrapper to ensure release on drop
    let conn = Connection {
        ip,
        limiter: limiter.clone(),
    };
    
    // Handle the connection
    process_connection(stream).await;
    
    // Connection automatically released when conn drops
}

struct Connection {
    ip: IpAddr,
    limiter: Arc<ConnectionLimiter>,
}

impl Drop for Connection {
    fn drop(&mut self) {
        let limiter = self.limiter.clone();
        let ip = self.ip;
        
        tokio::spawn(async move {
            limiter.release(ip).await;
        });
    }
}
```

### 2. Traffic Shaping with Leaky Bucket

Control traffic flow to prevent overwhelming the server:

```rust
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::time::sleep;

struct LeakyBucket {
    // Semaphore controls concurrent requests
    semaphore: Semaphore,
    // Tracks last request time
    last_leak: AtomicU64,
    // Leak rate in milliseconds per token
    leak_interval_ms: u64, 
}

impl LeakyBucket {
    fn new(capacity: usize, requests_per_second: u64) -> Self {
        // Calculate leak interval (ms between requests)
        let leak_interval_ms = 1000 / requests_per_second;
        
        LeakyBucket {
            semaphore: Semaphore::new(capacity),
            last_leak: AtomicU64::new(Instant::now().elapsed().as_millis() as u64),
            leak_interval_ms,
        }
    }
    
    async fn acquire(&self) -> LeakyBucketPermit<'_> {
        // Get permit from semaphore
        let permit = self.semaphore.acquire().await.unwrap();
        
        // Calculate time since last leak
        let now = Instant::now().elapsed().as_millis() as u64;
        let last = self.last_leak.load(Ordering::Relaxed);
        let elapsed = now.saturating_sub(last);
        
        // If not enough time has passed, delay
        if elapsed < self.leak_interval_ms {
            let delay = self.leak_interval_ms - elapsed;
            sleep(Duration::from_millis(delay)).await;
        }
        
        // Update last leak time
        self.last_leak.store(Instant::now().elapsed().as_millis() as u64, Ordering::Relaxed);
        
        // Return permit wrapped in our struct
        LeakyBucketPermit {
            _permit: permit,
        }
    }
}

struct LeakyBucketPermit<'a> {
    _permit: tokio::sync::SemaphorePermit<'a>,
}

// Usage in a request handler
async fn handle_request(
    req: ServiceRequest,
    bucket: web::Data<LeakyBucket>,
) -> Result<ServiceResponse, Error> {
    // Acquire permit from leaky bucket
    let _permit = bucket.acquire().await;
    
    // Process request with controlled flow
    let response = handle_inner(req).await?;
    
    Ok(response)
}
```

### 3. Request Body Size Limiting

Prevent memory exhaustion from large request bodies:

```rust
use actix_web::{Error, HttpRequest, HttpResponse};
use futures::{StreamExt, TryStreamExt};
use bytes::{BytesMut, Buf};

// PayloadConfig defines limits
#[derive(Clone)]
struct PayloadConfig {
    max_size: usize,
    error_handler: fn() -> HttpResponse,
}

// Middleware that limits payload size
async fn limit_payload_size(
    mut payload: web::Payload,
    req: HttpRequest,
    config: web::Data<PayloadConfig>,
) -> Result<Vec<u8>, Error> {
    // Initialize buffer
    let mut body = BytesMut::new();
    let max_size = config.max_size;
    
    // Process payload chunks
    while let Some(chunk) = payload.next().await {
        let chunk = chunk?;
        
        // Check size limit
        if (body.len() + chunk.len()) > max_size {
            return Err(
                ErrorPayloadTooLarge("Request payload too large").into()
            );
        }
        
        // Extend the buffer
        body.extend_from_slice(&chunk);
    }
    
    Ok(body.to_vec())
}

// Usage in application configuration
fn configure_app(config: &mut web::ServiceConfig) {
    let payload_config = web::Data::new(PayloadConfig {
        max_size: 1_048_576, // 1MB
        error_handler: || {
            HttpResponse::PayloadTooLarge()
                .body("Request body too large")
        },
    });
    
    config.app_data(payload_config);
}
```

## Detecting and Responding to DoS Attacks

### 1. Adaptive Rate Limiting

Adjust rate limits based on server load:

```rust
use std::sync::atomic::{AtomicUsize, Ordering};
use tokio::sync::RwLock;

struct AdaptiveRateLimiter {
    // Base rate limit parameters
    base_rate: u32,
    // Current system load (0-100)
    system_load: AtomicUsize,
    // Actual rate limiter
    rate_limiter: RwLock<TokenBucketRateLimiter>,
}

impl AdaptiveRateLimiter {
    fn new(base_rate: u32) -> Self {
        AdaptiveRateLimiter {
            base_rate,
            system_load: AtomicUsize::new(0),
            rate_limiter: RwLock::new(TokenBucketRateLimiter::new(
                base_rate,
                base_rate as f64 / 60.0, // Refill rate: capacity per minute
            )),
        }
    }
    
    // Update system load periodically
    async fn update_load(&self, load: usize) {
        let old_load = self.system_load.swap(load, Ordering::Relaxed);
        
        // If load changed significantly, adjust rate limits
        if (old_load as isize - load as isize).abs() > 10 {
            self.adjust_rate_limits().await;
        }
    }
    
    async fn adjust_rate_limits(&self) {
        let load = self.system_load.load(Ordering::Relaxed);
        
        // Calculate new capacity based on load
        // As load increases, capacity decreases
        let new_capacity = if load < 50 {
            // Normal load: use base rate
            self.base_rate
        } else if load < 80 {
            // Moderate load: reduce by 25-50%
            let reduction_factor = 0.75 - (load as f64 - 50.0) * 0.01;
            (self.base_rate as f64 * reduction_factor) as u32
        } else {
            // High load: reduce by 50-90%
            let reduction_factor = 0.5 - (load as f64 - 80.0) * 0.02;
            (self.base_rate as f64 * reduction_factor.max(0.1)) as u32
        };
        
        // Update rate limiter
        let mut limiter = self.rate_limiter.write().await;
        *limiter = TokenBucketRateLimiter::new(
            new_capacity,
            new_capacity as f64 / 60.0,
        );
        
        log::info!(
            "Adjusted rate limits: capacity={}, load={}%",
            new_capacity,
            load
        );
    }
    
    async fn is_rate_limited(&self, ip: IpAddr) -> bool {
        let limiter = self.rate_limiter.read().await;
        limiter.is_rate_limited(ip, 1)
    }
}

// Background task to monitor system load
async fn monitor_system_load(limiter: Arc<AdaptiveRateLimiter>) {
    let mut interval = tokio::time::interval(Duration::from_secs(30));
    
    loop {
        interval.tick().await;
        
        // Get system load (example using sysinfo)
        let load = get_system_load().await;
        limiter.update_load(load).await;
    }
}

async fn get_system_load() -> usize {
    // Example implementation using sysinfo
    let mut system = sysinfo::System::new_all();
    system.refresh_all();
    
    // Calculate CPU utilization percentage
    (system.global_cpu_info().cpu_usage() as usize).min(100)
}
```

### 2. Traffic Analysis and Anomaly Detection

Identify and block suspicious traffic patterns:

```rust
use std::collections::{HashMap, VecDeque};
use std::net::IpAddr;
use std::sync::Mutex;
use std::time::{Duration, Instant};

// Track request patterns for anomaly detection
struct RequestPattern {
    // Request counts by path
    path_counts: HashMap<String, usize>,
    // Total requests
    total_requests: usize,
    // Last request time
    last_request: Instant,
    // Suspicious score (higher is more suspicious)
    suspicion_score: f64,
}

struct AnomalyDetector {
    patterns: Mutex<HashMap<IpAddr, RequestPattern>>,
    // Time window for analysis
    window: Duration,
    // Threshold for automatic blocking
    block_threshold: f64,
    // IP blocklist
    blocklist: Mutex<HashMap<IpAddr, Instant>>,
    // Block duration
    block_duration: Duration,
}

impl AnomalyDetector {
    fn new(
        window_seconds: u64,
        block_threshold: f64,
        block_duration_minutes: u64,
    ) -> Self {
        AnomalyDetector {
            patterns: Mutex::new(HashMap::new()),
            window: Duration::from_secs(window_seconds),
            block_threshold,
            blocklist: Mutex::new(HashMap::new()),
            block_duration: Duration::from_secs(block_duration_minutes * 60),
        }
    }
    
    fn record_request(&self, ip: IpAddr, path: &str) -> bool {
        let now = Instant::now();
        
        // Check if IP is blocklisted
        {
            let mut blocklist = self.blocklist.lock().unwrap();
            
            // Clean expired entries
            blocklist.retain(|_, time| {
                now.duration_since(*time) < self.block_duration
            });
            
            // Check if IP is in blocklist
            if blocklist.contains_key(&ip) {
                return true; // IP is blocked
            }
        }
        
        // Update request pattern
        {
            let mut patterns = self.patterns.lock().unwrap();
            let pattern = patterns.entry(ip).or_insert(RequestPattern {
                path_counts: HashMap::new(),
                total_requests: 0,
                last_request: now,
                suspicion_score: 0.0,
            });
            
            // Update pattern data
            *pattern.path_counts.entry(path.to_string()).or_insert(0) += 1;
            pattern.total_requests += 1;
            
            // Calculate request rate
            let elapsed = now.duration_since(pattern.last_request);
            let request_rate = if elapsed.as_millis() == 0 {
                // Two requests in the same millisecond is suspicious
                10.0
            } else {
                1000.0 / elapsed.as_millis() as f64
            };
            
            // Update suspicion score based on various factors
            
            // 1. High request rate increases suspicion
            pattern.suspicion_score += request_rate * 0.1;
            
            // 2. Repetitive requests to the same path increase suspicion
            let path_ratio = *pattern.path_counts.get(path).unwrap() as f64 
                / pattern.total_requests as f64;
            if path_ratio > 0.8 && pattern.total_requests > 10 {
                pattern.suspicion_score += 5.0;
            }
            
            // 3. Requests to sensitive paths increase suspicion
            if path.contains("admin") || 
               path.contains("login") || 
               path.contains("password") {
                pattern.suspicion_score += 2.0;
            }
            
            // 4. Gradually decrease suspicion over time
            let time_factor = (now.duration_since(pattern.last_request).as_secs() as f64)
                .min(60.0) / 60.0;
            pattern.suspicion_score = (pattern.suspicion_score * (1.0 - time_factor)).max(0.0);
            
            pattern.last_request = now;
            
            // Check if threshold exceeded
            if pattern.suspicion_score > self.block_threshold {
                // Add to blocklist
                let mut blocklist = self.blocklist.lock().unwrap();
                blocklist.insert(ip, now);
                
                log::warn!("Blocking suspicious IP {} with score {:.2}", 
                          ip, pattern.suspicion_score);
                
                true
            } else {
                false
            }
        }
    }
}

// Usage in a request filter
async fn anomaly_detection_filter(
    req: ServiceRequest,
    detector: web::Data<AnomalyDetector>,
) -> Result<ServiceRequest, Error> {
    let ip = req.peer_addr().map(|addr| addr.ip())
        .unwrap_or_else(|| "0.0.0.0".parse().unwrap());
    
    let path = req.path().to_string();
    
    if detector.record_request(ip, &path) {
        // Request from blocked IP
        Err(ErrorForbidden("Access denied due to suspicious activity"))
    } else {
        Ok(req)
    }
}
```

## Graceful Degradation Strategies

### 1. Circuit Breaker Pattern

Protect overloaded backend services:

```rust
use std::sync::atomic::{AtomicUsize, AtomicBool, Ordering};
use std::time::{Duration, Instant};

enum CircuitState {
    Closed,
    Open(Instant),
    HalfOpen,
}

struct CircuitBreaker {
    state: RwLock<CircuitState>,
    failure_threshold: usize,
    failure_count: AtomicUsize,
    reset_timeout: Duration,
}

impl CircuitBreaker {
    fn new(failure_threshold: usize, reset_timeout: Duration) -> Self {
        CircuitBreaker {
            state: RwLock::new(CircuitState::Closed),
            failure_threshold,
            failure_count: AtomicUsize::new(0),
            reset_timeout,
        }
    }
    
    async fn call<F, T>(&self, f: F) -> Result<T, Error>
    where
        F: FnOnce() -> Pin<Box<dyn Future<Output = Result<T, Error>> + Send>>,
    {
        // Check circuit state
        let current_state = {
            let state = self.state.read().await;
            match *state {
                CircuitState::Open(opened_at) => {
                    // Check if timeout has elapsed
                    if opened_at.elapsed() > self.reset_timeout {
                        // Transition to half-open
                        drop(state);
                        *self.state.write().await = CircuitState::HalfOpen;
                        "half-open"
                    } else {
                        "open"
                    }
                }
                CircuitState::HalfOpen => "half-open",
                CircuitState::Closed => "closed",
            }
        };
        
        // If circuit is open, fail fast
        if current_state == "open" {
            return Err(ErrorServiceUnavailable("Circuit breaker is open"));
        }
        
        // Execute function
        match f().await {
            Ok(result) => {
                // Success - if half-open, close the circuit
                if current_state == "half-open" {
                    *self.state.write().await = CircuitState::Closed;
                    self.failure_count.store(0, Ordering::Relaxed);
                }
                
                Ok(result)
            }
            Err(err) => {
                // Failure - increment count
                let current_count = self.failure_count.fetch_add(1, Ordering::Relaxed) + 1;
                
                // If threshold reached, open the circuit
                if current_count >= self.failure_threshold {
                    *self.state.write().await = CircuitState::Open(Instant::now());
                    self.failure_count.store(0, Ordering::Relaxed);
                    
                    log::warn!("Circuit breaker opened after {} failures", current_count);
                }
                
                Err(err)
            }
        }
    }
}

// Usage with a database query
async fn get_user_data(
    db: web::Data<DbPool>,
    circuit_breaker: web::Data<CircuitBreaker>,
    user_id: i32,
) -> Result<User, Error> {
    circuit_breaker.call(|| {
        Box::pin(async move {
            // Attempt database query
            let result = sqlx::query_as!(
                User,
                "SELECT * FROM users WHERE id = $1",
                user_id
            )
            .fetch_one(&db)
            .await
            .map_err(|e| {
                ErrorInternalServerError(format!("Database error: {}", e))
            });
            
            result
        })
    }).await
}
```

### 2. Fallback Responses

Provide degraded service during overload:

```rust
use std::sync::atomic::{AtomicBool, Ordering};

struct OverloadProtection {
    high_load: AtomicBool,
    // External load monitor sets this flag
}

impl OverloadProtection {
    fn new() -> Self {
        OverloadProtection {
            high_load: AtomicBool::new(false),
        }
    }
    
    fn is_overloaded(&self) -> bool {
        self.high_load.load(Ordering::Relaxed)
    }
    
    fn set_overloaded(&self, overloaded: bool) {
        self.high_load.store(overloaded, Ordering::Relaxed);
    }
}

// Provide fallback for search results during high load
async fn search_products(
    query: web::Query<SearchQuery>,
    db: web::Data<DbPool>,
    overload_protection: web::Data<OverloadProtection>,
) -> Result<HttpResponse, Error> {
    if overload_protection.is_overloaded() {
        // Serve cached results or simplified version
        let fallback_results = get_top_products().await?;
        
        return Ok(HttpResponse::Ok()
            .append_header(("X-Degraded-Response", "true"))
            .json(fallback_results));
    }
    
    // Normal search logic
    let results = perform_full_search(&db, &query.term).await?;
    
    Ok(HttpResponse::Ok().json(results))
}

// Background task to monitor system load
async fn monitor_load(protection: Arc<OverloadProtection>) {
    let mut interval = tokio::time::interval(Duration::from_secs(5));
    
    loop {
        interval.tick().await;
        
        let system_load = get_system_load().await;
        
        // Set overload flag if CPU or memory utilization is too high
        protection.set_overloaded(system_load > 85);
        
        if system_load > 85 {
            log::warn!("System under high load ({}%), enabling degraded mode", system_load);
        } else if system_load < 70 && protection.is_overloaded() {
            log::info!("System load returned to normal ({}%), disabling degraded mode", system_load);
        }
    }
}
```

## Best Practices

1. **Defense in Depth**
   - Implement rate limiting at multiple levels (network, application)
   - Combine different strategies (IP-based, user-based, endpoint-specific)
   - Have fallback mechanisms if primary protection fails

2. **Progressive Rate Limiting**
   - Start with gentle rate limits and increase restrictions for abusive clients
   - Implement exponential backoff for repeated violations
   - Use CAPTCHA or other verification for suspicious clients

3. **Monitoring and Alerting**
   - Monitor traffic patterns to identify attacks
   - Set up alerts for unusual traffic spikes
   - Log rate limit events for analysis

4. **Client Communication**
   - Use appropriate HTTP status codes (429 Too Many Requests)
   - Include Retry-After headers to indicate when to retry
   - Provide clear error messages to legitimate users

5. **API Design for Resilience**
   - Use pagination to limit resource consumption
   - Design idempotent APIs to handle retries safely
   - Implement request validation to reject malformed requests early

## Knowledge Check

1. **What is the difference between fixed window and sliding window rate limiting?**
   - Fixed window resets counters at specific times, which can allow bursts at window boundaries. Sliding window continuously tracks requests over a moving time period, providing more consistent rate limiting.

2. **How does the token bucket algorithm allow for burst handling?**
   - Token bucket continuously adds tokens at a set rate up to a maximum capacity, allowing clients to "save up" tokens for occasional bursts while still enforcing a long-term rate.

3. **Why is distributed rate limiting important in a multi-server environment?**
   - Without distributed rate limiting, clients could exceed global limits by spreading requests across different servers. Distributed rate limiting ensures consistent enforcement.

4. **What is the circuit breaker pattern and how does it help during overload?**
   - The circuit breaker pattern monitors for failures and "trips" after a threshold, preventing cascading failures by failing fast and allowing overloaded systems time to recover.

5. **What HTTP status code should be returned when a client exceeds a rate limit?**
   - 429 Too Many Requests is the appropriate status code, ideally with a Retry-After header.

## Integration

- Integrate rate limiting with your authentication system (Module 20)
- Apply rate limiting selectively to different API endpoints (Module 24)
- Implement as middleware in your middleware pipeline (Module 25)
- Expose rate limiting metrics through your monitoring system (Module 28)

## Additional Resources

- [OWASP Denial of Service Prevention Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Denial_of_Service_Cheat_Sheet.html)
- [Rate Limiting - Architecting Modern Web Applications](https://docs.microsoft.com/en-us/dotnet/architecture/cloud-native/rate-limiting)
- [Redis Rate Limiting Pattern](https://redis.io/commands/incr#pattern-rate-limiter)
- [Circuit Breaker Pattern](https://martinfowler.com/bliki/CircuitBreaker.html)

[Previous: Authentication and Authorization](20-authentication-authorization.md) | [Next: Request Validation](22-request-validation.md)

## Quiz
1. What is rate limiting?
2. How can you mitigate DoS attacks?

## Diagram
```mermaid
graph TD
    A[Client] -- Request --> B[Webserver]
    B -- Rate Check --> C[Limiter]
    C -- Allow/Block --> B
```

## Security Considerations

### Threat Model for Rate Limiting and DoS Protection

Understanding the potential attacks on your rate limiting and DoS protection systems is the first step in creating effective defenses:

```mermaid
flowchart TD
    A[Attacker] -->|1. Distributed requests| RL[Rate Limiter]
    A -->|2. IP spoofing| RL
    A -->|3. Slow request attacks| RL
    A -->|4. Rate limit bypass| RL
    A -->|5. Resource exhaustion| Server[Web Server]
    A -->|6. Rate limiter DoS| RL
    
    RL --> D{Decision}
    D -->|Allow| Server
    D -->|Block| R[Response 429]
    
    class A fill:#f96,stroke:#333
    class RL fill:#69f,stroke:#333
    class Server fill:#6d9,stroke:#333
    class D fill:#fc9,stroke:#333
    class R fill:#f99,stroke:#333
```

### 1. Secure Rate Limiter Implementation

Your rate limiting implementation itself must be designed to withstand attacks:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use ring::rand::{SecureRandom, SystemRandom};
use ring::hmac;
use base64::{Engine as _, engine::general_purpose};

// Enhanced rate limiter with security features
struct SecureRateLimiter {
    // Use mutex for thread safety
    ip_counters: Mutex<HashMap<IpAddr, RateData>>,
    user_counters: Mutex<HashMap<String, RateData>>,
    // Use separate limits for different operations
    limits: HashMap<RequestType, RateLimit>,
    // Secure random source for jitter
    rng: SystemRandom,
    // HMAC key for IP hash
    ip_hash_key: hmac::Key,
}

struct RateData {
    attempts: Vec<Instant>,
    last_notification: Instant,
    block_until: Option<Instant>,
}

struct RateLimit {
    max_attempts: usize,
    window: Duration,
    block_duration: Duration,
}

enum RequestType {
    Login,
    Registration,
    ApiCall,
    StaticContent,
    AdminAction,
}

impl SecureRateLimiter {
    fn new() -> Self {
        // Create secure random for jitter and key generation
        let rng = SystemRandom::new();
        
        // Generate random HMAC key for IP hashing
        let mut key_bytes = [0u8; 32];
        rng.fill(&mut key_bytes).expect("Failed to generate random key");
        
        // Create HMAC key
        let ip_hash_key = hmac::Key::new(hmac::HMAC_SHA256, &key_bytes);
        
        // Define rate limits for different operations
        let mut limits = HashMap::new();
        limits.insert(RequestType::Login, RateLimit {
            max_attempts: 5,
            window: Duration::from_secs(300),
            block_duration: Duration::from_secs(1800),
        });
        limits.insert(RequestType::Registration, RateLimit {
            max_attempts: 3,
            window: Duration::from_secs(3600),
            block_duration: Duration::from_secs(86400),
        });
        limits.insert(RequestType::ApiCall, RateLimit {
            max_attempts: 100,
            window: Duration::from_secs(60),
            block_duration: Duration::from_secs(300),
        });
        
        Self {
            ip_counters: Mutex::new(HashMap::new()),
            user_counters: Mutex::new(HashMap::new()),
            limits,
            rng,
            ip_hash_key,
        }
    }
    
    // Check if a request should be rate limited
    fn check_rate_limit(
        &self, 
        ip: Option<IpAddr>, 
        user_id: Option<&str>,
        request_type: RequestType,
    ) -> Result<(), RateLimitError> {
        let now = Instant::now();
        
        // Apply jitter to make timing attacks harder
        self.apply_jitter()?;
        
        // Get rate limit for this request type
        let limit = match self.limits.get(&request_type) {
            Some(l) => l,
            None => return Ok(()), // No limit defined, allow request
        };
        
        // Check IP-based rate limiting first
        if let Some(ip_addr) = ip {
            // Check if IP is in denylist
            if self.is_denylisted_ip(&ip_addr) {
                return Err(RateLimitError::IpDenied);
            }
            
            let is_limited = {
                let mut counters = self.ip_counters.lock().unwrap();
                
                // Get or create counter for this IP
                let counter = counters.entry(ip_addr).or_insert_with(|| {
                    RateData {
                        attempts: Vec::with_capacity(limit.max_attempts),
                        last_notification: now,
                        block_until: None,
                    }
                });
                
                // Check if currently blocked
                if let Some(block_time) = counter.block_until {
                    if now < block_time {
                        // Still blocked
                        return Err(RateLimitError::IpLimited {
                            retry_after: block_time.duration_since(now).as_secs(),
                        });
                    } else {
                        // Block expired, reset
                        counter.block_until = None;
                        counter.attempts.clear();
                    }
                }
                
                // Clean up old attempts outside window
                counter.attempts.retain(|time| {
                    now.duration_since(*time) < limit.window
                });
                
                // Check if over limit
                let over_limit = counter.attempts.len() >= limit.max_attempts;
                
                // If over limit, block for the specified duration
                if over_limit {
                    counter.block_until = Some(now + limit.block_duration);
                    
                    // Only send notification once per block to prevent notification floods
                    if now.duration_since(counter.last_notification) > Duration::from_secs(300) {
                        counter.last_notification = now;
                        
                        // Log aggressive rate limiting
                        log::warn!(
                            "Rate limit exceeded for IP {}: {} requests in {:?}, blocking for {:?}",
                            self.hash_ip(&ip_addr),
                            counter.attempts.len(),
                            limit.window,
                            limit.block_duration
                        );
                    }
                    
                    true
                } else {
                    // Record this attempt
                    counter.attempts.push(now);
                    false
                }
            };
            
            if is_limited {
                return Err(RateLimitError::IpLimited {
                    retry_after: limit.block_duration.as_secs(),
                });
            }
        }
        
        // Then check user-based rate limiting if applicable
        if let Some(uid) = user_id {
            let is_limited = {
                let mut counters = self.user_counters.lock().unwrap();
                
                // Get or create counter for this user
                let counter = counters.entry(uid.to_string()).or_insert_with(|| {
                    RateData {
                        attempts: Vec::with_capacity(limit.max_attempts * 2), // Users get higher limit
                        last_notification: now,
                        block_until: None,
                    }
                });
                
                // Check if currently blocked
                if let Some(block_time) = counter.block_until {
                    if now < block_time {
                        // Still blocked
                        return Err(RateLimitError::UserLimited {
                            retry_after: block_time.duration_since(now).as_secs(),
                        });
                    } else {
                        // Block expired, reset
                        counter.block_until = None;
                        counter.attempts.clear();
                    }
                }
                
                // Clean up old attempts outside window
                counter.attempts.retain(|time| {
                    now.duration_since(*time) < limit.window
                });
                
                // User limits are typically higher than IP limits
                let user_max = limit.max_attempts * 2;
                
                // Check if over limit
                let over_limit = counter.attempts.len() >= user_max;
                
                // If over limit, block for the specified duration
                if over_limit {
                    counter.block_until = Some(now + limit.block_duration);
                    
                    // Log aggressive user rate limiting
                    log::warn!(
                        "Rate limit exceeded for user {}: {} requests in {:?}, blocking for {:?}",
                        uid,
                        counter.attempts.len(),
                        limit.window,
                        limit.block_duration
                    );
                    
                    true
                } else {
                    // Record this attempt
                    counter.attempts.push(now);
                    false
                }
            };
            
            if is_limited {
                return Err(RateLimitError::UserLimited {
                    retry_after: limit.block_duration.as_secs(),
                });
            }
        }
        
        // Not rate limited
        Ok(())
    }
    
    // Hash IP address for logging to protect privacy
    fn hash_ip(&self, ip: &IpAddr) -> String {
        let ip_bytes = match ip {
            IpAddr::V4(ipv4) => ipv4.octets().to_vec(),
            IpAddr::V6(ipv6) => ipv6.octets().to_vec(),
        };
        
        let tag = hmac::sign(&self.ip_hash_key, &ip_bytes);
        general_purpose::STANDARD.encode(tag.as_ref())
    }
    
    // Apply random jitter to prevent timing attacks
    fn apply_jitter(&self) -> Result<(), RateLimitError> {
        let mut jitter_ms = [0u8; 2];
        self.rng.fill(&mut jitter_ms).map_err(|_| RateLimitError::Internal)?;
        
        // Convert to milliseconds (0-10ms)
        let jitter = (u16::from_be_bytes(jitter_ms) % 10) as u64;
        
        // Sleep for the random duration
        std::thread::sleep(Duration::from_millis(jitter));
        
        Ok(())
    }
    
    // Check if an IP is in the denylist
    fn is_denylisted_ip(&self, ip: &IpAddr) -> bool {
        // Implement IP denylist check
        // Could use a bloom filter for efficient storage of large IP lists
        false
    }
}

// Custom error type with security-focused messages
enum RateLimitError {
    IpLimited { retry_after: u64 },
    UserLimited { retry_after: u64 },
    IpDenied,
    Internal,
}
```

### 2. Distributed Rate Limiting Security

When implementing rate limiting across multiple server instances, ensure secure communication:

```rust
use redis::{Client, Commands, RedisResult};
use serde::{Serialize, Deserialize};
use hmac::{Hmac, Mac, NewMac};
use sha2::Sha256;
use std::time::{SystemTime, UNIX_EPOCH};

// Secure distributed rate limiter using Redis
struct SecureDistributedRateLimiter {
    redis: Client,
    // API key for authenticating with Redis
    api_key: String,
    // HMAC key for request signing
    hmac_key: Vec<u8>,
    // Instance identifier
    instance_id: String,
}

// Rate limit request with authentication
#[derive(Serialize, Deserialize)]
struct RateLimitRequest {
    // Identifier (IP, user ID, etc)
    id: String,
    // Request type
    request_type: String,
    // Timestamp
    timestamp: u64,
    // Instance that made the request
    instance_id: String,
    // HMAC signature
    signature: String,
}

impl SecureDistributedRateLimiter {
    fn new(redis_url: &str, api_key: &str, hmac_key: &[u8], instance_id: &str) -> RedisResult<Self> {
        let redis = Client::open(redis_url)?;
        
        Ok(Self {
            redis,
            api_key: api_key.to_string(),
            hmac_key: hmac_key.to_vec(),
            instance_id: instance_id.to_string(),
        })
    }
    
    async fn is_rate_limited(&self, id: &str, request_type: &str) -> RedisResult<bool> {
        // Create authenticated request
        let request = self.create_signed_request(id, request_type)?;
        
        let mut conn = self.redis.get_async_connection().await?;
        
        // Set Redis authentication
        redis::cmd("AUTH")
            .arg(&self.api_key)
            .query_async(&mut conn)
            .await?;
        
        // Verify request hasn't expired
        if !self.is_request_valid(&request) {
            return Ok(true); // Consider rate limited for safety
        }
        
        // Use Redis for rate limiting with signed data
        let key = format!("ratelimit:{}:{}", request_type, id);
        
        // Pipeline for atomic operations with authentication
        let pipeline = redis::pipe()
            // Add current timestamp to sorted set
            .atomic()
            .zadd(&key, SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(), request.signature)
            // Remove timestamps outside the window (5 minutes)
            .zremrangebyscore(&key, 0, (SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() - 300))
            // Count remaining entries
            .zcard(&key)
            // Set expiry on the key
            .expire(&key, 600)
            // Execute all commands
            .query_async(&mut conn)
            .await?;
        
        // Get the count from pipeline result
        let count: usize = pipeline[2];
        
        // Each request type has different limits
        let limit = match request_type {
            "login" => 5,
            "api" => 100,
            "admin" => 20,
            _ => 30,
        };
        
        Ok(count > limit)
    }
    
    // Create signed request for authentication
    fn create_signed_request(&self, id: &str, request_type: &str) -> RedisResult<RateLimitRequest> {
        // Get current timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // Create message to sign
        let message = format!("{}:{}:{}:{}", id, request_type, timestamp, self.instance_id);
        
        // Create HMAC
        let mut mac = Hmac::<Sha256>::new_from_slice(&self.hmac_key)
            .map_err(|_| redis::RedisError::from((redis::ErrorKind::IoError, "HMAC key error")))?;
        
        mac.update(message.as_bytes());
        
        // Get HMAC result
        let signature = hex::encode(mac.finalize().into_bytes());
        
        Ok(RateLimitRequest {
            id: id.to_string(),
            request_type: request_type.to_string(),
            timestamp,
            instance_id: self.instance_id.clone(),
            signature,
        })
    }
    
    // Verify request is still valid (not expired, not tampered)
    fn is_request_valid(&self, request: &RateLimitRequest) -> bool {
        // Check timestamp isn't too old (prevent replay attacks)
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        if current_time - request.timestamp > 30 {
            return false; // Request too old
        }
        
        // Verify signature
        let message = format!(
            "{}:{}:{}:{}", 
            request.id, 
            request.request_type, 
            request.timestamp, 
            request.instance_id
        );
        
        let mut mac = match Hmac::<Sha256>::new_from_slice(&self.hmac_key) {
            Ok(m) => m,
            Err(_) => return false,
        };
        
        mac.update(message.as_bytes());
        
        // Verify signature with constant-time comparison
        match hex::decode(&request.signature) {
            Ok(sig_bytes) => {
                mac.verify_slice(&sig_bytes).is_ok()
            }
            Err(_) => false,
        }
    }
}
```

### 3. Preventing DoS Attacks on Rate Limiting System

The rate limiting system itself can be a DoS target. Implement safeguards to prevent this:

```rust
use std::collections::{HashMap, HashSet, VecDeque};
use std::net::IpAddr;
use std::sync::Arc;
use tokio::sync::RwLock;
use lru::LruCache;

// DoS-resistant rate limiter
struct DoSResistantRateLimiter {
    // Use RwLock for better concurrency
    ip_tracking: RwLock<LruCache<IpAddr, RateTracker>>,
    // Track suspicious IPs
    suspicious_ips: RwLock<HashSet<IpAddr>>,
    // Global counters for detecting attacks on the limiter itself
    global_counters: RwLock<GlobalCounters>,
    // Metrics for monitoring
    metrics: Arc<Metrics>,
}

struct RateTracker {
    attempts: VecDeque<u64>, // Timestamps
    blocked_until: Option<u64>,
}

struct GlobalCounters {
    // Total requests in last minute
    recent_requests: VecDeque<u64>,
    // Track if we're under attack
    defensive_mode: bool,
    // When defensive mode started
    defensive_mode_start: Option<u64>,
}

struct Metrics {
    // Atomic counters for monitoring
    requests_processed: AtomicU64,
    requests_limited: AtomicU64,
    requests_allowed: AtomicU64,
    suspicious_ips_tracked: AtomicU32,
}

impl DoSResistantRateLimiter {
    fn new(capacity: usize) -> Self {
        Self {
            // Use LRU cache to prevent unbounded memory growth
            ip_tracking: RwLock::new(LruCache::new(capacity)),
            suspicious_ips: RwLock::new(HashSet::new()),
            global_counters: RwLock::new(GlobalCounters {
                recent_requests: VecDeque::new(),
                defensive_mode: false,
                defensive_mode_start: None,
            }),
            metrics: Arc::new(Metrics {
                requests_processed: AtomicU64::new(0),
                requests_limited: AtomicU64::new(0),
                requests_allowed: AtomicU64::new(0),
                suspicious_ips_tracked: AtomicU32::new(0),
            }),
        }
    }
    
    async fn check_rate_limit(&self, ip: IpAddr) -> bool {
        // Update metrics
        self.metrics.requests_processed.fetch_add(1, Ordering::Relaxed);
        
        // Get current timestamp
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // First check global DoS detection
        {
            let mut global = self.global_counters.write().await;
            
            // Clean up old timestamps
            while let Some(ts) = global.recent_requests.front() {
                if now - *ts > 60 { // 1 minute window
                    global.recent_requests.pop_front();
                } else {
                    break;
                }
            }
            
            // Add current request timestamp
            global.recent_requests.push_back(now);
            
            // Check if request rate is extremely high
            if global.recent_requests.len() > 10000 { // 10K requests per minute
                if !global.defensive_mode {
                    global.defensive_mode = true;
                    global.defensive_mode_start = Some(now);
                    
                    log::warn!("Activating defensive mode due to high request rate: {} reqs/min", 
                              global.recent_requests.len());
                }
            } else if global.defensive_mode {
                // Check if we should exit defensive mode (after 5 minutes)
                if let Some(start) = global.defensive_mode_start {
                    if now - start > 300 {
                        global.defensive_mode = false;
                        global.defensive_mode_start = None;
                        
                        log::info!("Exiting defensive mode, request rate normalized");
                    }
                }
            }
            
            // In defensive mode, apply stricter limits
            if global.defensive_mode {
                // Check if this IP is suspicious
                let suspicious = {
                    let suspicious_ips = self.suspicious_ips.read().await;
                    suspicious_ips.contains(&ip)
                };
                
                if suspicious {
                    // Immediately rate limit suspicious IPs in defensive mode
                    self.metrics.requests_limited.fetch_add(1, Ordering::Relaxed);
                    return true; // Rate limited
                }
            }
        }
        
        // Now check individual IP limits
        let is_limited = {
            // First check if IP is in suspicious list
            let is_suspicious = {
                let suspicious_ips = self.suspicious_ips.read().await;
                suspicious_ips.contains(&ip)
            };
            
            // Get lower threshold for suspicious IPs
            let threshold = if is_suspicious { 10 } else { 30 };
            
            let mut tracking = self.ip_tracking.write().await;
            
            // Memory-efficient tracking
            let entry = match tracking.get_mut(&ip) {
                Some(entry) => entry,
                None => {
                    // Create new tracker
                    tracking.put(ip, RateTracker {
                        attempts: VecDeque::with_capacity(threshold),
                        blocked_until: None,
                    });
                    
                    tracking.get_mut(&ip).unwrap()
                }
            };
            
            // Check if currently blocked
            if let Some(blocked_until) = entry.blocked_until {
                if now < blocked_until {
                    // Still blocked
                    return true;
                } else {
                    // Block expired
                    entry.blocked_until = None;
                    entry.attempts.clear();
                }
            }
            
            // Clean up old attempts
            while let Some(ts) = entry.attempts.front() {
                if now - *ts > 60 { // 1 minute window
                    entry.attempts.pop_front();
                } else {
                    break;
                }
            }
            
            // Check rate
            if entry.attempts.len() >= threshold {
                // Rate exceeded
                
                // Block for increasingly longer periods
                let block_duration = match entry.attempts.len() {
                    n if n >= 100 => 3600,  // 1 hour
                    n if n >= 50 => 1800,   // 30 minutes
                    _ => 300,               // 5 minutes
                };
                
                entry.blocked_until = Some(now + block_duration);
                
                // Maybe add to suspicious list for persistent offenders
                if entry.attempts.len() >= 50 {
                    let mut suspicious = self.suspicious_ips.write().await;
                    suspicious.insert(ip);
                    self.metrics.suspicious_ips_tracked.fetch_add(1, Ordering::Relaxed);
                    
                    log::warn!("Added {} to suspicious IP list - {} requests in 60s",
                              ip, entry.attempts.len());
                }
                
                true
            } else {
                // Record this attempt
                entry.attempts.push_back(now);
                false
            }
        };
        
        if is_limited {
            self.metrics.requests_limited.fetch_add(1, Ordering::Relaxed);
        } else {
            self.metrics.requests_allowed.fetch_add(1, Ordering::Relaxed);
        }
        
        is_limited
    }
    
    // Periodically clean up tracking data
    async fn maintenance_task(&self) {
        loop {
            // Sleep for 5 minutes
            tokio::time::sleep(Duration::from_secs(300)).await;
            
            // Clean up suspicious IP list (remove IPs after 24 hours)
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();
            
            let suspicious_count = {
                let mut suspicious = self.suspicious_ips.write().await;
                let before_count = suspicious.len();
                
                // In a real implementation, you'd store timestamps with IPs
                // This is simplified for the example
                
                before_count
            };
            
            log::info!("Maintenance task: tracking {} suspicious IPs", suspicious_count);
            
            // Report metrics
            log::info!("Rate limiter metrics: processed={}, limited={}, allowed={}", 
                      self.metrics.requests_processed.load(Ordering::Relaxed),
                      self.metrics.requests_limited.load(Ordering::Relaxed),
                      self.metrics.requests_allowed.load(Ordering::Relaxed));
        }
    }
}

// Start background maintenance task
fn start_maintenance(limiter: Arc<DoSResistantRateLimiter>) {
    tokio::spawn(async move {
        limiter.maintenance_task().await;
    });
}
```

### 4. Secure Circuit Breaker Pattern

Implement a circuit breaker that cannot be manipulated by attackers:

```rust
use std::sync::atomic::{AtomicUsize, AtomicBool, Ordering};
use std::time::{Duration, Instant};
use std::future::Future;
use std::pin::Pin;
use tokio::sync::Mutex;
use rand::{thread_rng, Rng};
use std::hash::{Hash, Hasher};
use std::collections::hash_map::DefaultHasher;

// States for the circuit breaker
enum CircuitState {
    Closed,
    Open(Instant),
    HalfOpen {
        attempts_allowed: AtomicUsize,
        started_at: Instant,
    },
}

// Secure circuit breaker with anti-manipulation features
struct SecureCircuitBreaker {
    state: Mutex<CircuitState>,
    failure_threshold: usize,
    failure_count: AtomicUsize,
    success_count: AtomicUsize,
    reset_timeout: Duration,
    half_open_max_requests: usize,
    // Prevent predictable behavior
    jitter_factor: f64,
    // Prevent time-based attacks
    time_attack_protection: AtomicBool,
    // Track recent errors with hash
    error_hash: AtomicUsize,
}

impl SecureCircuitBreaker {
    fn new(
        failure_threshold: usize, 
        reset_timeout: Duration,
        half_open_max_requests: usize,
    ) -> Self {
        Self {
            state: Mutex::new(CircuitState::Closed),
            failure_threshold,
            failure_count: AtomicUsize::new(0),
            success_count: AtomicUsize::new(0),
            reset_timeout,
            half_open_max_requests,
            jitter_factor: 0.2, // 20% random jitter
            time_attack_protection: AtomicBool::new(false),
            error_hash: AtomicUsize::new(0),
        }
    }
    
    // Call a service with circuit breaker protection
    async fn call<F, Fut, T, E>(&self, request_hash: u64, f: F) -> Result<T, CircuitBreakerError<E>>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T, E>>,
        E: std::fmt::Display,
    {
        // Apply jitter to calls if time attack protection is enabled
        if self.time_attack_protection.load(Ordering::Relaxed) {
            self.apply_jitter().await;
        }
        
        // Check circuit state
        let current_state = {
            let state = self.state.lock().await;
            match &*state {
                CircuitState::Open(opened_at) => {
                    // Check if timeout has elapsed
                    if opened_at.elapsed() > self.get_reset_timeout() {
                        // Transition to half-open
                        drop(state);
                        let mut state = self.state.lock().await;
                        *state = CircuitState::HalfOpen {
                            attempts_allowed: AtomicUsize::new(self.half_open_max_requests),
                            started_at: Instant::now(),
                        };
                        "half-open"
                    } else {
                        "open"
                    }
                }
                CircuitState::HalfOpen { .. } => "half-open",
                CircuitState::Closed => "closed",
            }
        };
        
        // If circuit is open, fail fast
        if current_state == "open" {
            return Err(CircuitBreakerError::CircuitOpen);
        }
        
        // If half-open, limit concurrent requests
        if current_state == "half-open" {
            let allowed = {
                let state = self.state.lock().await;
                if let CircuitState::HalfOpen { attempts_allowed, .. } = &*state {
                    // Safely decrement remaining attempts
                    attempts_allowed.fetch_update(
                        Ordering::SeqCst,
                        Ordering::SeqCst,
                        |count| if count > 0 { Some(count - 1) } else { None }
                    ).is_ok()
                } else {
                    false
                }
            };
            
            if !allowed {
                return Err(CircuitBreakerError::TooManyRequests);
            }
        }
        
        // Execute function with timing metrics
        let start = Instant::now();
        let result = f().await;
        let elapsed = start.elapsed();
        
        match &result {
            Ok(_) => {
                // Success - handle half-open state transition
                if current_state == "half-open" {
                    let success_count = self.success_count.fetch_add(1, Ordering::SeqCst) + 1;
                    
                    // If enough successes in half-open, close the circuit
                    if success_count >= self.half_open_max_requests / 2 {
                        let mut state = self.state.lock().await;
                        if let CircuitState::HalfOpen { started_at, .. } = *state {
                            // Only close if this is the same half-open period
                            if Instant::now().duration_since(started_at) < Duration::from_secs(60) {
                                *state = CircuitState::Closed;
                                self.failure_count.store(0, Ordering::SeqCst);
                                self.success_count.store(0, Ordering::SeqCst);
                                
                                log::info!("Circuit breaker closed after successful recovery");
                            }
                        }
                    }
                }
                
                Ok(result.unwrap())
            }
            Err(err) => {
                // Hash error message to detect recurring similar errors
                let mut hasher = DefaultHasher::new();
                format!("{}", err).hash(&mut hasher);
                let error_hash = hasher.finish() as usize;
                
                // Update error hash for monitoring
                self.error_hash.store(error_hash, Ordering::Relaxed);
                
                // Increment failure count
                let current_count = self.failure_count.fetch_add(1, Ordering::SeqCst) + 1;
                
                // If threshold reached, open the circuit
                if current_count >= self.failure_threshold {
                    let mut state = self.state.lock().await;
                    
                    // Only open the circuit if not already open
                    match *state {
                        CircuitState::Open(_) => {
                            // Already open, do nothing
                        },
                        _ => {
                            *state = CircuitState::Open(Instant::now());
                            self.failure_count.store(0, Ordering::SeqCst);
                            self.success_count.store(0, Ordering::SeqCst);
                            
                            // Enable time attack protection
                            self.time_attack_protection.store(true, Ordering::SeqCst);
                            
                            log::warn!(
                                "Circuit breaker opened after {} failures, last error: {}, response time: {:?}",
                                current_count, err, elapsed
                            );
                        }
                    }
                }
                
                Err(CircuitBreakerError::ServiceError(format!("{}", err)))
            }
        }
    }
    
    // Add jitter to response time to prevent timing analysis
    async fn apply_jitter(&self) {
        let mut rng = thread_rng();
        let jitter_ms = rng.gen_range(5..20);
        tokio::time::sleep(Duration::from_millis(jitter_ms)).await;
    }
    
    // Get reset timeout with jitter to prevent predictable recovery
    fn get_reset_timeout(&self) -> Duration {
        let mut rng = thread_rng();
        let jitter = rng.gen_range((1.0 - self.jitter_factor)..(1.0 + self.jitter_factor));
        
        let millis = self.reset_timeout.as_millis() as f64 * jitter;
        
        Duration::from_millis(millis as u64)
    }
    
    // Get current circuit status for monitoring
    async fn get_status(&self) -> CircuitStatus {
        let state = self.state.lock().await;
        match &*state {
            CircuitState::Closed => CircuitStatus {
                state: "closed".to_string(),
                failure_count: self.failure_count.load(Ordering::Relaxed),
                success_count: self.success_count.load(Ordering::Relaxed),
                error_hash: self.error_hash.load(Ordering::Relaxed),
                opened_at: None,
                half_open_requests_remaining: None,
            },
            CircuitState::Open(opened_at) => CircuitStatus {
                state: "open".to_string(),
                failure_count: self.failure_count.load(Ordering::Relaxed),
                success_count: self.success_count.load(Ordering::Relaxed),
                error_hash: self.error_hash.load(Ordering::Relaxed),
                opened_at: Some(opened_at.elapsed().as_secs()),
                half_open_requests_remaining: None,
            },
            CircuitState::HalfOpen { attempts_allowed, started_at } => CircuitStatus {
                state: "half-open".to_string(),
                failure_count: self.failure_count.load(Ordering::Relaxed),
                success_count: self.success_count.load(Ordering::Relaxed),
                error_hash: self.error_hash.load(Ordering::Relaxed),
                opened_at: Some(started_at.elapsed().as_secs()),
                half_open_requests_remaining: Some(attempts_allowed.load(Ordering::Relaxed)),
            },
        }
    }
}

// Error type for circuit breaker
enum CircuitBreakerError<E> {
    CircuitOpen,
    TooManyRequests,
    ServiceError(E),
}

// Status for monitoring
struct CircuitStatus {
    state: String,
    failure_count: usize,
    success_count: usize,
    error_hash: usize,
    opened_at: Option<u64>,
    half_open_requests_remaining: Option<usize>,
}
```

### 5. Security Monitoring for Rate Limiting

Implement monitoring to detect and respond to attacks on your rate limiting system:

```rust
use prometheus::{IntCounter, IntGauge, Registry};
use std::sync::Arc;
use tokio::task::JoinHandle;
use chrono::{Utc, DateTime, Duration};
use std::collections::{HashMap, VecDeque};
use std::net::IpAddr;

// Monitoring system for rate limiting and DoS protection
struct RateLimitingMonitor {
    registry: Registry,
    // Total requests counter
    requests_total: IntCounter,
    // Rate limited requests counter
    limited_total: IntCounter,
    // Currently blocked IPs gauge
    blocked_ips: IntGauge,
    // Suspicious IPs being tracked
    suspicious_ips: IntGauge,
    
    // Attack detection system
    attack_detector: Arc<AttackDetector>,
}

impl RateLimitingMonitor {
    fn new() -> Self {
        let registry = Registry::new();
        
        let requests_total = IntCounter::new(
            "rate_limiting_requests_total", 
            "Total number of requests processed by rate limiter"
        ).unwrap();
        
        let limited_total = IntCounter::new(
            "rate_limiting_requests_limited", 
            "Total number of requests that were rate limited"
        ).unwrap();
        
        let blocked_ips = IntGauge::new(
            "rate_limiting_blocked_ips", 
            "Number of currently blocked IPs"
        ).unwrap();
        
        let suspicious_ips = IntGauge::new(
            "rate_limiting_suspicious_ips", 
            "Number of IPs being monitored as suspicious"
        ).unwrap();
        
        registry.register(Box::new(requests_total.clone())).unwrap();
        registry.register(Box::new(limited_total.clone())).unwrap();
        registry.register(Box::new(blocked_ips.clone())).unwrap();
        registry.register(Box::new(suspicious_ips.clone())).unwrap();
        
        Self {
            registry,
            requests_total,
            limited_total,
            blocked_ips,
            suspicious_ips,
            attack_detector: Arc::new(AttackDetector::new()),
        }
    }
    
    // Record a request being processed
    fn record_request(&self, ip: IpAddr, path: &str, limited: bool) {
        self.requests_total.inc();
        
        if limited {
            self.limited_total.inc();
        }
        
        // Feed data to attack detector
        self.attack_detector.record_request(ip, path, limited);
    }
    
    // Update metrics for blocked IPs
    fn update_blocked_count(&self, count: i64) {
        self.blocked_ips.set(count);
    }
    
    // Update metrics for suspicious IPs
    fn update_suspicious_count(&self, count: i64) {
        self.suspicious_ips.set(count);
    }
    
    // Start monitoring background tasks
    fn start_monitoring(&self) -> JoinHandle<()> {
        let detector = self.attack_detector.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::seconds(30).to_std().unwrap());
            
            loop {
                interval.tick().await;
                
                // Check for ongoing attacks
                if let Some(attack) = detector.detect_attacks().await {
                    log::warn!("Possible DoS attack detected: {:?}", attack);
                    
                    // Report attack details to security monitoring
                    if attack.confidence > 0.8 {
                        send_security_alert(&attack).await;
                    }
                }
            }
        })
    }
}

// Detector for DoS and DDoS attacks
struct AttackDetector {
    // Request data by IP
    ip_data: tokio::sync::Mutex<HashMap<IpAddr, VecDeque<RequestData>>>,
