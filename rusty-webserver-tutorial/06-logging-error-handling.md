<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\06-logging-error-handling.md -->
# Logging and Error Handling

## Learning Objectives
- Implement a comprehensive logging system for server operations and requests
- Design and build custom error types tailored for web server operations
- Create a robust error handling system that maps errors to appropriate HTTP responses
- Apply structured logging practices for better observability and debugging
- Integrate logging and error handling throughout the server codebase
- Configure different log levels for development and production environments

## Prerequisites
- Completion of Module 05: Configuration System
- Understanding of Rust's error handling patterns and Result type
- Familiarity with trait implementations in Rust
- Basic knowledge of logging concepts (levels, formats, destinations)
- Experience with HTTP status codes and error responses

## Navigation
- [Previous: Configuration System](05-configuration-system.md)
- [Next: Multithreaded Request Handling](07-multithreaded-request-handling.md)

## Introduction

Robust logging and error handling are essential components of any production-grade web server. In this module, we'll enhance our server with comprehensive logging capabilities and implement a structured error handling system that improves both developer experience and end-user feedback.

Logging provides visibility into server operations, helps with debugging issues, and creates an audit trail of requests and system events. A well-designed error handling system ensures graceful failure recovery, clear error reporting, and appropriate HTTP responses for different error conditions.

Together, these systems form the observability foundation of our web server, making it more reliable, maintainable, and production-ready.

## Understanding Logging and Error Handling

Before diving into implementation, let's develop a deep understanding of logging and error handling concepts in the context of web servers.

### Logging System Architecture

A well-designed logging system consists of several components working together:

```
┌─────────────┐     ┌──────────────┐     ┌────────────────┐
│ Log Sources │────>│ Log Router   │────>│ Log Sinks      │
│ - Requests  │     │ - Filtering  │     │ - Console      │
│ - Errors    │     │ - Formatting │     │ - Files        │
│ - System    │     │ - Enrichment │     │ - Network      │
└─────────────┘     └──────────────┘     └────────────────┘
```

**Log Sources**: Various parts of the server generating log events
**Log Router**: Central system that processes and directs log events
**Log Sinks**: Destinations where logs are ultimately written

### Logging Levels

Logs should be categorized by severity to enable appropriate filtering:

| Level | Usage | Example |
|-------|-------|---------|
| **ERROR** | Critical failures requiring immediate attention | Server cannot bind to port |
| **WARN** | Potential issues that don't prevent operation | Slow request processing |
| **INFO** | Normal but significant events | Server started, request handled |
| **DEBUG** | Detailed information for debugging | Request headers, parsing details |
| **TRACE** | Very detailed diagnostic information | Function entry/exit, variable values |

### Error Handling Flow

Error handling in a web server follows a structured path from occurrence to response:

```mermaid
flowchart TD
    Request[Client Request] --> Server
    Server --> |Process| Handler[Request Handler]
    Handler --> |Success| Success[Generate Success Response]
    Handler --> |Failure| Error[Error Detection]
    Error --> Logging[Log Error Details]
    Error --> Categorize[Categorize Error Type]
    Categorize --> HTTPError[Map to HTTP Status]
    HTTPError --> Generate[Generate Error Response]
    Generate --> Return[Return to Client]
    Success --> Return
    
    subgraph "Error Handling System"
        Error
        Logging
        Categorize
        HTTPError
        Generate
    end
```

This system ensures that:
1. Errors are detected and captured
2. Detailed information is logged for debugging
3. Appropriate HTTP responses are generated for clients
4. The server continues operating despite errors

## Implementation Strategy

We'll implement logging and error handling in several phases, building from basic functionality to more advanced features:

1. **Set up the logging system** with appropriate levels and formats
2. **Design custom error types** specific to our web server
3. **Create error-to-HTTP response mapping** to generate appropriate client responses
4. **Implement access logging** to track all requests in a standard format
5. **Integrate throughout the codebase** to ensure comprehensive coverage

### Phase 1: Setting Up the Logging System

#### Selecting Dependencies

First, we need to choose appropriate libraries for our logging system:

| Dependency | Purpose | Version | Features |
|------------|---------|---------|----------|
| `log` | Logging facade providing standardized macros | 0.4 | Core logging API, filtering |
| `env_logger` | Environment-configurable logger implementation | 0.10 | Environment variable configuration, formatting |
| `chrono` | Date and time handling | 0.4 | Timestamp formatting in logs |

These dependencies provide a flexible foundation that can be easily extended as our needs grow.

#### Adding Dependencies to Cargo.toml

```toml
[dependencies]
# Existing dependencies...

# Logging infrastructure
log = "0.4"
env_logger = "0.10"
chrono = "0.4"
```

The `log` crate provides a facade with macros like `error!`, `warn!`, `info!`, `debug!`, and `trace!`. The `env_logger` crate is an implementation that writes to stderr and can be configured via environment variables.

### 2. Set up Basic Logging

Create a module for logging in `src/logging.rs`:

```rust
use chrono::Local;
use env_logger::{Builder, Env};
use log::LevelFilter;
use std::io::Write;

/// Initialize the logging system
pub fn init_logger() -> Result<(), Box<dyn std::error::Error>> {
    // Create a custom log format with timestamps
    Builder::from_env(Env::default().default_filter_or("info"))
        .format(|buf, record| {
            writeln!(
                buf,
                "{} [{}] - {}",
                Local::now().format("%Y-%m-%d %H:%M:%S"),
                record.level(),
                record.args()
            )
        })
        .init();

    Ok(())
}

/// Logger for HTTP access logs in common log format
pub struct AccessLogger {
    /// Path to the access log file, or None for stdout
    log_path: Option<std::path::PathBuf>,
}

impl AccessLogger {
    /// Create a new access logger
    pub fn new(log_path: Option<std::path::PathBuf>) -> Self {
        Self { log_path }
    }

    /// Log an HTTP request in common log format
    pub fn log(&self, client: &str, method: &str, path: &str, status: u16, size: usize) {
        let now = Local::now();
        let message = format!(
            "{} - - [{}] \"{} {} HTTP/1.1\" {} {}",
            client,
            now.format("%d/%b/%Y:%H:%M:%S %z"),
            method,
            path,
            status,
            size
        );

        if let Some(path) = &self.log_path {
            // Write to file
            if let Ok(mut file) = std::fs::OpenOptions::new()
                .append(true)
                .create(true)
                .open(path)
            {
                let _ = writeln!(file, "{}", message);
            }
        } else {
            // Write to stdout
            println!("{}", message);
        }
    }
}
```

### Phase 3: Designing a Custom Error System

A well-designed error system should:
- Represent all possible error conditions in our server
- Provide context for debugging
- Enable consistent error handling
- Support conversion between different error types
- Map cleanly to HTTP status codes

#### Error Type Design

Let's create a dedicated error module in `src/error.rs` with a comprehensive error type:

```rust
use std::fmt;
use std::io;

/// Custom error types for the server
#[derive(Debug)]
pub enum ServerError {
    /// I/O error (file operations, network, etc.)
    Io(io::Error),
    
    /// HTTP protocol error (malformed request, unsupported feature)
    Http(String),
    
    /// Configuration error (invalid settings, missing files)
    Config(String),
    
    /// Resource not found (404 errors)
    NotFound(String),
    
    /// Error parsing HTTP request (malformed headers, invalid method)
    HttpParse(String),
    
    /// Server is too busy to handle the request (overloaded)
    ServerBusy,
    
    /// Access denied (permission issues, unauthorized)
    Forbidden(String),
    
    /// Request timeout (client too slow, network issues)
    Timeout(String),
    
    /// Generic error with a message
    Other(String),
}

impl fmt::Display for ServerError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ServerError::Io(err) => write!(f, "I/O error: {}", err),
            ServerError::Http(msg) => write!(f, "HTTP error: {}", msg),
            ServerError::Config(msg) => write!(f, "Configuration error: {}", msg),
            ServerError::NotFound(path) => write!(f, "Not found: {}", path),
            ServerError::HttpParse(msg) => write!(f, "Error parsing HTTP request: {}", msg),
            ServerError::ServerBusy => write!(f, "Server is too busy to handle the request"),
            ServerError::Forbidden(msg) => write!(f, "Access denied: {}", msg),
            ServerError::Timeout(msg) => write!(f, "Request timeout: {}", msg),
            ServerError::Other(msg) => write!(f, "{}", msg),
        }
    }
}

// Implement std::error::Error trait to make our error compatible with the ecosystem
impl std::error::Error for ServerError {}

// Implement From<io::Error> to allow using the ? operator with I/O operations
impl From<io::Error> for ServerError {
    fn from(err: io::Error) -> Self {
        ServerError::Io(err)
    }
}

// Additional conversions could be added for other error types
// For example:
impl From<serde_yaml::Error> for ServerError {
    fn from(err: serde_yaml::Error) -> Self {
        ServerError::Config(format!("YAML parsing error: {}", err))
    }
}

/// Type alias for Result with ServerError
pub type Result<T> = std::result::Result<T, ServerError>;
```

#### Error Design Considerations

Our error type uses the enum approach for several important reasons:

| Approach | Description | Advantages | Disadvantages |
|----------|-------------|------------|--------------|
| **Enum-based** (our choice) | All errors defined as enum variants | Type safety, exhaustive matching | Less flexible, requires updating enum |
| **Trait objects** | Box<dyn Error> | Flexible, allows any error type | No static type checking, runtime overhead |
| **Context-based** | Using crates like anyhow/eyre | Rich error context, stacktraces | More complex, additional dependencies |
| **Error code-based** | Numeric codes with messages | Simple, consistent | Less expressive, C-style approach |

We chose the enum-based approach because it provides:
- Clear, exhaustive listing of possible error cases
- Compile-time checking that we handle all error types
- Easy conversion to appropriate HTTP status codes
- Good performance with no runtime overhead

### Phase 4: Mapping Errors to HTTP Responses

A critical aspect of our error handling system is mapping server errors to appropriate HTTP responses for clients. This ensures that clients receive informative, standards-compliant error feedback.

#### Error-to-HTTP Status Mapping

Each server error type should correspond to a specific HTTP status code:

| Error Type | HTTP Status | Reason |
|------------|-------------|--------|
| NotFound | 404 Not Found | Resource doesn't exist |
| Forbidden | 403 Forbidden | Permission denied |
| HttpParse | 400 Bad Request | Malformed request |
| ServerBusy | 503 Service Unavailable | Server cannot handle request |
| Timeout | 408 Request Timeout | Request processing timed out |
| Io | 500 Internal Server Error | Server-side failure |
| Other | 500 Internal Server Error | Generic fallback |

#### Implementation

Let's create a function to convert our server errors to HTTP responses:

```rust
/// Convert a ServerError to an HTTP response
pub fn error_to_response(error: &ServerError) -> http::Response {
    use crate::http::{Response, StatusCode};
    use log::error;
    
    // Log the error before converting to response
    error!("Server error: {}", error);

    match error {
        ServerError::NotFound(path) => Response::new()
            .with_status(StatusCode::NotFound)
            .with_content_type("text/html")
            .with_text(format!(
                "<!DOCTYPE html>\n<html>\n<head><title>404 Not Found</title></head>\n<body>\n\
                <h1>404 Not Found</h1>\n<p>The requested resource '{}' was not found on this server.</p>\n\
                </body>\n</html>",
                path
            )),

        ServerError::Forbidden(reason) => Response::new()
            .with_status(StatusCode::Forbidden)
            .with_content_type("text/html")
            .with_text(format!(
                "<!DOCTYPE html>\n<html>\n<head><title>403 Forbidden</title></head>\n<body>\n\
                <h1>403 Forbidden</h1>\n<p>Access denied: {}</p>\n\
                </body>\n</html>",
                reason
            )),

        ServerError::ServerBusy => Response::new()
            .with_status(StatusCode::ServiceUnavailable)
            .with_content_type("text/html")
            .with_text(
                "<!DOCTYPE html>\n<html>\n<head><title>503 Service Unavailable</title></head>\n<body>\n\
                <h1>503 Service Unavailable</h1>\n<p>The server is currently unable to handle the request due to temporary overloading.</p>\n\
                </body>\n</html>"
            )
            .with_header("Retry-After", "60"), // Suggest client retry after 60 seconds

        ServerError::HttpParse(msg) => Response::new()
            .with_status(StatusCode::BadRequest)
            .with_content_type("text/html")
            .with_text(format!(
                "<!DOCTYPE html>\n<html>\n<head><title>400 Bad Request</title></head>\n<body>\n\
                <h1>400 Bad Request</h1>\n<p>The server could not understand your request: {}</p>\n\
                </body>\n</html>",
                msg
            )),
            
        ServerError::Timeout(msg) => Response::new()
            .with_status(StatusCode::RequestTimeout)
            .with_content_type("text/html")
            .with_text(format!(
                "<!DOCTYPE html>\n<html>\n<head><title>408 Request Timeout</title></head>\n<body>\n\
                <h1>408 Request Timeout</h1>\n<p>The request timed out: {}</p>\n\
                </body>\n</html>",
                msg
            )),

        // Default case for other errors
        _ => {
            // For unexpected errors, log with higher severity
            error!("CRITICAL ERROR: Unhandled server error type: {:?}", error);
            
            Response::new()
                .with_status(StatusCode::InternalServerError)
                .with_content_type("text/html")
                .with_text(
                    "<!DOCTYPE html>\n<html>\n<head><title>500 Internal Server Error</title></head>\n<body>\n\
                    <h1>500 Internal Server Error</h1>\n<p>The server encountered an unexpected condition that prevented it from fulfilling the request.</p>\n\
                    </body>\n</html>"
                )
        }
    }
}
```

#### Design Considerations

Our error-to-response mapping follows several important principles:

1. **Informative But Secure**: Provide enough information to help users, but not expose sensitive server details
2. **Consistent Formatting**: All error pages follow the same HTML structure
3. **Standards Compliance**: Use standard HTTP status codes as defined in RFC 7231
4. **Actionable**: Where applicable, provide hints on how to resolve the issue
5. **Logging Integration**: Log all errors before generating responses for comprehensive error tracking


### 5. Use Logging and Error Handling in Your Server

Update your `main.rs` file to use the new error handling and logging:

```rust
mod config;
mod error;
mod http;
mod logging;
mod server;

use log::{info, error};
use std::process;

fn main() {
    // Initialize logging
    if let Err(e) = logging::init_logger() {
        eprintln!("Failed to initialize logger: {}", e);
        process::exit(1);
    }

    info!("Starting Rusty Server v0.1.0");

    // Load configuration
    let config = match config::Config::load() {
        Ok(config) => config,
        Err(e) => {
            error!("Failed to load configuration: {}", e);
            error!("Using default configuration");
            config::Config::default()
        }
    };

    info!("Server configured to listen on {}", config.address());
    info!("Serving files from {}", config.doc_root);

    // Create and run the server
    let server = server::Server::new(&config);

    if let Err(e) = server.run() {
        error!("Server error: {}", e);
        process::exit(1);
    }
}
```

### 6. Update the Request Handler to Log Requests and Errors

Modify your request handling code to use the new error types and logging:

```rust
pub fn handle_request(&self, mut stream: TcpStream) -> Result<()> {
    // Get peer address for logging
    let peer_addr = stream.peer_addr()
        .map_err(|e| ServerError::Io(e))?;

    debug!("Connection established from: {}", peer_addr);

    // Parse the HTTP request
    let request = match http::Request::from_stream(&mut stream) {
        Ok(req) => req,
        Err(e) => {
            error!("Error parsing request: {}", e);

            // Send a 400 Bad Request response
            let response = http::Response::new()
                .with_status(StatusCode::BadRequest)
                .with_text("400 Bad Request");

            response.write_to(&mut stream)?;

            // Log the failed request
            self.access_logger.log(
                &peer_addr.to_string(),
                "INVALID",
                "",
                400,
                14 // Size of "400 Bad Request"
            );

            return Err(ServerError::HttpParse(e.to_string()));
        }
    };

    debug!("Received {} request for {}", request.method, request.path);

    // Handle the request
    let response = match self.serve_request(&request) {
        Ok(resp) => resp,
        Err(e) => {
            error!("Error serving request: {}", e);
            error_to_response(&e)
        }
    };

    // Log the request and response
    self.access_logger.log(
        &peer_addr.to_string(),
        &request.method.to_string(),
        &request.path,
        response.status.code(),
        response.body.len()
    );

    // Send the response
    response.write_to(&mut stream)?;

    debug!("Response sent to {}", peer_addr);

    Ok(())
}
```

## Advanced Logging Features

### Configuring Log Levels

You can configure different log levels based on modules:

```
RUST_LOG=info,server=debug,http=trace cargo run
```

This sets the default level to `info`, but `server` module to `debug` and `http` module to `trace`.

### Structured Logging

For more advanced scenarios, consider using structured logging crates like `slog` or `tracing`:

```toml
[dependencies]
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
```

Structured logging allows for better filtering and analysis of log data.

## Testing

### Unit Tests for Error Types

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_server_error_display() {
        let error = ServerError::NotFound("/index.html".to_string());
        assert_eq!(error.to_string(), "Not found: /index.html");
    }

    #[test]
    fn test_error_to_response() {
        let error = ServerError::NotFound("/index.html".to_string());
        let response = error_to_response(&error);
        assert_eq!(response.status.code(), 404);
    }
}
```

### Testing Logging

- Check the log output for server events and errors.
- Trigger errors (e.g., request a missing file) and verify logs and responses.
- Use different log levels to ensure proper filtering.

## Tradeoffs

- **Verbose logging** helps debugging but can impact performance. Use log levels wisely.
- **Custom error types** add complexity but improve error handling and response generation.
- **File-based logging** requires file I/O but preserves logs across server restarts.

## Performance and Production Considerations

Logging and error handling can significantly impact server performance and behavior in production environments. Here are important considerations:

### Logging Performance

| Consideration | Impact | Solution |
|--------------|--------|----------|
| **Log I/O** | Synchronous logging blocks request handling | Use async logging or buffered writers |
| **Verbosity** | Excessive logging affects performance and storage | Configure appropriate log levels per environment |
| **Log Growth** | Unchecked log files can fill disk space | Implement log rotation and retention policies |
| **Log Format** | Text formats are readable but inefficient | Consider binary formats for high-volume logs |

### Production Best Practices

1. **Structured Logging**: Use structured formats (like JSON) in production for easier parsing and analysis
2. **Log Aggregation**: Send logs to centralized systems (like ELK stack or Prometheus) 
3. **Error Metrics**: Track error rates and types as metrics for monitoring
4. **Sampling**: In high-volume systems, consider sampling frequent logs rather than logging every occurrence
5. **Sensitive Data**: Ensure logs don't contain passwords, tokens, or other sensitive information

## Summary

In this module, we've built a comprehensive logging and error handling system for our web server. Key accomplishments include:

1. Implementing a flexible logging system with different levels and formatting
2. Creating a custom error type hierarchy that represents all server error conditions
3. Establishing a robust mapping between server errors and HTTP status codes
4. Developing an access logger that records all requests in a standard format
5. Integrating error handling throughout the server to ensure consistent behavior

These improvements transform our server from a basic prototype to a more production-ready system with better observability, reliability, and user experience.

## Knowledge Check

1. **Which log level would be most appropriate for recording that a client connected to the server?**
   - A) ERROR
   - B) WARN
   - C) INFO
   - D) DEBUG

2. **What's the primary advantage of using an enum-based error type instead of Box<dyn Error>?**
   - A) Better performance
   - B) Type safety and exhaustive pattern matching
   - C) Easier integration with external libraries
   - D) Smaller binary size

3. **What HTTP status code should be returned when a requested resource doesn't exist?**
   - A) 400 Bad Request
   - B) 403 Forbidden
   - C) 404 Not Found
   - D) 500 Internal Server Error

4. **What's the purpose of the From<io::Error> implementation for our ServerError type?**
   - A) To print I/O errors as strings
   - B) To convert between error types
   - C) To allow using the ? operator with I/O operations
   - D) To implement the Error trait

5. **Which of these is NOT a concern when implementing logging for a production server?**
   - A) Log rotation
   - B) Performance impact
   - C) Color formatting
   - D) Sensitive data leakage

<details>
<summary>Click to see answers</summary>

1. C) INFO
2. B) Type safety and exhaustive pattern matching
3. C) 404 Not Found
4. C) To allow using the ? operator with I/O operations
5. C) Color formatting
</details>

## Additional Resources

### Logging
- [Rust Log Crate Documentation](https://docs.rs/log/) - Official documentation for the log facade
- [The env_logger Crate](https://docs.rs/env_logger/) - Configurable logging implementation
- [Apache Common Log Format](https://httpd.apache.org/docs/current/logs.html) - Standard for HTTP access logging
- [Advanced Rust Logging with tracing](https://tokio.rs/tokio/topics/tracing) - For more advanced structured logging

### Error Handling
- [Rust Error Handling Best Practices](https://blog.burntsushi.net/rust-error-handling/) - Comprehensive guide
- [HTTP Status Codes](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status) - Complete reference for HTTP errors
- [thiserror Crate](https://docs.rs/thiserror/) - Derive macros for error type implementations
- [eyre Crate](https://docs.rs/eyre/) - Flexible error reporting for production applications

### Books and Articles
- [Error Handling in Rust](https://blog.rust-lang.org/inside-rust/2021/01/21/error-handling-survey.html) - Community survey on error handling
- [Logging in Production](https://www.oreilly.com/library/view/logging-in-action/9781617292958/) - Best practices for production logging
- [Observability Engineering](https://www.oreilly.com/library/view/observability-engineering/9781492076438/) - Modern approach to logs, metrics, and traces

## Next Steps

With logging and error handling in place, our server is more robust, but it still has a critical limitation: it processes requests sequentially. In the next module, we'll implement multithreaded request handling to dramatically improve performance and concurrency.

## Navigation
- [Previous: Configuration System](05-configuration-system.md)
- [Next: Multithreaded Request Handling](07-multithreaded-request-handling.md)
