# Distributed Systems and Microservices

As your application grows, a single webserver may not be sufficient to handle increasing demands. This module explores how to scale your Rust webserver using distributed architectures and microservice patterns.

## Learning Objectives
- Understand distributed systems concepts and challenges
- Implement microservices in Rust with gRPC and RESTful APIs
- Design resilient inter-service communication patterns
- Build service discovery and orchestration for your services
- Implement distributed tracing for complex service interactions

## Prerequisites
- Completion of previous modules on HTTP servers
- Understanding of asynchronous Rust and Tokio
- Basic knowledge of networking and API design

## Distributed Systems Fundamentals

A distributed system is a collection of independent components located on different networked computers that communicate and coordinate to appear as a single coherent system to end users.

### Key Challenges in Distributed Systems

1. **Consistency and Replication**
   - CAP Theorem: Consistency, Availability, Partition Tolerance
   - Eventual vs. Strong Consistency models
   - Data replication strategies

2. **Service Coordination**
   - Leader election
   - Distributed consensus (Raft, Paxos)
   - Service discovery

3. **Failure Handling**
   - Partial failures
   - Network partitions
   - Timeout and retry strategies
   - Circuit breakers

4. **Distributed Data**
   - Sharding strategies
   - Consistent hashing
   - Distributed transactions

## Microservices Architecture

Microservices is an architectural style that structures an application as a collection of loosely coupled services. Each service implements a specific business capability and can be developed, deployed, and scaled independently.

### Advantages
- **Independent Development**: Teams can work autonomously
- **Technology Diversity**: Choose the right tool for each service
- **Resilience**: Failures are isolated to specific services
- **Scalability**: Scale services based on demand

### Challenges
- **Operational Complexity**: More moving parts to manage
- **Network Reliability**: Services communicate over the network
- **Service Discovery**: Finding service instances dynamically
- **Data Consistency**: Managing data across service boundaries

## Communication Patterns

### Synchronous Communication

#### REST API Implementation in Rust

Using `warp` for a RESTful service:

```rust
use warp::{Filter, Reply};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Clone, Debug, Deserialize, Serialize)]
struct Product {
    id: u64,
    name: String,
    price: f64,
}

type Products = Arc<RwLock<Vec<Product>>>;

async fn create_product_service() -> impl Filter<Extract = impl Reply, Error = warp::Rejection> + Clone {
    // Shared state
    let products: Products = Arc::new(RwLock::new(vec![]));
    
    // GET /products
    let products_list = {
        let products = products.clone();
        warp::path("products")
            .and(warp::get())
            .and(warp::any().map(move || products.clone()))
            .and_then(get_products)
    };
    
    // POST /products
    let products_create = {
        let products = products.clone();
        warp::path("products")
            .and(warp::post())
            .and(warp::body::json())
            .and(warp::any().map(move || products.clone()))
            .and_then(create_product)
    };
    
    // GET /products/{id}
    let product_get = {
        let products = products.clone();
        warp::path!("products" / u64)
            .and(warp::get())
            .and(warp::any().map(move || products.clone()))
            .and_then(get_product)
    };
    
    // Combine routes
    products_list
        .or(products_create)
        .or(product_get)
        .with(warp::cors().allow_any_origin())
}

async fn get_products(products: Products) -> Result<impl Reply, warp::Rejection> {
    let products = products.read().await.clone();
    Ok(warp::reply::json(&products))
}

async fn create_product(new_product: Product, products: Products) -> Result<impl Reply, warp::Rejection> {
    let mut products_lock = products.write().await;
    products_lock.push(new_product.clone());
    Ok(warp::reply::json(&new_product))
}

async fn get_product(id: u64, products: Products) -> Result<impl Reply, warp::Rejection> {
    let products = products.read().await;
    let product = products.iter().find(|p| p.id == id);
    
    match product {
        Some(product) => Ok(warp::reply::json(&product)),
        None => Err(warp::reject::not_found())
    }
}

#[tokio::main]
async fn main() {
    // Create our API service
    let api = create_product_service().await;
    
    // Start the server
    println!("Starting product microservice on http://localhost:3030");
    warp::serve(api).run(([127, 0, 0, 1], 3030)).await;
}
```

#### gRPC Service Implementation in Rust

Using `tonic` for gRPC:

```rust
use tonic::{transport::Server, Request, Response, Status};

// Import generated code from protobuf
pub mod product {
    tonic::include_proto!("product");
}

use product::{
    product_service_server::{ProductService, ProductServiceServer},
    CreateProductRequest, CreateProductResponse,
    GetProductRequest, GetProductResponse,
    ListProductsRequest, ListProductsResponse,
    Product,
};

use std::sync::Arc;
use tokio::sync::RwLock;

// Define service implementation
#[derive(Debug)]
struct ProductServiceImpl {
    products: Arc<RwLock<Vec<Product>>>,
}

#[tonic::async_trait]
impl ProductService for ProductServiceImpl {
    async fn create_product(
        &self,
        request: Request<CreateProductRequest>,
    ) -> Result<Response<CreateProductResponse>, Status> {
        let new_product = request.into_inner().product;
        
        if new_product.is_none() {
            return Err(Status::invalid_argument("Product data is required"));
        }
        
        let new_product = new_product.unwrap();
        
        let mut products = self.products.write().await;
        products.push(new_product.clone());
        
        Ok(Response::new(CreateProductResponse {
            product: Some(new_product),
        }))
    }
    
    async fn get_product(
        &self,
        request: Request<GetProductRequest>,
    ) -> Result<Response<GetProductResponse>, Status> {
        let id = request.into_inner().id;
        let products = self.products.read().await;
        
        for product in products.iter() {
            if product.id == id {
                return Ok(Response::new(GetProductResponse {
                    product: Some(product.clone()),
                }));
            }
        }
        
        Err(Status::not_found(format!("Product with ID {} not found", id)))
    }
    
    async fn list_products(
        &self,
        _request: Request<ListProductsRequest>,
    ) -> Result<Response<ListProductsResponse>, Status> {
        let products = self.products.read().await;
        
        Ok(Response::new(ListProductsResponse {
            products: products.clone(),
        }))
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let addr = "[::1]:50051".parse()?;
    
    let product_service = ProductServiceImpl {
        products: Arc::new(RwLock::new(Vec::new())),
    };
    
    println!("Starting gRPC product service on {}", addr);
    
    Server::builder()
        .add_service(ProductServiceServer::new(product_service))
        .serve(addr)
        .await?;
    
    Ok(())
}
```

### Asynchronous Communication

For loosely coupled services, asynchronous messaging provides greater resilience:

```rust
use lapin::{
    options::*, types::FieldTable, Connection,
    ConnectionProperties, Result, message::DeliveryResult,
};
use futures::StreamExt;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
struct OrderCreatedEvent {
    order_id: String,
    product_id: String,
    quantity: i32,
    customer_id: String,
}

async fn publish_order_event(event: OrderCreatedEvent) -> Result<()> {
    let uri = "amqp://guest:guest@localhost:5672/%2f";
    let connection = Connection::connect(uri, ConnectionProperties::default()).await?;
    let channel = connection.create_channel().await?;
    
    // Declare the exchange
    channel
        .exchange_declare(
            "orders",
            lapin::ExchangeKind::Topic,
            ExchangeDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        )
        .await?;
    
    // Serialize event to JSON
    let payload = serde_json::to_vec(&event)?;
    
    // Publish message
    channel
        .basic_publish(
            "orders",
            "orders.created",
            BasicPublishOptions::default(),
            &payload,
            BasicProperties::default()
                .with_delivery_mode(2) // persistent
                .with_content_type("application/json".into()),
        )
        .await?;
    
    Ok(())
}

async fn consume_order_events() -> Result<()> {
    let uri = "amqp://guest:guest@localhost:5672/%2f";
    let connection = Connection::connect(uri, ConnectionProperties::default()).await?;
    let channel = connection.create_channel().await?;
    
    // Declare the exchange
    channel
        .exchange_declare(
            "orders",
            lapin::ExchangeKind::Topic,
            ExchangeDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        )
        .await?;
    
    // Declare a queue for this service
    let queue = channel
        .queue_declare(
            "inventory-service",
            QueueDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        )
        .await?;
    
    // Bind the queue to the exchange with a routing key
    channel
        .queue_bind(
            "inventory-service",
            "orders",
            "orders.created",
            QueueBindOptions::default(),
            FieldTable::default(),
        )
        .await?;
    
    // Start consuming messages
    let mut consumer = channel
        .basic_consume(
            "inventory-service",
            "inventory-consumer",
            BasicConsumeOptions::default(),
            FieldTable::default(),
        )
        .await?;
    
    println!("Inventory service waiting for messages");
    
    while let Some(delivery) = consumer.next().await {
        if let Ok(delivery) = delivery {
            // Parse message
            if let Ok(order_event) = serde_json::from_slice::<OrderCreatedEvent>(&delivery.data) {
                println!("Processing order {} for product {}", 
                         order_event.order_id, order_event.product_id);
                
                // Process the order
                // Update inventory, etc.
                
                // Acknowledge the message
                delivery
                    .ack(BasicAckOptions::default())
                    .await
                    .expect("Failed to acknowledge message");
            }
        }
    }
    
    Ok(())
}
```

## Service Discovery and Registration

For services to find each other dynamically, we need service discovery:

### Using Consul for Service Discovery

```rust
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use hyper::{Body, Client, Method, Request, Uri};
use serde::{Deserialize, Serialize};
use serde_json::json;

#[derive(Serialize, Deserialize, Debug)]
struct ServiceRegistration {
    name: String,
    id: String,
    address: String,
    port: u16,
    tags: Vec<String>,
    checks: Vec<HealthCheck>,
}

#[derive(Serialize, Deserialize, Debug)]
struct HealthCheck {
    name: String,
    http: String,
    interval: String,
    timeout: String,
}

async fn register_with_consul(
    service_name: &str,
    service_id: &str,
    address: IpAddr,
    port: u16,
) -> Result<(), Box<dyn std::error::Error>> {
    let client = Client::new();
    
    // Health check endpoint that the service should expose
    let health_url = format!("http://{}:{}/health", address, port);
    
    let registration = ServiceRegistration {
        name: service_name.to_string(),
        id: service_id.to_string(),
        address: address.to_string(),
        port,
        tags: vec!["rust".to_string(), "microservice".to_string()],
        checks: vec![HealthCheck {
            name: format!("{} health check", service_name),
            http: health_url,
            interval: "15s".to_string(),
            timeout: "5s".to_string(),
        }],
    };
    
    let body = serde_json::to_string(&registration)?;
    let consul_url = "http://localhost:8500/v1/agent/service/register";
    
    let req = Request::builder()
        .method(Method::PUT)
        .uri(consul_url)
        .header("Content-Type", "application/json")
        .body(Body::from(body))?;
    
    let resp = client.request(req).await?;
    
    if resp.status().is_success() {
        println!("Successfully registered service {} with Consul", service_name);
        Ok(())
    } else {
        Err(format!("Failed to register with Consul: {:?}", resp).into())
    }
}

async fn discover_service(service_name: &str) -> Result<Vec<SocketAddr>, Box<dyn std::error::Error>> {
    let client = Client::new();
    let consul_url = format!("http://localhost:8500/v1/catalog/service/{}", service_name);
    
    let req = Request::builder()
        .method(Method::GET)
        .uri(consul_url)
        .header("Content-Type", "application/json")
        .body(Body::empty())?;
    
    let resp = client.request(req).await?;
    
    if resp.status().is_success() {
        let body = hyper::body::to_bytes(resp.into_body()).await?;
        let services: Vec<serde_json::Value> = serde_json::from_slice(&body)?;
        
        let mut addresses = Vec::new();
        for service in services {
            if let (Some(address), Some(port)) = (
                service.get("ServiceAddress").and_then(|a| a.as_str()),
                service.get("ServicePort").and_then(|p| p.as_u64()),
            ) {
                let ip: IpAddr = address.parse()?;
                addresses.push(SocketAddr::new(ip, port as u16));
            }
        }
        
        Ok(addresses)
    } else {
        Err(format!("Failed to discover service: {:?}", resp).into())
    }
}
```

## Distributed Tracing

To understand how requests flow through microservices, implement distributed tracing:

```rust
use opentelemetry::sdk::{trace, Resource};
use opentelemetry::trace::{TraceError, Tracer};
use opentelemetry::{global, KeyValue};
use opentelemetry_jaeger::new_pipeline;
use tracing_subscriber::layer::SubscriberExt;
use tracing_subscriber::Registry;

fn init_tracer() -> Result<impl Tracer, TraceError> {
    global::set_text_map_propagator(opentelemetry_jaeger::Propagator::new());
    
    // Set up the Jaeger exporter pipeline
    let tracer = opentelemetry_jaeger::new_pipeline()
        .with_service_name("product-service")
        .with_resource(Resource::new(vec![KeyValue::new("service.version", "0.1.0")]))
        .install_batch(opentelemetry::runtime::Tokio)?;
    
    // Set up the tracing subscriber
    let telemetry = tracing_opentelemetry::layer().with_tracer(tracer.clone());
    let subscriber = Registry::default().with(telemetry);
    
    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set tracing subscriber");
    
    Ok(tracer)
}

// Using tracing in endpoint handlers
async fn handle_request(req: Request<Body>) -> Result<Response<Body>, Infallible> {
    let span = tracing::info_span!("handle_request", 
        method = %req.method(), 
        uri = %req.uri(),
    );
    
    let _guard = span.enter();
    
    tracing::info!("Processing incoming request");
    
    // Extract trace context from request headers
    let parent_cx = global::get_text_map_propagator(|prop| {
        let mut extractor = HeaderExtractor::new(req.headers());
        prop.extract(&mut extractor)
    });
    
    // Attach the trace context to subsequent spans
    let child_span = tracing::info_span!(parent: &parent_cx, "database_query");
    let _guard = child_span.enter();
    
    // Execute request logic
    tracing::info!("Querying database");
    
    // Make downstream service call with propagation
    let downstream_response = call_downstream_service(&parent_cx).await;
    
    tracing::info!("Request processing complete");
    
    Ok(Response::new(Body::from("Success")))
}

async fn call_downstream_service(context: &Context) -> Result<String, Error> {
    let client = Client::new();
    
    // Create a request to the downstream service
    let mut req = Request::builder()
        .method(Method::GET)
        .uri("http://localhost:8080/api/downstream")
        .body(Body::empty())?;
    
    // Inject the current trace context into the outgoing request headers
    global::get_text_map_propagator(|propagator| {
        propagator.inject_context(
            context,
            &mut HeaderInjector(req.headers_mut()),
        )
    });
    
    // Make the request to the downstream service
    let resp = client.request(req).await?;
    
    // Process response
    let body = hyper::body::to_bytes(resp.into_body()).await?;
    Ok(String::from_utf8(body.to_vec())?)
}
```

## Circuit Breaking and Retries

Handle service failures gracefully with circuit breakers:

```rust
use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};

enum CircuitState {
    Closed,
    Open {
        since: Instant,
        reset_timeout: Duration,
    },
    HalfOpen,
}

struct CircuitBreaker {
    state: RwLock<CircuitState>,
    failure_threshold: u32,
    failure_count: Mutex<u32>,
    reset_timeout: Duration,
}

impl CircuitBreaker {
    pub fn new(failure_threshold: u32, reset_timeout: Duration) -> Self {
        CircuitBreaker {
            state: RwLock::new(CircuitState::Closed),
            failure_threshold,
            failure_count: Mutex::new(0),
            reset_timeout,
        }
    }
    
    pub async fn execute<F, T, E>(&self, f: F) -> Result<T, E>
    where
        F: FnOnce() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, E>> + Send>>,
        E: std::error::Error + Send + 'static,
    {
        match *self.state.read().await {
            CircuitState::Open { since, reset_timeout } => {
                if since.elapsed() >= reset_timeout {
                    // Transition to half-open
                    *self.state.write().await = CircuitState::HalfOpen;
                    self.try_execute(f).await
                } else {
                    Err(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        "Circuit is open",
                    ))?
                }
            }
            CircuitState::HalfOpen => self.try_execute(f).await,
            CircuitState::Closed => self.try_execute(f).await,
        }
    }
    
    async fn try_execute<F, T, E>(&self, f: F) -> Result<T, E>
    where
        F: FnOnce() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, E>> + Send>>,
        E: std::error::Error + Send + 'static,
    {
        match f().await {
            Ok(result) => {
                // On success, reset failure count and transition to Closed if in HalfOpen
                if matches!(*self.state.read().await, CircuitState::HalfOpen) {
                    *self.state.write().await = CircuitState::Closed;
                }
                *self.failure_count.lock().await = 0;
                Ok(result)
            }
            Err(err) => {
                // Increment failure count
                let mut count = self.failure_count.lock().await;
                *count += 1;
                
                // Check if we need to open the circuit
                if *count >= self.failure_threshold {
                    *self.state.write().await = CircuitState::Open {
                        since: Instant::now(),
                        reset_timeout: self.reset_timeout,
                    };
                    *count = 0;
                }
                
                Err(err)
            }
        }
    }
}

// Using the circuit breaker with a service call
async fn call_service_with_circuit_breaker() -> Result<String, Box<dyn std::error::Error>> {
    let circuit_breaker = Arc::new(CircuitBreaker::new(
        5,           // 5 failures will trip the circuit
        Duration::from_secs(30), // 30 seconds open circuit before trying again
    ));
    
    // Execute service call through circuit breaker
    circuit_breaker
        .execute(|| {
            Box::pin(async {
                // Service call here
                let client = reqwest::Client::new();
                Ok(client.get("https://api.example.com/data")
                   .send()
                   .await?
                   .text()
                   .await?)
            })
        })
        .await
}
```

## Building a Complete Microservice System

### 1. Define Service Boundaries

Identify bounded contexts according to domain-driven design:

- **Product Catalog Service**: Manages product information
- **Inventory Service**: Tracks product stock levels
- **Order Service**: Handles customer orders
- **Payment Service**: Processes payments
- **User Service**: Manages user accounts and authentication

### 2. Design Inter-Service Communication

```rust
// src/api_gateway/main.rs
use actix_web::{web, App, HttpServer, Responder, HttpResponse};
use actix_web::client::Client;
use futures::future::join_all;

// Product aggregation
async fn get_product_with_inventory(
    client: web::Data<Client>, 
    product_id: web::Path<String>
) -> impl Responder {
    // Parallel requests to product and inventory services
    let product_future = client
        .get(&format!("http://product-service/products/{}", product_id))
        .send();
        
    let inventory_future = client
        .get(&format!("http://inventory-service/inventory/{}", product_id))
        .send();
    
    // Wait for both requests to complete
    let (product_response, inventory_response) = join(
        product_future, 
        inventory_future
    ).await;
    
    // Process responses and combine data
    match (product_response, inventory_response) {
        (Ok(product_resp), Ok(inv_resp)) if product_resp.status().is_success() && inv_resp.status().is_success() => {
            let product_json = product_resp.json::<serde_json::Value>().await.unwrap_or_default();
            let inventory_json = inv_resp.json::<serde_json::Value>().await.unwrap_or_default();
            
            // Combine the results
            let mut combined = product_json;
            if let Some(obj) = combined.as_object_mut() {
                if let Some(inv) = inventory_json.get("stock_level") {
                    obj.insert("stock_level".to_string(), inv.clone());
                }
            }
            
            HttpResponse::Ok().json(combined)
        },
        _ => HttpResponse::InternalServerError().body("Failed to retrieve product information"),
    }
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Create HTTP client
    let client = Client::default();
    let client_data = web::Data::new(client);
    
    HttpServer::new(move || {
        App::new()
            .app_data(client_data.clone())
            .route("/products/{id}", web::get().to(get_product_with_inventory))
            // Additional routes...
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
```

### 3. Deployment with Docker and Kubernetes

```yaml
# docker-compose.yml for local development
version: '3.8'

services:
  product-service:
    build: ./product-service
    ports:
      - "3001:3000"
    environment:
      - DATABASE_URL=******************************************/products
      - MESSAGE_QUEUE=amqp://guest:guest@rabbitmq:5672
    depends_on:
      - postgres
      - rabbitmq
      
  inventory-service:
    build: ./inventory-service
    ports:
      - "3002:3000"
    environment:
      - DATABASE_URL=******************************************/inventory
      - MESSAGE_QUEUE=amqp://guest:guest@rabbitmq:5672
    depends_on:
      - postgres
      - rabbitmq
      
  order-service:
    build: ./order-service
    ports:
      - "3003:3000"
    environment:
      - DATABASE_URL=******************************************/orders
      - MESSAGE_QUEUE=amqp://guest:guest@rabbitmq:5672
    depends_on:
      - postgres
      - rabbitmq
      
  api-gateway:
    build: ./api-gateway
    ports:
      - "8080:8080"
    depends_on:
      - product-service
      - inventory-service
      - order-service
      
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "15672:15672"  # Management UI
      - "5672:5672"    # AMQP port
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
      
  jaeger:
    image: jaegertracing/all-in-one:1.40
    ports:
      - "16686:16686"  # UI
      - "4317:4317"    # OTLP gRPC
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      
volumes:
  postgres-data:
  rabbitmq-data:
```

## Best Practices for Distributed Systems

1. **Design for Failure**
   - Assume that any component can fail
   - Use timeouts, retries, and circuit breakers
   - Fail gracefully and provide defaults when services are unavailable

2. **Implement Observability**
   - Distributed tracing across service boundaries
   - Centralized logging with correlation IDs
   - Metrics for system health and performance

3. **Ensure Data Consistency**
   - Use event sourcing or saga patterns for multi-service transactions
   - Consider eventual consistency where appropriate
   - Implement idempotent APIs to handle duplicate requests

4. **Security Considerations**
   - Implement service-to-service authentication
   - Use mTLS for secure communication
   - Apply the principle of least privilege for service identities

5. **Performance Optimization**
   - Use connection pooling for database and HTTP clients
   - Implement caching at appropriate layers
   - Consider binary protocols like gRPC for inter-service communication

## Knowledge Check

1. **What is the CAP theorem, and what trade-offs does it describe?**
   - The CAP theorem states that in a distributed system, it's impossible to simultaneously guarantee Consistency, Availability, and Partition tolerance—you can only have two of the three.

2. **What are the advantages of using asynchronous messaging between services compared to synchronous REST calls?**
   - Loose coupling between services, better resilience to service failures, and the ability to handle traffic spikes through message queuing.

3. **What is a circuit breaker pattern and why is it important in microservices?**
   - A circuit breaker prevents cascading failures by stopping calls to a failing service, allowing it time to recover, and failing fast when the service is unavailable.

4. **How does distributed tracing help with debugging issues in microservices?**
   - It tracks requests as they propagate through multiple services, providing visibility into the entire request path, latencies, and helping identify bottlenecks.

5. **What is service discovery and why is it necessary in a dynamic microservice environment?**
   - Service discovery allows services to find and communicate with each other without hardcoded addresses, which is essential in environments where service instances can change dynamically.

## Additional Resources

- [Microservices.io Patterns](https://microservices.io/patterns/index.html)
- [The Twelve-Factor App](https://12factor.net/)
- [Building Microservices](https://samnewman.io/books/building_microservices/) by Sam Newman
- [Designing Data-Intensive Applications](https://dataintensive.net/) by Martin Kleppmann

## Next Steps

In the [next module](40-cicd-automated-deployment.md), we'll explore CI/CD and automated deployment for your distributed Rust webserver.

[Previous: Advanced Testing and Fuzzing](38-advanced-testing-fuzzing.md) | [Next: CI/CD and Automated Deployment](40-cicd-automated-deployment.md)
