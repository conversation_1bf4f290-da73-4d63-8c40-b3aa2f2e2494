# Monitoring, Metrics, and Health Checks

## Learning Objectives
- Understand the importance of monitoring and observability in web services
- Implement metrics collection and reporting in a Rust webserver
- Create comprehensive health check systems for service reliability
- Build dashboards and alerting based on collected metrics
- Design proper operational readiness indicators for your web application

## Prerequisites
- Understanding of HTTP routing and middleware concepts
- Basic knowledge of async Rust and web frameworks
- Familiarity with webserver architecture components

## Introduction

Monitoring, metrics collection, and health checks are critical aspects of running production web services. They provide visibility into your application's performance, help detect issues before they become critical failures, and ensure your system is operating correctly. This module covers implementing these systems in a Rust webserver, from basic health endpoints to comprehensive metrics collection and monitoring integration.

## Monitoring Fundamentals

### Key Concepts

- **Metrics**: Measurable values that indicate system performance and behavior
- **Health Checks**: Endpoints that report on service availability and status
- **Observability**: The ability to understand a system's internal state through external outputs
- **Cardinality**: The number of unique combinations of labels for metrics
- **SLI, SLO, SLA**: Service Level Indicators, Objectives, and Agreements

### Types of Metrics

1. **Counter**: A cumulative metric that only increases (e.g., total requests)
2. **Gauge**: A metric that can increase and decrease (e.g., active connections)
3. **Histogram**: Samples observations and counts them in configurable buckets (e.g., request duration)
4. **Summary**: Similar to histograms, but calculates percentiles over time windows

## Metrics Collection in Rust

### Using Prometheus for Metrics

[Prometheus](https://prometheus.io/) is a popular open-source monitoring solution that works well with Rust. Let's implement metrics collection using the `prometheus` crate:

```rust
use axum::{
    routing::get,
    Router,
    http::StatusCode,
    response::IntoResponse,
};
use prometheus::{
    Counter, Encoder, Gauge, Histogram, HistogramOpts, 
    IntCounter, IntCounterVec, Registry, TextEncoder
};
use std::sync::Arc;
use tokio::sync::Mutex;

// Create a metrics registry
pub struct Metrics {
    registry: Registry,
    request_counter: IntCounterVec,
    response_time: Histogram,
    active_connections: Gauge,
}

impl Metrics {
    pub fn new() -> Self {
        let registry = Registry::new();
        
        // Counter for requests by path and method
        let request_counter = IntCounterVec::new(
            prometheus::opts!("http_requests_total", "Total HTTP requests"),
            &["method", "path", "status"],
        ).unwrap();
        
        // Histogram for response time
        let response_time = Histogram::with_opts(
            HistogramOpts::new("http_request_duration_seconds", "HTTP request duration")
                .buckets(vec![0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]),
        ).unwrap();
        
        // Gauge for active connections
        let active_connections = Gauge::new(
            "http_active_connections", "Current active HTTP connections"
        ).unwrap();
        
        // Register metrics with the registry
        registry.register(Box::new(request_counter.clone())).unwrap();
        registry.register(Box::new(response_time.clone())).unwrap();
        registry.register(Box::new(active_connections.clone())).unwrap();
        
        Self {
            registry,
            request_counter,
            response_time,
            active_connections,
        }
    }
    
    // Method to expose metrics in Prometheus format
    pub fn metrics_handler(&self) -> String {
        let encoder = TextEncoder::new();
        let mut buffer = Vec::new();
        
        let metric_families = self.registry.gather();
        encoder.encode(&metric_families, &mut buffer).unwrap();
        
        String::from_utf8(buffer).unwrap()
    }
}

// Shared application state with metrics
pub struct AppState {
    metrics: Arc<Metrics>,
}

#[tokio::main]
async fn main() {
    // Initialize metrics
    let metrics = Arc::new(Metrics::new());
    
    // Create shared state
    let state = AppState {
        metrics: metrics.clone(),
    };
    
    // Create router with metrics endpoint
    let app = Router::new()
        .route("/metrics", get(metrics_handler))
        .route("/api/users", get(get_users))
        .with_state(Arc::new(state));
        
    // Start server
    axum::Server::bind(&"0.0.0.0:3000".parse().unwrap())
        .serve(app.into_make_service())
        .await
        .unwrap();
}

// Handler for metrics endpoint
async fn metrics_handler(
    state: axum::extract::State<Arc<AppState>>,
) -> impl IntoResponse {
    let metrics_text = state.metrics.metrics_handler();
    (StatusCode::OK, metrics_text)
}

// Example API endpoint with metrics
async fn get_users(
    state: axum::extract::State<Arc<AppState>>,
) -> impl IntoResponse {
    // Increment the request counter
    state.metrics.request_counter
        .with_label_values(&["GET", "/api/users", "200"])
        .inc();
    
    // Track active connection
    state.metrics.active_connections.inc();
    
    // Record response time
    let timer = std::time::Instant::now();
    
    // Simulate some work
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    // Record response time duration
    let duration = timer.elapsed().as_secs_f64();
    state.metrics.response_time.observe(duration);
    
    // Decrement active connection counter
    state.metrics.active_connections.dec();
    
    (StatusCode::OK, "Users data would go here")
}
```

### Middleware-Based Metrics Collection

A more elegant approach is to use middleware to automatically collect metrics for all routes:

```rust
use std::time::Instant;
use axum::{
    extract::MatchedPath, 
    http::{Request, StatusCode},
    middleware::Next,
    response::Response,
};
use tower_http::trace::TraceLayer;

// Metrics middleware
async fn metrics_middleware<B>(
    state: axum::extract::State<Arc<AppState>>,
    req: Request<B>,
    next: Next<B>,
) -> Response {
    // Extract path from request for metrics labeling
    let path = if let Some(matched_path) = req.extensions().get::<MatchedPath>() {
        matched_path.as_str().to_owned()
    } else {
        req.uri().path().to_owned()
    };
    
    let method = req.method().as_str().to_owned();
    
    // Increment active connections
    state.metrics.active_connections.inc();
    
    // Start timer for request duration
    let start = Instant::now();
    
    // Process the request
    let response = next.run(req).await;
    
    // Record request duration
    let duration = start.elapsed().as_secs_f64();
    state.metrics.response_time.observe(duration);
    
    // Record status code metrics
    let status = response.status().as_u16().to_string();
    state.metrics.request_counter
        .with_label_values(&[&method, &path, &status])
        .inc();
    
    // Decrement active connections
    state.metrics.active_connections.dec();
    
    response
}

// Usage in router setup
let app = Router::new()
    .route("/metrics", get(metrics_handler))
    .route("/api/users", get(get_users))
    .layer(axum::middleware::from_fn_with_state(
        state.clone(),
        metrics_middleware
    ))
    .with_state(Arc::new(state));
```

## Implementing Health Checks

Health checks are endpoints that verify your application is functioning correctly. They range from simple uptime checks to deep health verification.

### Basic Health Check

A basic health check endpoint simply returns a 200 status:

```rust
async fn health_check() -> impl IntoResponse {
    StatusCode::OK
}

// Register in router
let app = Router::new()
    .route("/health", get(health_check))
    // Other routes
```

### Comprehensive Health Checks

More sophisticated health checks verify dependencies and system components:

```rust
use serde::Serialize;
use sqlx::PgPool;

#[derive(Serialize)]
struct HealthStatus {
    status: String,
    version: String,
    components: Vec<ComponentStatus>,
}

#[derive(Serialize)]
struct ComponentStatus {
    name: String,
    status: String,
    details: Option<String>,
}

#[derive(Clone)]
struct AppServices {
    db_pool: PgPool,
    redis_client: redis::Client,
}

async fn advanced_health_check(
    State(services): State<Arc<AppServices>>,
) -> impl IntoResponse {
    let mut health = HealthStatus {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        components: Vec::new(),
    };
    
    // Check database
    let db_status = match services.db_pool.acquire().await {
        Ok(_) => ComponentStatus {
            name: "database".to_string(),
            status: "up".to_string(),
            details: None,
        },
        Err(e) => {
            // Mark overall status as degraded
            health.status = "degraded".to_string();
            
            ComponentStatus {
                name: "database".to_string(),
                status: "down".to_string(),
                details: Some(e.to_string()),
            }
        }
    };
    health.components.push(db_status);
    
    // Check Redis
    let redis_status = match services.redis_client.get_connection() {
        Ok(_) => ComponentStatus {
            name: "redis".to_string(),
            status: "up".to_string(),
            details: None,
        },
        Err(e) => {
            // Mark overall status as degraded
            health.status = "degraded".to_string();
            
            ComponentStatus {
                name: "redis".to_string(),
                status: "down".to_string(),
                details: Some(e.to_string()),
            }
        }
    };
    health.components.push(redis_status);
    
    // Return overall health status with appropriate HTTP code
    let status_code = if health.status == "healthy" {
        StatusCode::OK
    } else {
        StatusCode::SERVICE_UNAVAILABLE
    };
    
    (status_code, axum::Json(health))
}
```

### Readiness vs Liveness

For containerized applications, it's important to distinguish between different health endpoints:

1. **Readiness**: Indicates the application is ready to receive traffic
2. **Liveness**: Indicates the application is running and not deadlocked

```rust
// Readiness check - more comprehensive
async fn readiness_check(
    State(services): State<Arc<AppServices>>,
) -> impl IntoResponse {
    // Check database, cache, etc.
    // Return 200 only if everything is ready
    // ...
}

// Liveness check - simpler
async fn liveness_check() -> impl IntoResponse {
    // Just verify process is running and responding
    StatusCode::OK
}

// Register in router
let app = Router::new()
    .route("/health/live", get(liveness_check))
    .route("/health/ready", get(readiness_check))
    // Other routes
```

## Integration with Monitoring Systems

### Prometheus Integration

Configure Prometheus to scrape metrics from your Rust webserver:

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'rust_webserver'
    scrape_interval: 15s
    static_configs:
      - targets: ['localhost:3000']
```

### Grafana Dashboard

Create a Grafana dashboard to visualize your application metrics:

```rust
// Example of a Grafana dashboard JSON model (simplified)
{
  "title": "Rust Webserver Dashboard",
  "panels": [
    {
      "title": "Request Rate",
      "type": "graph",
      "gridPos": { "x": 0, "y": 0, "w": 12, "h": 8 },
      "targets": [
        {
          "expr": "rate(http_requests_total[1m])",
          "legendFormat": "{{method}} {{path}}"
        }
      ]
    },
    {
      "title": "Response Time (95th Percentile)",
      "type": "graph", 
      "gridPos": { "x": 12, "y": 0, "w": 12, "h": 8 },
      "targets": [
        {
          "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
          "legendFormat": "95th Percentile"
        }
      ]
    },
    {
      "title": "Active Connections",
      "type": "gauge",
      "gridPos": { "x": 0, "y": 8, "w": 8, "h": 8 },
      "targets": [
        {
          "expr": "http_active_connections",
          "legendFormat": "Connections"
        }
      ],
      "options": {
        "maxValue": 100
      }
    }
  ]
}
```

## Best Practices

### Metric Naming and Organization

- Use a consistent naming scheme (e.g., `namespace_subsystem_metric_name`)
- Follow the Prometheus naming conventions when using Prometheus
- Keep cardinality under control (avoid high-cardinality labels)
- Document metrics for operational use

```rust
// Good metric naming
let requests_total = Counter::new(
    "http_requests_total", 
    "Total number of HTTP requests"
).unwrap();

// Bad - inconsistent naming
let req_count = Counter::new(
    "ReqCount", 
    "Counts reqs"
).unwrap();
```

### Health Check Best Practices

- Provide different detail levels for different consumers
- Include version information for debugging
- Avoid heavy operations in frequently called health checks
- Implement caching for expensive health checks

```rust
#[derive(Serialize)]
struct HealthResponse {
    status: String,
    version: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    details: Option<DetailedHealth>,
}

#[derive(Serialize)]
struct DetailedHealth {
    components: HashMap<String, ComponentStatus>,
    uptime: u64,
    memory_usage_mb: u64,
}

// Health check with optional detail level
async fn health_check(
    detailed: Option<axum::extract::Query<DetailedQuery>>,
) -> impl IntoResponse {
    let base_health = HealthResponse {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        details: None,
    };
    
    if detailed.map_or(false, |q| q.detailed) {
        // Generate detailed health info
        // This is more expensive
        // ...
    }
    
    // Return appropriately detailed response
    (StatusCode::OK, axum::Json(base_health))
}
```

### Alerting Strategy

- Focus on actionable alerts
- Implement rate-based alerts (e.g., error rate > 5%)
- Monitor trends, not just absolute thresholds
- Set up alert severity levels

## Knowledge Check

1. What is the difference between a Counter and a Gauge in metrics collection?
2. How would you implement a middleware to collect timing metrics for all endpoints?
3. What are the key components that should be included in a comprehensive health check?
4. How can you prevent high cardinality issues when collecting metrics?
5. What is the difference between readiness and liveness in health checks?
6. How would you integrate your Rust webserver metrics with Prometheus?

## Additional Resources

- [Prometheus Documentation](https://prometheus.io/docs/introduction/overview/)
- [Grafana Dashboarding Guide](https://grafana.com/docs/grafana/latest/dashboards/)
- [Google SRE Book: Monitoring Distributed Systems](https://sre.google/sre-book/monitoring-distributed-systems/)
- [Rust prometheus Crate Documentation](https://docs.rs/prometheus/latest/prometheus/)

## Diagram: Monitoring Architecture

```mermaid
graph TD
    A[Rust Webserver] -- exposes /metrics --> B[Prometheus]
    A -- exposes /health --> C[Kubernetes/Load Balancer]
    B -- collects --> D[Metrics Database]
    D -- visualizes --> E[Grafana Dashboards]
    D -- triggers --> F[Alerting System]
    F -- sends --> G[Alerts to Ops Team]
    C -- reports --> H[Service Status]
```
