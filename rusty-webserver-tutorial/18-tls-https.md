# TLS/HTTPS with Rust

## Navigation
- [Previous: WebSockets & Real-Time](17-websockets-realtime.md)
- [Next: Session Management](19-session-management.md)

## Table of Contents
- [Introduction](#introduction)
- [Theory](#theory)
- [TLS Implementation](#rust-example-using-rustls)
- [Best Practices](#best-practices)
- [Security Considerations](#security-considerations)
- [Integration](#integration)
- [Quiz](#quiz)

## Introduction

Securing your webserver with HTTPS is essential for protecting data in transit.

## Modern TLS Theory

### TLS 1.3 Advantages
- **Faster Handshake**: Reduced round trips (1-RTT vs 2-RTT in TLS 1.2)
- **Forward Secrecy**: All cipher suites provide perfect forward secrecy
- **Simplified Cipher Suites**: Removed legacy and weak cryptographic algorithms
- **0-RTT Resumption**: Even faster reconnections for returning clients
- **Enhanced Security**: Encrypted handshake messages prevent downgrade attacks

### Certificate Management
- **Modern Certificate Types**: ECDSA certificates for better performance
- **Certificate Transparency**: Automatic CT log submission for security
- **ACME Protocol**: Automated certificate provisioning with Let's Encrypt
- **Certificate Pinning**: Enhanced security for high-value applications

## Rust Implementation (using `rustls` with TLS 1.3)
```rust
use rustls::{ServerConfig, ProtocolVersion};
use rustls_pemfile::{certs, pkcs8_private_keys};
use std::sync::Arc;
use std::fs::File;
use std::io::BufReader;

// Load certificates and private key
let cert_file = File::open("cert.pem")?;
let key_file = File::open("key.pem")?;

let cert_chain = certs(&mut BufReader::new(cert_file))?
    .into_iter()
    .map(rustls::Certificate)
    .collect();

let mut keys = pkcs8_private_keys(&mut BufReader::new(key_file))?;
let private_key = rustls::PrivateKey(keys.remove(0));

// Configure for TLS 1.3 with modern security
let config = Arc::new(
    ServerConfig::builder()
        .with_cipher_suites(&[
            rustls::cipher_suite::TLS13_AES_256_GCM_SHA384,
            rustls::cipher_suite::TLS13_AES_128_GCM_SHA256,
            rustls::cipher_suite::TLS13_CHACHA20_POLY1305_SHA256,
        ])
        .with_kx_groups(&[
            &rustls::kx_group::X25519,
            &rustls::kx_group::SECP384R1,
        ])
        .with_protocol_versions(&[&rustls::version::TLS13])?
        .with_no_client_auth()
        .with_single_cert(cert_chain, private_key)?
);
```

## Modern Security Best Practices
- **TLS 1.3 Only**: Disable TLS 1.2 and earlier versions for new applications
- **Strong Cipher Suites**: Use only AEAD ciphers (AES-GCM, ChaCha20-Poly1305)
- **Modern Key Exchange**: Prefer X25519 and P-384 curves
- **HSTS with Preload**: Enable HTTP Strict Transport Security with preload list
- **Certificate Transparency**: Monitor CT logs for unauthorized certificates
- **OCSP Stapling**: Improve performance and privacy of certificate validation

## Security Considerations

TLS/HTTPS implementation is fundamentally about security, but doing it correctly requires attention to many details. This section covers essential security considerations for your TLS implementation.

### TLS Threat Model

Understanding the threats to your TLS implementation helps prioritize security measures:

```mermaid
flowchart TD
    A[🔴 Attacker] -->|1. Downgrade attacks| TLS[🔒 TLS Connection]
    A -->|2. MITM attacks| TLS
    A -->|3. Protocol vulnerabilities| TLS
    A -->|4. Certificate forgery| TLS
    A -->|5. Implementation flaws| TLS
    A -->|6. Cipher weaknesses| TLS

    TLS --> S[🖥️ Server]
    TLS --> C[👤 Client]

    %% Enhanced styling for better contrast and security theme
    style A fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000
    style TLS fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    style S fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    style C fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
```

### Secure Configuration

Implementing a secure TLS configuration is critical to protecting your server:

```rust
/// Create a secure TLS configuration for your Rust server
fn create_secure_tls_config() -> Result<Arc<ServerConfig>> {
    // Load certificates and private key
    let cert_path = "path/to/certificates.pem";
    let key_path = "path/to/private_key.pem";
    
    let cert_file = std::fs::File::open(cert_path)?;
    let key_file = std::fs::File::open(key_path)?;
    
    let cert_chain = rustls_pemfile::certs(&mut std::io::BufReader::new(cert_file))
        .collect::<Result<Vec<_>, _>>()?;
        
    let mut key_reader = std::io::BufReader::new(key_file);
    let key = rustls_pemfile::pkcs8_private_keys(&mut key_reader)
        .next()
        .ok_or_else(|| ServerError::Tls("No private key found".to_string()))??;
        
    // Create root cert store for client authentication
    let mut root_store = RootCertStore::empty();
    
    // Define secure protocol versions (TLS 1.2 and 1.3 only)
    let protocol_versions = vec![&rustls::version::TLS13, &rustls::version::TLS12];
    
    // Define secure cipher suites - prefer ChaCha20 and AES-GCM ciphers in modern TLS
    let cipher_suites = vec![
        // TLS 1.3 suites
        rustls::cipher_suite::TLS13_CHACHA20_POLY1305_SHA256,
        rustls::cipher_suite::TLS13_AES_256_GCM_SHA384,
        rustls::cipher_suite::TLS13_AES_128_GCM_SHA256,
        
        // TLS 1.2 suites
        rustls::cipher_suite::TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
        rustls::cipher_suite::TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
        rustls::cipher_suite::TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
        rustls::cipher_suite::TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
        rustls::cipher_suite::TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
        rustls::cipher_suite::TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
    ];
    
    // Create the TLS configuration builder
    let mut config = ServerConfig::builder()
        .with_cipher_suites(&cipher_suites)
        .with_safe_default_kx_groups()
        .with_protocol_versions(&protocol_versions)?
        .with_no_client_auth()
        .with_single_cert(cert_chain, PrivateKey(key))?;
    
    // Set session parameters
    config.session_storage = ServerSessionMemoryCache::new(1024); // Remember 1024 sessions
    config.ticketer = Ticketer::new()?;
    
    // Set ALPN protocols if needed (for HTTP/2)
    config.alpn_protocols = vec![b"h2".to_vec(), b"http/1.1".to_vec()];
    
    // Disable legacy renegotiation
    config.enable_client_cert_auth = false;
    
    Ok(Arc::new(config))
}
```

### Certificate Management

Properly managing certificates is essential for TLS security:

```rust
/// TLS certificate manager
struct CertificateManager {
    // Current TLS configuration
    current_config: RwLock<Arc<ServerConfig>>,
    // Certificate paths
    cert_path: PathBuf,
    key_path: PathBuf,
    // Last modification time
    last_modified: AtomicU64,
    // Auto-reload interval
    reload_interval: Duration,
}

impl CertificateManager {
    /// Create a new certificate manager
    pub fn new(cert_path: PathBuf, key_path: PathBuf) -> Result<Self> {
        // Load the initial configuration
        let config = create_secure_tls_config()?;
        
        // Get the last modification time
        let last_modified = last_modified_time(&cert_path)?;
        
        Ok(Self {
            current_config: RwLock::new(config),
            cert_path,
            key_path,
            last_modified: AtomicU64::new(last_modified),
            reload_interval: Duration::from_secs(3600), // Check hourly
        })
    }
    
    /// Get the current TLS configuration
    pub fn get_config(&self) -> Arc<ServerConfig> {
        self.current_config.read().unwrap().clone()
    }
    
    /// Check if certificates need to be reloaded
    pub fn check_reload_needed(&self) -> Result<bool> {
        let current_modified = last_modified_time(&self.cert_path)?;
        let last_modified = self.last_modified.load(Ordering::Relaxed);
        
        Ok(current_modified > last_modified)
    }
    
    /// Reload certificates if needed
    pub fn reload_if_needed(&self) -> Result<bool> {
        if !self.check_reload_needed()? {
            return Ok(false);
        }
        
        log::info!("Certificate changes detected, reloading TLS configuration");
        
        // Load new configuration
        let new_config = create_secure_tls_config()?;
        
        // Update the configuration
        {
            let mut config = self.current_config.write().unwrap();
            *config = new_config;
        }
        
        // Update last modified time
        let current_modified = last_modified_time(&self.cert_path)?;
        self.last_modified.store(current_modified, Ordering::Relaxed);
        
        log::info!("TLS configuration reloaded successfully");
        Ok(true)
    }
    
    /// Start a background task to periodically reload certificates
    pub fn start_auto_reload(&self) -> Result<JoinHandle<()>> {
        let cert_path = self.cert_path.clone();
        let reload_interval = self.reload_interval;
        let manager = self.clone();
        
        let handle = thread::spawn(move || {
            loop {
                thread::sleep(reload_interval);
                
                if let Err(e) = manager.reload_if_needed() {
                    log::error!("Failed to reload certificates: {}", e);
                }
            }
        });
        
        Ok(handle)
    }
}

/// Get the last modified time of a file as a Unix timestamp
fn last_modified_time(path: &Path) -> Result<u64> {
    let metadata = std::fs::metadata(path)?;
    let modified = metadata.modified()?;
    
    let since_epoch = modified.duration_since(std::time::UNIX_EPOCH)
        .map_err(|e| ServerError::Io(format!("Time error: {}", e)))?;
        
    Ok(since_epoch.as_secs())
}
```

### Certificate Validation

Properly validate certificates to prevent security issues:

```rust
/// Certificate validator for client certificates
struct CertificateValidator {
    // Root certificates
    root_store: RootCertStore,
    // CRL (Certificate Revocation List)
    crl_store: Option<CrlStore>,
    // OCSP (Online Certificate Status Protocol) client
    ocsp_client: Option<OcspClient>,
    // Whether to check revocation status
    check_revocation: bool,
}

impl CertificateValidator {
    /// Create a new certificate validator
    pub fn new() -> Result<Self> {
        let mut root_store = RootCertStore::empty();
        
        // Add system root certificates
        let certs = rustls_native_certs::load_native_certs()?;
        for cert in certs {
            root_store.add(&Certificate(cert.0))?;
        }
        
        Ok(Self {
            root_store,
            crl_store: None,
            ocsp_client: None,
            check_revocation: false,
        })
    }
    
    /// Enable revocation checking
    pub fn with_revocation_checking(mut self) -> Self {
        self.check_revocation = true;
        self
    }
    
    /// Add a CRL from a file
    pub fn add_crl_from_file(&mut self, path: &Path) -> Result<()> {
        let crl_data = std::fs::read(path)?;
        
        let crl_store = self.crl_store.get_or_insert_with(|| CrlStore::new());
        crl_store.add_crl(crl_data)?;
        
        Ok(())
    }
    
    /// Validate a certificate chain
    pub fn validate_cert_chain(&self, chain: &[Certificate]) -> Result<()> {
        if chain.is_empty() {
            return Err(ServerError::Tls("Empty certificate chain".to_string()));
        }
        
        // Extract the end-entity (leaf) certificate
        let end_entity = chain[0].0.clone();
        
        // Build the chain for verification
        let intermediates = chain.iter().skip(1).map(|c| c.0.clone()).collect::<Vec<_>>();
        
        // Parse the certificate
        let cert = x509_parser::parse_x509_certificate(&end_entity)
            .map_err(|e| ServerError::Tls(format!("Failed to parse certificate: {}", e)))?
            .1;
        
        // Check if the certificate has expired
        let now = SystemTime::now();
        let not_before = cert.validity.not_before.to_datetime();
        let not_after = cert.validity.not_after.to_datetime();
        
        if now < not_before.into() {
            return Err(ServerError::Tls("Certificate not yet valid".to_string()));
        }
        
        if now > not_after.into() {
            return Err(ServerError::Tls("Certificate has expired".to_string()));
        }
        
        // Check for revocation if enabled
        if self.check_revocation {
            // Check CRL if available
            if let Some(ref crl_store) = self.crl_store {
                if crl_store.is_revoked(&cert) {
                    return Err(ServerError::Tls("Certificate is revoked (CRL)".to_string()));
                }
            }
            
            // Check OCSP if available
            if let Some(ref ocsp_client) = self.ocsp_client {
                match ocsp_client.check_status(&cert, &intermediates) {
                    OcspStatus::Revoked => {
                        return Err(ServerError::Tls("Certificate is revoked (OCSP)".to_string()));
                    }
                    OcspStatus::Unknown => {
                        log::warn!("OCSP status unknown for certificate");
                    }
                    OcspStatus::Good => {
                        // Certificate is valid according to OCSP
                    }
                }
            }
        }
        
        Ok(())
    }
}
```

### TLS Version and Cipher Control

Control which TLS versions and ciphers are allowed to prevent vulnerabilities:

```rust
/// TLS version enforcer
struct TlsVersionEnforcer {
    // Minimum allowed TLS version
    min_version: u16,
    // Whether to reject weak ciphers
    reject_weak_ciphers: bool,
}

impl TlsVersionEnforcer {
    /// Create a new TLS version enforcer
    pub fn new() -> Self {
        Self {
            min_version: rustls::version::TLS12, // TLS 1.2 minimum
            reject_weak_ciphers: true,
        }
    }
    
    /// Enforce minimum TLS version on a connection
    pub fn enforce(&self, version: u16, cipher_suite: CipherSuite) -> Result<()> {
        // Check TLS version
        if version < self.min_version {
            let version_str = match version {
                rustls::version::TLS10 => "TLS 1.0",
                rustls::version::TLS11 => "TLS 1.1",
                rustls::version::TLS12 => "TLS 1.2",
                rustls::version::TLS13 => "TLS 1.3",
                _ => "Unknown",
            };
            
            return Err(ServerError::Tls(format!(
                "TLS version too old: {}. Minimum required: TLS 1.2", version_str
            )));
        }
        
        // Check cipher strength if needed
        if self.reject_weak_ciphers {
            let is_weak = match cipher_suite {
                // Reject weak ciphers (no longer used in modern TLS, but included for completeness)
                CipherSuite::TLS_RSA_WITH_3DES_EDE_CBC_SHA |
                CipherSuite::TLS_RSA_WITH_AES_128_CBC_SHA |
                CipherSuite::TLS_RSA_WITH_AES_256_CBC_SHA => true,
                
                // All other ciphers are considered strong
                _ => false,
            };
            
            if is_weak {
                return Err(ServerError::Tls(format!(
                    "Weak cipher suite rejected: {:?}", cipher_suite
                )));
            }
        }
        
        Ok(())
    }
}
```

### HTTP Security Headers

Implement HTTP security headers specifically for HTTPS:

```rust
/// Add security headers to HTTPS responses
fn add_security_headers(response: &mut Response) {
    // Strict-Transport-Security: tell browsers to use HTTPS only
    response.headers.insert(
        "strict-transport-security".to_string(),
        "max-age=31536000; includeSubDomains; preload".to_string()
    );
    
    // Content-Security-Policy: control resources the page can load
    response.headers.insert(
        "content-security-policy".to_string(),
        "default-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; \
         script-src 'self'; object-src 'none'; frame-ancestors 'none'; \
         upgrade-insecure-requests; block-all-mixed-content".to_string()
    );
    
    // X-Content-Type-Options: prevent MIME type sniffing
    response.headers.insert(
        "x-content-type-options".to_string(),
        "nosniff".to_string()
    );
    
    // X-Frame-Options: prevent clickjacking
    response.headers.insert(
        "x-frame-options".to_string(),
        "DENY".to_string()
    );
    
    // X-XSS-Protection: basic XSS protection
    response.headers.insert(
        "x-xss-protection".to_string(),
        "1; mode=block".to_string()
    );
    
    // Referrer-Policy: control referrer information
    response.headers.insert(
        "referrer-policy".to_string(),
        "strict-origin-when-cross-origin".to_string()
    );
    
    // Feature-Policy/Permissions-Policy: control browser features
    response.headers.insert(
        "permissions-policy".to_string(),
        "camera=(), microphone=(), geolocation=(), payment=()".to_string()
    );
}
```

### HTTPS Redirector

Create a secure redirector from HTTP to HTTPS:

```rust
/// HTTP to HTTPS redirector
struct HttpsRedirector {
    // HTTPS port
    https_port: u16,
    // HSTS max age in seconds
    hsts_max_age: u64,
    // Whether to include subdomains in HSTS
    include_subdomains: bool,
    // Whether to add preload directive
    preload: bool,
}

impl HttpsRedirector {
    /// Create a new HTTPS redirector
    pub fn new() -> Self {
        Self {
            https_port: 443,
            hsts_max_age: 31536000, // 1 year
            include_subdomains: true,
            preload: true,
        }
    }
    
    /// Handle HTTP request by redirecting to HTTPS
    pub fn redirect_to_https(&self, request: &Request) -> Response {
        // Extract host from request
        let host = request.headers
            .get("host")
            .cloned()
            .unwrap_or_else(|| "localhost".to_string());
            
        // Remove port if present
        let host = if host.contains(':') {
            host.split(':').next().unwrap().to_string()
        } else {
            host
        };
        
        // Build the HTTPS URL
        let https_url = if self.https_port == 443 {
            format!("https://{}{}", host, request.path)
        } else {
            format!("https://{}:{}{}", host, self.https_port, request.path)
        };
        
        // Create redirect response
        let mut response = Response::new(StatusCode::MovedPermanently);
        response.headers.insert("location".to_string(), https_url);
        
        // Add HSTS header to redirect response to start training browsers
        let hsts_value = format!(
            "max-age={}{}{}",
            self.hsts_max_age,
            if self.include_subdomains { "; includeSubDomains" } else { "" },
            if self.preload { "; preload" } else { "" }
        );
        
        response.headers.insert(
            "strict-transport-security".to_string(),
            hsts_value
        );
        
        response
    }
}
```

### Certificate Pinning

Implement certificate pinning to prevent MITM attacks:

```rust
/// Certificate pinning manager
struct CertificatePinner {
    // Pins by host (host -> set of hashes)
    pins: HashMap<String, HashSet<String>>,
    // Whether pinning is enforced
    enforce: bool,
    // Backup pins (for key rotation)
    backup_pins: HashMap<String, HashSet<String>>,
}

impl CertificatePinner {
    /// Create a new certificate pinner
    pub fn new() -> Self {
        Self {
            pins: HashMap::new(),
            enforce: true,
            backup_pins: HashMap::new(),
        }
    }
    
    /// Add a certificate pin for a host
    pub fn add_pin(&mut self, host: &str, pin: &str) {
        let pins = self.pins.entry(host.to_string()).or_insert_with(HashSet::new);
        pins.insert(pin.to_string());
    }
    
    /// Add a backup certificate pin for a host
    pub fn add_backup_pin(&mut self, host: &str, pin: &str) {
        let pins = self.backup_pins.entry(host.to_string()).or_insert_with(HashSet::new);
        pins.insert(pin.to_string());
    }
    
    /// Check certificate pins for a host
    pub fn check_pins(&self, host: &str, cert_chain: &[Certificate]) -> Result<()> {
        // Skip check if not enforcing
        if !self.enforce {
            return Ok(());
        }
        
        // Check if we have pins for this host
        if !self.pins.contains_key(host) {
            return Ok(());
        }
        
        // Extract pins for this host
        let host_pins = self.pins.get(host).unwrap();
        
        // Calculate the public key hash for each certificate in the chain
        for cert in cert_chain {
            // Extract the public key
            let cert_data = cert.0.clone();
            let parsed = x509_parser::parse_x509_certificate(&cert_data)
                .map_err(|e| ServerError::Tls(format!("Failed to parse certificate: {}", e)))?
                .1;
                
            let public_key = parsed.public_key();
            let public_key_data = public_key.raw;
            
            // Calculate SHA-256 hash
            let mut hasher = Sha256::new();
            hasher.update(public_key_data);
            let hash = hasher.finalize();
            
            // Convert to base64
            let hash_b64 = BASE64_STANDARD.encode(hash);
            
            // Check if hash matches any of our pins
            let pin_value = format!("sha256/{}", hash_b64);
            if host_pins.contains(&pin_value) {
                return Ok(());
            }
        }
        
        Err(ServerError::Tls(format!(
            "Certificate pinning failed for host: {}", host
        )))
    }
}
```

### TLS Server Logging and Monitoring

Implement logging and monitoring for TLS connections:

```rust
/// TLS connection logger
struct TlsConnectionLogger {
    // Log TLS handshakes
    log_handshakes: bool,
    // Log TLS errors
    log_errors: bool,
    // Statistics
    stats: Arc<TlsStats>,
}

/// TLS statistics
struct TlsStats {
    // Total connections
    total_connections: AtomicUsize,
    // Failed handshakes
    failed_handshakes: AtomicUsize,
    // TLS 1.2 connections
    tls12_connections: AtomicUsize,
    // TLS 1.3 connections
    tls13_connections: AtomicUsize,
    // Average handshake time (microseconds)
    avg_handshake_time_us: AtomicUsize,
}

impl TlsConnectionLogger {
    /// Create a new TLS connection logger
    pub fn new() -> Self {
        Self {
            log_handshakes: true,
            log_errors: true,
            stats: Arc::new(TlsStats {
                total_connections: AtomicUsize::new(0),
                failed_handshakes: AtomicUsize::new(0),
                tls12_connections: AtomicUsize::new(0),
                tls13_connections: AtomicUsize::new(0),
                avg_handshake_time_us: AtomicUsize::new(0),
            }),
        }
    }
    
    /// Log a successful TLS handshake
    pub fn log_handshake(&self, client_ip: &str, version: u16, cipher_suite: CipherSuite, duration_us: u64) {
        // Update statistics
        self.stats.total_connections.fetch_add(1, Ordering::Relaxed);
        
        if version == rustls::version::TLS12 {
            self.stats.tls12_connections.fetch_add(1, Ordering::Relaxed);
        } else if version == rustls::version::TLS13 {
            self.stats.tls13_connections.fetch_add(1, Ordering::Relaxed);
        }
        
        // Update average handshake time
        let current_avg = self.stats.avg_handshake_time_us.load(Ordering::Relaxed);
        let total_conns = self.stats.total_connections.load(Ordering::Relaxed);
        
        if total_conns > 0 {
            let new_avg = ((current_avg as u64 * (total_conns as u64 - 1)) + duration_us) / total_conns as u64;
            self.stats.avg_handshake_time_us.store(new_avg as usize, Ordering::Relaxed);
        }
        
        // Log handshake if enabled
        if self.log_handshakes {
            let version_str = match version {
                rustls::version::TLS12 => "TLS 1.2",
                rustls::version::TLS13 => "TLS 1.3",
                _ => "Unknown",
            };
            
            log::info!(
                "TLS handshake from {} using {} with cipher {:?} ({}µs)",
                client_ip, version_str, cipher_suite, duration_us
            );
        }
    }
    
    /// Log a TLS error
    pub fn log_error(&self, client_ip: &str, error: &str) {
        // Update statistics
        self.stats.failed_handshakes.fetch_add(1, Ordering::Relaxed);
        
        // Log error if enabled
        if self.log_errors {
            log::warn!("TLS error from {}: {}", client_ip, error);
        }
    }
    
    /// Get current TLS statistics
    pub fn get_stats(&self) -> TlsStatsReport {
        let total = self.stats.total_connections.load(Ordering::Relaxed);
        let failed = self.stats.failed_handshakes.load(Ordering::Relaxed);
        
        TlsStatsReport {
            total_connections: total,
            failed_handshakes: failed,
            success_rate: if total > 0 {
                ((total - failed) as f64 / total as f64) * 100.0
            } else {
                0.0
            },
            tls12_percentage: if total > 0 {
                (self.stats.tls12_connections.load(Ordering::Relaxed) as f64 / total as f64) * 100.0
            } else {
                0.0
            },
            tls13_percentage: if total > 0 {
                (self.stats.tls13_connections.load(Ordering::Relaxed) as f64 / total as f64) * 100.0
            } else {
                0.0
            },
            avg_handshake_time_us: self.stats.avg_handshake_time_us.load(Ordering::Relaxed),
        }
    }
}
```

### Best Practices for TLS/HTTPS Security

1. **Use Modern TLS Versions**:
   - Only support TLS 1.2 and 1.3
   - Disable all older SSL/TLS versions

2. **Implement Strong Cipher Suites**:
   - Prefer authenticated encryption (AEAD) ciphers like AES-GCM and ChaCha20-Poly1305
   - Use forward secrecy with ECDHE key exchange

3. **Secure Certificate Management**:
   - Use certificates from trusted CAs
   - Implement automated certificate renewal
   - Protect private keys with strong access controls

4. **Apply Security Headers**:
   - Implement HSTS (HTTP Strict Transport Security)
   - Use Content-Security-Policy
   - Set X-Content-Type-Options, X-Frame-Options, etc.

5. **Monitor and Log TLS/HTTPS Connections**:
   - Log TLS handshake details
   - Monitor for unusual handshake failures
   - Track cipher usage to detect downgrade attacks

6. **Redirect HTTP to HTTPS**:
   - Always force HTTPS use through redirects
   - Use HSTS preloading for optimum security

7. **Consider Certificate Pinning**:
   - Pin certificates for critical applications
   - Implement backup pins for certificate rotation

8. **Avoid Mixed Content**:
   - Ensure all resources are loaded via HTTPS
   - Set a Content-Security-Policy that blocks mixed content

## Integration
- Integrate certificate reloads and monitoring.

## Quiz
1. What is the purpose of TLS?
2. Name a Rust crate for HTTPS support.

## Diagram
```mermaid
graph TD
    A[Client] -- HTTPS --> B[Webserver]
    B -- Cert Validation --> A
```
