# Authentication and Authorization

Control access to your webserver using authentication (who are you?) and authorization (what can you do?). This module covers various authentication methods, secure credential storage, and permission-based access control.

## Learning Objectives
- Implement modern, secure user authentication systems
- Design zero-trust authorization systems with fine-grained access control
- Integrate JWT with proper security practices (short-lived tokens, refresh tokens)
- Store credentials securely using Argon2id and other modern algorithms
- Protect against OWASP Top 10 authentication vulnerabilities
- Implement multi-factor authentication (MFA) and passwordless authentication
- Design secure API authentication for microservices

## Prerequisites
- Completion of previous chapters, especially TLS/HTTPS and Security Features
- Understanding of modern cryptography concepts (hashing, signing, encryption)
- Familiarity with OAuth2 and OpenID Connect standards
- Basic knowledge of JWT structure and security considerations

## Authentication Methods

### 1. Session-Based Authentication
Session-based authentication uses cookies to maintain user state:

```rust
use actix_session::{CookieSession, Session};
use actix_web::{web, App, HttpResponse, HttpServer};

// Configure session middleware
fn configure_session() -> CookieSession {
    CookieSession::signed(&[0; 32]) // Use a secure random key in production!
        .secure(true)
        .http_only(true)
        .name("session")
        .max_age(3600) // 1 hour
}

// Login handler
async fn login(
    session: Session, 
    form: web::Form<LoginForm>,
    db: web::Data<DbPool>,
) -> HttpResponse {
    // Verify credentials from database
    match authenticate_user(&form.username, &form.password, &db).await {
        Ok(user) => {
            // Store user ID in session
            session.insert("user_id", user.id)?;
            session.insert("role", user.role)?;
            
            HttpResponse::Ok().finish()
        }
        Err(_) => HttpResponse::Unauthorized().finish(),
    }
}

// Protected route middleware
async fn require_auth(
    session: Session,
    req: ServiceRequest,
) -> Result<ServiceRequest, Error> {
    if let Some(user_id) = session.get::<i64>("user_id")? {
        // User is authenticated
        Ok(req)
    } else {
        // User is not authenticated
        Err(ErrorUnauthorized("Unauthorized"))
    }
}
```

### 2. JWT (JSON Web Tokens) Authentication
JWT provides stateless authentication suitable for APIs:

```rust
use jsonwebtoken::{encode, decode, Header, Validation, EncodingKey, DecodingKey};
use serde::{Deserialize, Serialize};
use chrono::{Utc, Duration};

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,         // Subject (typically user ID)
    exp: usize,          // Expiration time
    iat: usize,          // Issued at time
    roles: Vec<String>,  // User roles for authorization
}

// Generate JWT token
fn create_jwt(user_id: &str, roles: Vec<String>, secret: &[u8]) -> Result<String, Error> {
    let expiration = Utc::now()
        .checked_add_signed(Duration::hours(24))
        .expect("valid timestamp")
        .timestamp() as usize;
        
    let claims = Claims {
        sub: user_id.to_owned(),
        exp: expiration,
        iat: Utc::now().timestamp() as usize,
        roles,
    };
    
    encode(&Header::default(), &claims, &EncodingKey::from_secret(secret))
        .map_err(|e| e.into())
}

// Verify JWT token
fn verify_jwt(token: &str, secret: &[u8]) -> Result<Claims, Error> {
    let validation = Validation::default();
    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(secret),
        &validation,
    )?;
    
    Ok(token_data.claims)
}

// JWT middleware for actix-web
fn jwt_middleware(
    req: ServiceRequest,
    jwt_secret: &[u8],
) -> Result<ServiceRequest, Error> {
    let auth_header = match req.headers().get("Authorization") {
        Some(header) => header,
        None => return Err(ErrorUnauthorized("Missing Authorization header")),
    };
    
    let auth_str = match auth_header.to_str() {
        Ok(s) => s,
        Err(_) => return Err(ErrorUnauthorized("Invalid Authorization header")),
    };
    
    if !auth_str.starts_with("Bearer ") {
        return Err(ErrorUnauthorized("Invalid Authentication scheme"));
    }
    
    let token = &auth_str[7..]; // Remove "Bearer " prefix
    
    match verify_jwt(token, jwt_secret) {
        Ok(claims) => {
            // Store claims in request for later access
            req.extensions_mut().insert(claims);
            Ok(req)
        }
        Err(_) => Err(ErrorUnauthorized("Invalid token")),
    }
}
```

### 3. Secure Password Storage
Always hash passwords before storing:

```rust
use argon2::{
    password_hash::{rand_core::OsRng, PasswordHash, PasswordHasher, PasswordVerifier, SaltString},
    Argon2,
};

// Hash a password
fn hash_password(password: &str) -> Result<String, Error> {
    let salt = SaltString::generate(&mut OsRng);
    let argon2 = Argon2::default();
    
    // Hash password with Argon2id
    let password_hash = argon2
        .hash_password(password.as_bytes(), &salt)?
        .to_string();
    
    Ok(password_hash)
}

// Verify a password against a stored hash
fn verify_password(password: &str, hash: &str) -> Result<bool, Error> {
    let parsed_hash = PasswordHash::new(hash)?;
    let argon2 = Argon2::default();
    
    // Verify password using constant-time comparison
    Ok(argon2.verify_password(password.as_bytes(), &parsed_hash).is_ok())
}

// Usage in user registration
async fn register_user(
    db: &DbPool,
    username: &str,
    email: &str,
    password: &str,
) -> Result<User, Error> {
    // Hash password before storage
    let password_hash = hash_password(password)?;
    
    // Store user with hashed password
    let user = sqlx::query_as!(
        User,
        "INSERT INTO users (username, email, password_hash) VALUES ($1, $2, $3) RETURNING *",
        username,
        email,
        password_hash
    )
    .fetch_one(db)
    .await?;
    
    Ok(user)
}
```

## Authorization Systems

### 1. Role-Based Access Control (RBAC)

```rust
use actix_web::{web, guard, HttpResponse, Error};

#[derive(Debug, Clone)]
enum Role {
    User,
    Editor,
    Admin,
}

// Authorization middleware
fn check_role(
    req: ServiceRequest,
    required_role: Role,
) -> Result<ServiceRequest, Error> {
    // Get user claims from JWT middleware
    if let Some(claims) = req.extensions().get::<Claims>() {
        // Check if user has the required role
        if claims.roles.iter().any(|r| r == required_role.as_str()) {
            return Ok(req);
        }
    }
    
    // User doesn't have the required role
    Err(ErrorForbidden("Insufficient privileges"))
}

// Usage in route configuration
fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/admin")
            .wrap(check_role_middleware(Role::Admin))
            .route("/dashboard", web::get().to(admin_dashboard))
    );
    
    cfg.service(
        web::scope("/editor")
            .wrap(check_role_middleware(Role::Editor))
            .route("/articles", web::get().to(edit_articles))
    );
    
    cfg.service(
        web::scope("/api")
            .wrap(check_role_middleware(Role::User))
            .route("/profile", web::get().to(user_profile))
    );
}
```

### 2. Permission-Based Authorization

More granular than RBAC, permission-based systems control specific actions:

```rust
use std::collections::HashSet;

#[derive(Debug, Clone, Eq, PartialEq, Hash)]
enum Permission {
    ReadArticle,
    EditArticle,
    DeleteArticle,
    ManageUsers,
    ViewAnalytics,
}

// Check if a user has a specific permission
async fn has_permission(
    db: &DbPool,
    user_id: i64,
    permission: Permission,
) -> Result<bool, Error> {
    // Get user permissions from database
    let permissions = get_user_permissions(db, user_id).await?;
    
    // Check if permission exists
    Ok(permissions.contains(&permission))
}

// Permission checking middleware
async fn require_permission(
    req: ServiceRequest,
    db: &DbPool,
    required_permission: Permission,
) -> Result<ServiceRequest, Error> {
    // Get user ID from session or JWT
    let user_id = get_user_id_from_request(&req)?;
    
    // Check permission
    if has_permission(db, user_id, required_permission).await? {
        Ok(req)
    } else {
        Err(ErrorForbidden("Permission denied"))
    }
}

// Example usage in handler
async fn delete_article(
    req: HttpRequest,
    path: web::Path<i64>,
    db: web::Data<DbPool>,
) -> Result<HttpResponse, Error> {
    let article_id = path.into_inner();
    let user_id = get_user_id_from_request(&req)?;
    
    // Check permission
    if !has_permission(&db, user_id, Permission::DeleteArticle).await? {
        return Err(ErrorForbidden("Permission denied"));
    }
    
    // Delete article
    delete_article_from_db(&db, article_id).await?;
    
    Ok(HttpResponse::Ok().finish())
}
```

## Multi-Factor Authentication (MFA)

Add an extra layer of security with TOTP (Time-based One-Time Passwords):

```rust
use totp_rs::{TOTP, Algorithm, Secret};

// Generate new TOTP secret for a user
fn generate_totp_secret(username: &str) -> (String, String) {
    // Generate a random secret
    let secret = Secret::generate_secret();
    
    // Create TOTP instance
    let totp = TOTP::new(
        Algorithm::SHA1,
        6,
        1,
        30,
        Secret::Raw(secret.to_vec()).to_bytes().unwrap(),
    ).unwrap();
    
    // Generate URI for QR code
    let uri = totp.get_url(username, "YourServiceName");
    
    // Return the base32 encoded secret and URI
    (totp.get_secret_base32(), uri)
}

// Verify TOTP code
fn verify_totp(secret: &str, code: &str) -> bool {
    let totp = TOTP::new(
        Algorithm::SHA1,
        6,
        1,
        30,
        Secret::Base32(secret.to_string()).to_bytes().unwrap(),
    ).unwrap();
    
    totp.check_current(code).unwrap_or(false)
}

// Login with MFA
async fn login_with_mfa(
    session: Session,
    form: web::Form<MfaLoginForm>,
    db: web::Data<DbPool>,
) -> HttpResponse {
    // First verify username and password
    match authenticate_user(&form.username, &form.password, &db).await {
        Ok(user) => {
            // Then verify TOTP code
            if user.mfa_enabled {
                if !verify_totp(&user.totp_secret, &form.totp_code) {
                    return HttpResponse::Unauthorized().json(json!({
                        "error": "Invalid MFA code"
                    }));
                }
            }
            
            // Both checks passed, create session
            session.insert("user_id", user.id)?;
            session.insert("role", user.role)?;
            
            HttpResponse::Ok().finish()
        }
        Err(_) => HttpResponse::Unauthorized().finish(),
    }
}
```

## OAuth2 Integration

Allow users to sign in with external providers:

```rust
use oauth2::{
    AuthUrl, ClientId, ClientSecret, AuthorizationCode,
    TokenUrl, RedirectUrl, TokenResponse, Scope, CsrfToken,
    PkceCodeChallenge, PkceCodeVerifier, StandardTokenResponse,
    basic::BasicClient, reqwest::http_client
};

// Set up OAuth2 client for Google
fn create_oauth_client() -> BasicClient {
    BasicClient::new(
        ClientId::new("YOUR_CLIENT_ID".to_string()),
        Some(ClientSecret::new("YOUR_CLIENT_SECRET".to_string())),
        AuthUrl::new("https://accounts.google.com/o/oauth2/v2/auth".to_string()).unwrap(),
        Some(TokenUrl::new("https://oauth2.googleapis.com/token".to_string()).unwrap()),
    )
    .set_redirect_uri(RedirectUrl::new("http://localhost:8080/auth/google/callback".to_string()).unwrap())
}

// Start OAuth flow
async fn start_oauth_flow(session: Session) -> HttpResponse {
    let client = create_oauth_client();
    
    // Generate PKCE challenge
    let (pkce_challenge, pkce_verifier) = PkceCodeChallenge::new_random_sha256();
    
    // Store PKCE verifier in session
    session.insert("pkce_verifier", pkce_verifier.secret())?;
    
    // Generate authorization URL with CSRF token
    let (auth_url, csrf_token) = client
        .authorize_url(CsrfToken::new_random)
        .add_scope(Scope::new("profile".to_string()))
        .add_scope(Scope::new("email".to_string()))
        .set_pkce_challenge(pkce_challenge)
        .url();
    
    // Store CSRF token in session
    session.insert("csrf_token", csrf_token.secret())?;
    
    // Redirect to authorization URL
    HttpResponse::Found()
        .append_header(("Location", auth_url.to_string()))
        .finish()
}

// Handle OAuth callback
async fn oauth_callback(
    req: HttpRequest, 
    query: web::Query<OAuthCallback>,
    session: Session,
    db: web::Data<DbPool>,
) -> HttpResponse {
    // Verify CSRF token
    let csrf_token = session.get::<String>("csrf_token")?;
    if csrf_token != query.state {
        return HttpResponse::BadRequest().body("Invalid CSRF token");
    }
    
    // Get PKCE verifier from session
    let pkce_verifier = match session.get::<String>("pkce_verifier")? {
        Some(v) => PkceCodeVerifier::new(v),
        None => return HttpResponse::BadRequest().body("Missing PKCE verifier"),
    };
    
    // Exchange authorization code for token
    let client = create_oauth_client();
    let token_result = client
        .exchange_code(AuthorizationCode::new(query.code.clone()))
        .set_pkce_verifier(pkce_verifier)
        .request(http_client)
        .await;
    
    let token = match token_result {
        Ok(t) => t,
        Err(_) => return HttpResponse::BadRequest().body("Failed to exchange code"),
    };
    
    // Use token to get user info
    let user_info = get_user_info_from_provider(token).await?;
    
    // Find or create user in database
    let user = find_or_create_oauth_user(&db, &user_info).await?;
    
    // Set up session
    session.insert("user_id", user.id)?;
    session.insert("role", user.role)?;
    
    // Redirect to dashboard
    HttpResponse::Found()
        .append_header(("Location", "/dashboard"))
        .finish()
}
```

## Security Considerations

### Threat Model for Authentication & Authorization

Understanding the threat landscape for authentication and authorization systems is essential for proper implementation:

```mermaid
graph TD
    A[🔴 Attacker] -->|1. Credential Stuffing| Auth[🔐 Authentication System]
    A -->|2. Brute Force| Auth
    A -->|3. Session Hijacking| Auth
    A -->|4. JWT Token Theft| Auth
    A -->|5. Privilege Escalation| Authz[🛡️ Authorization System]
    A -->|6. Insecure Direct Object References| Authz

    Auth --> User[👤 User Identity]
    User --> Authz
    Authz --> Resources[🔒 Protected Resources]

    %% Enhanced styling for security theme
    style A fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000
    style Auth fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    style Authz fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    style User fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style Resources fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
```

### 1. Secure Password Handling

Passwords must be properly hashed before storage to protect against database breaches:

```rust
use argon2::{
    password_hash::{rand_core::OsRng, PasswordHasher, SaltString, PasswordHash, PasswordVerifier},
    Argon2, Params,
};

// Configuration for Argon2id with modern parameters
fn create_argon2_hasher() -> Argon2<'static> {
    let params = Params::new(
        // Memory cost: 64MB (65536 KiB)
        65536,
        // Iterations: 3 passes
        3, 
        // Parallelism: Adjust based on CPU cores
        4,
        Some(32), // Output length: 32 bytes (256 bits)
    ).expect("Invalid Argon2 parameters");
    
    Argon2::new(argon2::Algorithm::Argon2id, argon2::Version::V0x13, params)
}

// Securely hash a password
fn hash_password(password: &str) -> Result<String, Error> {
    // Generate a cryptographically secure salt
    let salt = SaltString::generate(&mut OsRng);
    
    // Create hasher with modern parameters
    let argon2 = create_argon2_hasher();
    
    // Hash password
    let hash = argon2
        .hash_password(password.as_bytes(), &salt)?
        .to_string();
    
    Ok(hash)
}

// Securely verify a password
fn verify_password(password: &str, hash: &str) -> Result<bool, Error> {
    let parsed_hash = PasswordHash::new(hash)?;
    let argon2 = create_argon2_hasher();
    
    // Use constant-time comparison to prevent timing attacks
    Ok(argon2.verify_password(password.as_bytes(), &parsed_hash).is_ok())
}

// Store password - never log or expose the password
async fn store_user_password(
    db: &DbPool,
    user_id: i64, 
    password: &str
) -> Result<(), Error> {
    // Hash password before storage
    let password_hash = hash_password(password)?;
    
    // Store hash in database
    sqlx::query!(
        "UPDATE users SET password_hash = $1, password_updated_at = NOW() WHERE id = $2",
        password_hash,
        user_id
    )
    .execute(db)
    .await?;
    
    // Implement password history if needed
    sqlx::query!(
        "INSERT INTO password_history (user_id, password_hash, created_at) VALUES ($1, $2, NOW())",
        user_id,
        password_hash
    )
    .execute(db)
    .await?;
    
    Ok(())
}

// Enforce password policies
fn validate_password_strength(password: &str) -> Result<(), Error> {
    if password.len() < 12 {
        return Err(Error::PasswordTooShort);
    }
    
    // Check for variety of character types
    let has_lowercase = password.chars().any(|c| c.is_lowercase());
    let has_uppercase = password.chars().any(|c| c.is_uppercase());
    let has_digit = password.chars().any(|c| c.is_ascii_digit());
    let has_symbol = password.chars().any(|c| !c.is_alphanumeric());
    
    if !(has_lowercase && has_uppercase && has_digit && has_symbol) {
        return Err(Error::PasswordTooWeak);
    }
    
    // Check against common password lists
    if is_common_password(password) {
        return Err(Error::PasswordTooCommon);
    }
    
    Ok(())
}
```

### 2. JWT Security Best Practices

JWTs require careful handling to prevent common security vulnerabilities:

```rust
use jsonwebtoken::{encode, decode, Header, Algorithm, Validation, EncodingKey, DecodingKey};
use serde::{Deserialize, Serialize};
use chrono::{Utc, Duration};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    // Standard claims
    sub: String,     // Subject (user ID)
    exp: usize,      // Expiration time
    iat: usize,      // Issued at time
    jti: String,     // JWT ID (unique identifier)
    
    // Custom claims
    roles: Vec<String>,
    scope: String,
}

// Secure JWT generation with short expiry
fn generate_auth_tokens(
    user_id: &str,
    roles: Vec<String>,
    scope: String,
    secret: &[u8],
    refresh_secret: &[u8],
) -> Result<(String, String), Error> {
    // Create access token (short-lived)
    let access_token = generate_token(
        user_id, 
        roles.clone(), 
        scope.clone(), 
        Duration::minutes(15), // Short lifetime
        secret,
    )?;
    
    // Create refresh token (longer-lived)
    let refresh_token = generate_token(
        user_id, 
        vec!["refresh".to_string()], // Limited scope
        "refresh".to_string(), 
        Duration::days(7), // Longer lifetime
        refresh_secret,    // Different signing key
    )?;
    
    Ok((access_token, refresh_token))
}

// Generate a signed JWT
fn generate_token(
    user_id: &str,
    roles: Vec<String>,
    scope: String,
    duration: Duration,
    secret: &[u8],
) -> Result<String, Error> {
    let now = Utc::now();
    let expiration = now + duration;
    
    let claims = Claims {
        sub: user_id.to_owned(),
        exp: expiration.timestamp() as usize,
        iat: now.timestamp() as usize,
        jti: Uuid::new_v4().to_string(), // Unique token ID
        roles,
        scope,
    };
    
    // Use strong algorithms
    let header = Header::new(Algorithm::HS256);
    
    encode(&header, &claims, &EncodingKey::from_secret(secret))
        .map_err(|e| e.into())
}

// Verify JWT with robust validation
fn verify_token(token: &str, secret: &[u8]) -> Result<Claims, Error> {
    // Setup strict validation
    let mut validation = Validation::new(Algorithm::HS256);
    validation.validate_exp = true;
    validation.set_required_spec_claims(&["exp", "iat", "sub", "jti"]);
    validation.leeway = 0; // No leeway for expiration time
    
    // Decode and verify token
    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(secret),
        &validation,
    )?;
    
    // Check if token has been revoked
    if is_token_revoked(&token_data.claims.jti) {
        return Err(Error::TokenRevoked);
    }
    
    Ok(token_data.claims)
}

// Revoke a token (store in blacklist/database)
async fn revoke_token(jti: &str, user_id: &str) -> Result<(), Error> {
    // Store revoked token with expiration
    redis::cmd("SET")
        .arg(format!("revoked_token:{}", jti))
        .arg(user_id)
        .arg("EX")
        .arg(86400) // Store for 24 hours
        .execute(&mut get_redis_connection()?);
    
    Ok(())
}

// Check if token is revoked
fn is_token_revoked(jti: &str) -> bool {
    match redis::cmd("EXISTS")
        .arg(format!("revoked_token:{}", jti))
        .query::<bool>(&mut get_redis_connection().unwrap()) {
        Ok(exists) => exists,
        Err(_) => false, // Fail open to prevent DOS, but log error
    }
}

// Implementation of refresh flow with rotation
async fn refresh_auth_token(
    refresh_token: &str,
    refresh_secret: &[u8],
    auth_secret: &[u8],
) -> Result<(String, String), Error> {
    // Verify refresh token
    let claims = verify_token(refresh_token, refresh_secret)?;
    
    // Ensure token has refresh scope
    if claims.scope != "refresh" {
        return Err(Error::InvalidToken);
    }
    
    // Revoke the used refresh token (prevent replay)
    revoke_token(&claims.jti, &claims.sub).await?;
    
    // Issue new token pair
    let user_id = claims.sub;
    let user_roles = get_user_roles(&user_id).await?;
    
    generate_auth_tokens(
        &user_id,
        user_roles,
        "api".to_string(),
        auth_secret,
        refresh_secret,
    )
}
```

### 3. Cross-Site Request Forgery (CSRF) Protection

Protect authentication operations from CSRF attacks:

```rust
use actix_csrf::CsrfMiddleware;
use actix_web::{web, cookie::{Cookie, SameSite}};
use ring::hmac;

// Set up CSRF protection
fn setup_csrf_protection() -> CsrfMiddleware {
    CsrfMiddleware::builder()
        .set_cookie_name("csrf_token")
        .set_cookie_http_only(true)
        .set_cookie_secure(true)
        .set_cookie_same_site(SameSite::Lax) // SameSite=Lax is safer than None
        .set_cookie_path("/")
        .set_header_name("X-CSRF-Token")
        .build()
}

// Generate CSRF token with HMAC-based double submit
fn generate_csrf_token(session_id: &str, secret: &[u8]) -> String {
    let key = hmac::Key::new(hmac::HMAC_SHA256, secret);
    let tag = hmac::sign(&key, session_id.as_bytes());
    
    base64::encode(tag.as_ref())
}

// Middleware for login forms to include CSRF token
async fn login_form_handler(
    csrf_token: web::Data<CsrfToken>,
) -> HttpResponse {
    let form_html = format!(
        r#"<form method="post" action="/login">
            <input type="hidden" name="csrf_token" value="{}">
            <input type="text" name="username">
            <input type="password" name="password">
            <button type="submit">Login</button>
        </form>"#,
        csrf_token.token()
    );
    
    HttpResponse::Ok()
        .content_type("text/html")
        .body(form_html)
}

// Login handler with CSRF validation
async fn login_handler(
    form: web::Form<LoginForm>,
    csrf_token: CsrfToken, // Will automatically validate
) -> HttpResponse {
    // Token already validated by middleware
    // Proceed with authentication
    // ...
    
    HttpResponse::Ok().finish()
}
```

### 4. Rate Limiting and Brute Force Protection

Implement robust rate limiting to protect against credential stuffing and brute force attacks:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Mutex;
use std::time::{Duration, Instant};

struct AdvancedRateLimiter {
    // Track attempts by IP
    ip_attempts: HashMap<IpAddr, Vec<Instant>>,
    // Track attempts by username
    username_attempts: HashMap<String, Vec<Instant>>,
    // Track failed attempts for account lockout
    account_failures: HashMap<String, usize>,
    // Track suspicious IPs
    suspicious_ips: HashMap<IpAddr, u32>,
    // Configuration
    window: Duration,
    max_ip_attempts: usize,
    max_username_attempts: usize,
    account_lockout_threshold: usize,
}

impl AdvancedRateLimiter {
    fn new() -> Self {
        Self {
            ip_attempts: HashMap::new(),
            username_attempts: HashMap::new(),
            account_failures: HashMap::new(),
            suspicious_ips: HashMap::new(),
            window: Duration::from_secs(300), // 5 minutes
            max_ip_attempts: 10,
            max_username_attempts: 5,
            account_lockout_threshold: 5,
        }
    }
    
    // Check if authentication attempt should be rate limited
    fn check_auth_attempt(&mut self, ip: IpAddr, username: &str) -> Result<(), RateLimitError> {
        let now = Instant::now();
        
        // Clean up old records
        self.cleanup_old_attempts(now);
        
        // Check for IP rate limit
        if self.is_ip_limited(ip, now) {
            // Increment suspicion score for this IP
            *self.suspicious_ips.entry(ip).or_insert(0) += 1;
            
            return Err(RateLimitError::IpLimited {
                ip,
                retry_after: 300,
            });
        }
        
        // Check for username rate limit (prevents username enumeration)
        if self.is_username_limited(username, now) {
            return Err(RateLimitError::UsernameLimited {
                retry_after: 300,
            });
        }
        
        // Check for account lockout
        if self.is_account_locked(username) {
            return Err(RateLimitError::AccountLocked {
                retry_after: 1800, // 30 minutes
            });
        }
        
        // Record the attempt
        self.record_attempt(ip, username.to_string(), now);
        
        Ok(())
    }
    
    // Record authentication failure
    fn record_failure(&mut self, ip: IpAddr, username: &str) {
        // Increment account failure counter
        *self.account_failures.entry(username.to_string()).or_insert(0) += 1;
        
        // Increment IP suspicion score
        *self.suspicious_ips.entry(ip).or_insert(0) += 2;
        
        // Log the failure for security monitoring
        log::warn!(
            "Authentication failure: username={}, ip={}, failures={}", 
            username,
            ip,
            self.account_failures.get(username).unwrap_or(&0)
        );
    }
    
    // Record successful authentication
    fn record_success(&mut self, username: &str) {
        // Reset failure counter
        self.account_failures.remove(username);
    }
    
    // Helper methods implementation...
    fn cleanup_old_attempts(&mut self, now: Instant) {
        // Clean IP attempts
        for attempts in self.ip_attempts.values_mut() {
            attempts.retain(|time| now.duration_since(*time) < self.window);
        }
        
        // Clean username attempts
        for attempts in self.username_attempts.values_mut() {
            attempts.retain(|time| now.duration_since(*time) < self.window);
        }
        
        // Remove empty entries
        self.ip_attempts.retain(|_, v| !v.is_empty());
        self.username_attempts.retain(|_, v| !v.is_empty());
    }
    
    fn is_ip_limited(&self, ip: IpAddr, now: Instant) -> bool {
        if let Some(attempts) = self.ip_attempts.get(&ip) {
            // Get suspicion score multiplier (repeat offenders face stricter limits)
            let suspicion_factor = match self.suspicious_ips.get(&ip) {
                Some(score) if *score > 10 => 0.2, // 80% reduction in limit
                Some(score) if *score > 5 => 0.5,  // 50% reduction in limit
                Some(_) => 0.8,                    // 20% reduction in limit
                None => 1.0,                       // No reduction
            };
            
            let effective_limit = (self.max_ip_attempts as f32 * suspicion_factor) as usize;
            return attempts.len() >= effective_limit;
        }
        false
    }
    
    fn is_username_limited(&self, username: &str, now: Instant) -> bool {
        if let Some(attempts) = self.username_attempts.get(username) {
            return attempts.len() >= self.max_username_attempts;
        }
        false
    }
    
    fn is_account_locked(&self, username: &str) -> bool {
        if let Some(failures) = self.account_failures.get(username) {
            return *failures >= self.account_lockout_threshold;
        }
        false
    }
    
    fn record_attempt(&mut self, ip: IpAddr, username: String, now: Instant) {
        // Record IP attempt
        self.ip_attempts.entry(ip)
            .or_insert_with(Vec::new)
            .push(now);
            
        // Record username attempt
        self.username_attempts.entry(username)
            .or_insert_with(Vec::new)
            .push(now);
    }
}

// Use in login handler with comprehensive protection
async fn secure_login(
    req: HttpRequest,
    form: web::Form<LoginForm>,
    rate_limiter: web::Data<Mutex<AdvancedRateLimiter>>,
    db: web::Data<DbPool>,
) -> HttpResponse {
    // Get client IP
    let ip = req.peer_addr()
        .map(|addr| addr.ip())
        .unwrap_or_else(|| "0.0.0.0".parse().unwrap());
    
    let username = &form.username;
    
    // Apply rate limiting
    let rate_limit_result = {
        let mut limiter = rate_limiter.lock().unwrap();
        limiter.check_auth_attempt(ip, username)
    };
    
    if let Err(error) = rate_limit_result {
        match error {
            RateLimitError::IpLimited { retry_after, .. } => {
                return HttpResponse::TooManyRequests()
                    .append_header(("Retry-After", retry_after.to_string()))
                    .finish();
            }
            RateLimitError::UsernameLimited { retry_after } => {
                return HttpResponse::TooManyRequests()
                    .append_header(("Retry-After", retry_after.to_string()))
                    .finish();
            }
            RateLimitError::AccountLocked { retry_after } => {
                return HttpResponse::Forbidden()
                    .append_header(("Retry-After", retry_after.to_string()))
                    .json(json!({
                        "error": "Account temporarily locked due to multiple failed attempts"
                    }));
            }
        }
    }
    
    // Perform actual authentication
    match authenticate_user(username, &form.password, &db).await {
        Ok(user) => {
            // Record successful login
            let mut limiter = rate_limiter.lock().unwrap();
            limiter.record_success(username);
            
            // Create session, tokens, etc.
            // ...
            
            // Log successful authentication
            log::info!("User {} logged in successfully from {}", username, ip);
            
            HttpResponse::Ok().finish()
        }
        Err(_) => {
            // Record failed login
            let mut limiter = rate_limiter.lock().unwrap();
            limiter.record_failure(ip, username);
            
            // Introduce constant-time delay to prevent timing attacks
            // that could be used for username enumeration
            tokio::time::sleep(Duration::from_millis(500)).await;
            
            HttpResponse::Unauthorized().finish()
        }
    }
}
```

### 5. Session Security

Properly secure session storage and transmission:

```rust
use actix_session::SessionExt;
use actix_web::cookie::{Cookie, SameSite};
use rand::Rng;
use std::time::{Duration, SystemTime};

// Create secure session configuration
fn configure_secure_session() -> SessionMiddlewareBuilder {
    // Generate a strong key (in production, load from secure storage)
    let secret_key = Key::generate();
    
    // Configure the session middleware
    SessionMiddleware::builder(CookieSessionStore::default(), secret_key)
        .cookie_secure(true)                      // Require HTTPS
        .cookie_http_only(true)                   // Prevent JavaScript access
        .cookie_same_site(SameSite::Lax)          // Prevent CSRF
        .session_lifecycle(                       // Define lifecycle
            PersistentSession::default()
                .session_ttl(Duration::hours(2))  // 2 hour validity
                .session_ttl_extension_policy(    // Sliding expiration policy
                    TtlExtensionPolicy::OnStateChanges
                )
        )
}

// Session regeneration on privilege change
async fn login_handler(
    session: Session,
    form: web::Form<LoginForm>,
    db: web::Data<DbPool>,
) -> Result<HttpResponse, Error> {
    // Authenticate user
    let user = authenticate_user(&form.username, &form.password, &db).await?;
    
    // Force new session ID generation to prevent session fixation
    session.renew();
    
    // Store minimal information in session
    session.insert("user_id", user.id)?;
    session.insert("roles", user.roles.clone())?;
    session.insert("created_at", SystemTime::now().elapsed().unwrap().as_secs())?;
    
    // Store session fingerprint to detect anomalies
    let fingerprint = generate_fingerprint(&session.request());
    session.insert("fp", fingerprint)?;
    
    Ok(HttpResponse::Ok().finish())
}

// Session validation middleware
async fn validate_session(
    session: Session,
    req: ServiceRequest,
) -> Result<ServiceRequest, actix_web::Error> {
    // Check if session exists
    if let Ok(Some(user_id)) = session.get::<i64>("user_id") {
        // Validate session fingerprint to detect potential hijacking
        let current_fp = generate_fingerprint(req.request());
        
        if let Ok(Some(session_fp)) = session.get::<String>("fp") {
            if session_fp != current_fp {
                // Fingerprint mismatch - potential session hijacking
                session.purge();
                return Err(actix_web::error::ErrorUnauthorized("Invalid session"));
            }
        }
        
        // Check for session expiration
        if let Ok(Some(created_at)) = session.get::<u64>("created_at") {
            let current_time = SystemTime::now().elapsed().unwrap().as_secs();
            
            // Maximum absolute session lifetime (12 hours), regardless of activity
            if current_time - created_at > 12 * 3600 {
                session.purge();
                return Err(actix_web::error::ErrorUnauthorized("Session expired"));
            }
        }
        
        // Session is valid
        Ok(req)
    } else {
        Err(actix_web::error::ErrorUnauthorized("Unauthorized"))
    }
}

// Generate browser fingerprint to help detect session hijacking
fn generate_fingerprint(request: &HttpRequest) -> String {
    use sha2::{Sha256, Digest};
    
    let mut hasher = Sha256::new();
    
    // Add user agent
    if let Some(user_agent) = request.headers().get("user-agent") {
        if let Ok(ua) = user_agent.to_str() {
            hasher.update(ua.as_bytes());
        }
    }
    
    // Add IP address (with caution, as it can change for mobile users)
    if let Some(ip) = request.connection_info().realip_remote_addr() {
        hasher.update(ip.as_bytes());
    }
    
    // Add client hints if available
    for header in &["sec-ch-ua", "sec-ch-ua-platform"] {
        if let Some(value) = request.headers().get(*header) {
            if let Ok(v) = value.to_str() {
                hasher.update(v.as_bytes());
            }
        }
    }
    
    // Compute hash
    let result = hasher.finalize();
    
    // Return as hex string
    format!("{:x}", result)
}
```

### 6. Permission Verification and Least Privilege

Implement robust permission checking with proper logging:

```rust
use actix_web::{dev::ServiceRequest, Error, HttpMessage};
use futures::future::{ready, Ready};
use std::future::Future;
use std::pin::Pin;
use std::task::{Context, Poll};
use std::marker::PhantomData;

#[derive(Clone, Debug, PartialEq, Eq, Hash)]
enum Permission {
    ViewUsers,
    CreateUser,
    EditUser,
    DeleteUser,
    ViewArticles,
    CreateArticle,
    EditArticle,
    DeleteArticle,
    AdminAccess,
}

// Permission guard middleware
struct PermissionGuard<P> {
    permission: P,
    error_handler: Option<fn() -> HttpResponse>,
}

impl<P> PermissionGuard<P>
where
    P: Fn(&User) -> bool + Clone + 'static,
{
    fn new(permission_check: P) -> Self {
        Self {
            permission: permission_check,
            error_handler: None,
        }
    }
    
    fn with_error_handler(mut self, handler: fn() -> HttpResponse) -> Self {
        self.error_handler = Some(handler);
        self
    }
}

// Middleware implementation for permission checking
impl<S, B, P> Transform<S, ServiceRequest> for PermissionGuard<P>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
    P: Fn(&User) -> bool + Clone + 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = PermissionGuardMiddleware<S, P>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(PermissionGuardMiddleware {
            service,
            permission_check: self.permission.clone(),
            error_handler: self.error_handler,
        }))
    }
}

struct PermissionGuardMiddleware<S, P> {
    service: S,
    permission_check: P,
    error_handler: Option<fn() -> HttpResponse>,
}

// Service implementation for permission guard
impl<S, B, P> Service<ServiceRequest> for PermissionGuardMiddleware<S, P>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
    P: Fn(&User) -> bool + Clone + 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    fn poll_ready(&self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.service.poll_ready(cx)
    }

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let check = self.permission_check.clone();
        let error_handler = self.error_handler;
        
        // Get user from request extensions (set by authentication middleware)
        let user = match req.extensions().get::<User>().cloned() {
            Some(user) => user,
            None => {
                // No authenticated user found
                let res = error_handler
                    .map(|h| h())
                    .unwrap_or_else(|| HttpResponse::Unauthorized().finish());
                
                return Box::pin(async move {
                    Ok(req.into_response(res.into_body()))
                });
            }
        };

        // Check if user has required permission
        if !check(&user) {
            // Log permission denied for security auditing
            log::warn!(
                "Permission denied: user={} path={} method={}", 
                user.id, 
                req.path(), 
                req.method()
            );
            
            let res = error_handler
                .map(|h| h())
                .unwrap_or_else(|| {
                    HttpResponse::Forbidden()
                        .json(json!({"error": "Insufficient permissions"}))
                });
                
            return Box::pin(async move {
                Ok(req.into_response(res.into_body()))
            });
        }

        // User has permission, proceed with request
        let fut = self.service.call(req);
        
        Box::pin(async move {
            fut.await
        })
    }
}

// Create permission checking functions
fn has_permission(required: Permission) -> impl Fn(&User) -> bool {
    move |user: &User| {
        // Admin role has all permissions
        if user.roles.contains(&"admin".to_string()) {
            return true;
        }
        
        // Check specific permission
        let user_permissions = get_user_permissions(user);
        user_permissions.contains(&required)
    }
}

// Usage in routes configuration
fn configure_routes(cfg: &mut web::ServiceConfig) {
    // Protected admin routes
    cfg.service(
        web::scope("/admin")
            .wrap(PermissionGuard::new(has_permission(Permission::AdminAccess)))
            .route("/dashboard", web::get().to(admin_dashboard))
    );
    
    // User management with fine-grained permissions
    cfg.service(
        web::scope("/users")
            .route("", web::get().to(list_users))
                .wrap(PermissionGuard::new(has_permission(Permission::ViewUsers)))
            .route("", web::post().to(create_user))
                .wrap(PermissionGuard::new(has_permission(Permission::CreateUser)))
            .route("/{id}", web::put().to(update_user))
                .wrap(PermissionGuard::new(has_permission(Permission::EditUser)))
            .route("/{id}", web::delete().to(delete_user))
                .wrap(PermissionGuard::new(has_permission(Permission::DeleteUser)))
    );
}
```

### 7. Security Logging and Monitoring

Implement comprehensive logging for authentication events to detect security incidents:

```rust
use tracing::{info, warn, error};
use serde_json::json;
use uuid::Uuid;

// Security event categories
enum SecurityEventType {
    Login,
    Logout,
    PasswordChange,
    FailedLogin,
    AccountLockout,
    PermissionDenied,
    TokenRevocation,
    RoleChange,
    Mfa,
}

// Log security event with structured data
fn log_security_event(
    event_type: SecurityEventType,
    user_id: Option<&str>,
    details: serde_json::Value,
    request: &HttpRequest,
) {
    // Get client IP
    let client_ip = request
        .connection_info()
        .realip_remote_addr()
        .unwrap_or("unknown")
        .to_string();
    
    // Get user agent
    let user_agent = request
        .headers()
        .get(header::USER_AGENT)
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown")
        .to_string();
    
    // Generate event ID
    let event_id = Uuid::new_v4().to_string();
    
    // Create structured log data
    let log_data = json!({
        "event_id": event_id,
        "event_type": format!("{:?}", event_type),
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "user_id": user_id.unwrap_or("anonymous"),
        "client_ip": client_ip,
        "user_agent": user_agent,
        "path": request.path(),
        "method": request.method().as_str(),
        "details": details
    });
    
    // Log based on event type
    match event_type {
        SecurityEventType::Login | 
        SecurityEventType::Logout | 
        SecurityEventType::PasswordChange |
        SecurityEventType::Mfa => {
            info!(security_event = true, "Security event: {:?}", log_data);
        },
        SecurityEventType::FailedLogin | 
        SecurityEventType::PermissionDenied |
        SecurityEventType::TokenRevocation |
        SecurityEventType::RoleChange => {
            warn!(security_event = true, "Security event: {:?}", log_data);
        },
        SecurityEventType::AccountLockout => {
            error!(security_event = true, "Security event: {:?}", log_data);
        }
    }
    
    // Forward to security monitoring system if configured
    if let Some(security_monitor) = get_security_monitor() {
        let _ = security_monitor.send_event(log_data);
    }
}

// Example usage
async fn login_handler(
    req: HttpRequest,
    form: web::Form<LoginForm>,
    db: web::Data<DbPool>,
) -> Result<HttpResponse, Error> {
    match authenticate_user(&form.username, &form.password, &db).await {
        Ok(user) => {
            // Log successful login
            log_security_event(
                SecurityEventType::Login,
                Some(&user.id.to_string()),
                json!({
                    "username": form.username,
                    "success": true,
                    "mfa_used": user.mfa_enabled,
                }),
                &req
            );
            
            // Create session, etc.
            
            Ok(HttpResponse::Ok().finish())
        },
        Err(e) => {
            // Log failed login
            log_security_event(
                SecurityEventType::FailedLogin,
                None,
                json!({
                    "username": form.username,
                    "error": e.to_string(),
                }),
                &req
            );
            
            Ok(HttpResponse::Unauthorized().finish())
        }
    }
}
```

### 8. Multi-Factor Authentication Security

Implement secure MFA handling with proper session management:

```rust
use totp_rs::{TOTP, Algorithm, Secret};
use qrcode::{QrCode, render::unicode::Dense1x2};
use base32;
use rand::Rng;

// Generate secure MFA secret
fn generate_mfa_secret() -> String {
    let mut rng = rand::thread_rng();
    let mut bytes = [0u8; 20]; // 160 bits of entropy
    rng.fill(&mut bytes);
    
    base32::encode(
        base32::Alphabet::RFC4648 { padding: true },
        &bytes
    )
}

// Create MFA setup for user
async fn setup_mfa(
    user_id: i64,
    db: &DbPool,
) -> Result<(String, String), Error> {
    // Generate secure secret
    let secret = generate_mfa_secret();
    
    // Hash secret for storage - we'll hash it again for verification
    let hashed_secret = hash_totp_secret(&secret)?;
    
    // Store hashed secret in database
    sqlx::query!(
        "UPDATE users SET mfa_secret = $1, mfa_enabled = true WHERE id = $2",
        hashed_secret,
        user_id
    )
    .execute(db)
    .await?;
    
    // Get user details for URI
    let user = get_user(db, user_id).await?;
    
    // Create TOTP instance
    let totp = TOTP::new(
        Algorithm::SHA1,         // Industry standard - compatible with most authenticator apps
        6,                       // 6-digit code
        1,                       // 30-second period step size
        30,                      // 30-second period
        Secret::Encoded(secret.clone()).to_bytes().unwrap(),
        Some("Rusty Server".to_string()), // Issuer
        user.username.clone(),   // Account name
    ).unwrap();
    
    // Generate URI for QR code
    let uri = totp.get_url();
    
    // Return the secret (for manual entry) and URI (for QR code)
    Ok((secret, uri))
}

// Verify MFA code with secure practices
async fn verify_mfa_code(
    user_id: i64, 
    code: &str,
    db: &DbPool,
) -> Result<bool, Error> {
    // Validate code format
    if !is_valid_mfa_code_format(code) {
        return Ok(false);
    }

    // Get user's secret from database
    let user = sqlx::query!("SELECT mfa_secret FROM users WHERE id = $1", user_id)
        .fetch_one(db)
        .await?;
    
    if let Some(hashed_secret) = user.mfa_secret {
        // Verify code against all potential secrets to avoid timing attacks
        let valid_secrets = get_valid_secrets(&hashed_secret)?;
        
        // Check each potential secret (multiple time steps)
        for secret in valid_secrets {
            let totp = TOTP::new(
                Algorithm::SHA1,
                6,
                1,
                30,
                secret,
                None,
                "".to_string(),
            ).unwrap();
            
            // Check code - includes current period and one period before/after
            if totp.check_current(code).unwrap_or(false) {
                return Ok(true);
            }
        }
    }
    
    // Code is invalid
    Ok(false)
}

// Check if MFA code has valid format
fn is_valid_mfa_code_format(code: &str) -> bool {
    // MFA codes are typically 6 digits
    code.len() == 6 && code.chars().all(|c| c.is_ascii_digit())
}

// Login with MFA protection
async fn mfa_protected_login(
    req: HttpRequest,
    session: Session,
    form: web::Form<MfaLoginForm>,
    db: web::Data<DbPool>,
) -> Result<HttpResponse, Error> {
    // First verify username and password
    let user = authenticate_user(&form.username, &form.password, &db).await?;
    
    if user.mfa_enabled {
        if let Some(mfa_code) = &form.mfa_code {
            // Verify MFA code
            let valid_mfa = verify_mfa_code(user.id, mfa_code, &db).await?;
            
            if !valid_mfa {
                // Invalid MFA code
                log_security_event(
                    SecurityEventType::FailedLogin,
                    Some(&user.id.to_string()),
                    json!({
                        "error": "Invalid MFA code",
                        "username": form.username
                    }),
                    &req
                );
                
                return Ok(HttpResponse::Unauthorized().json(json!({
                    "error": "Invalid MFA code"
                })));
            }
        } else {
            // MFA code required but not provided
            return Ok(HttpResponse::Unauthorized().json(json!({
                "error": "MFA code required",
                "mfa_required": true
            })));
        }
    }
    
    // Both authentication stages passed
    session.renew(); // Generate new session ID to prevent session fixation
    session.insert("user_id", user.id)?;
    
    // Log successful login with MFA status
    log_security_event(
        SecurityEventType::Login,
        Some(&user.id.to_string()),
        json!({
            "username": form.username,
            "mfa_used": user.mfa_enabled
        }),
        &req
    );
    
    Ok(HttpResponse::Ok().finish())
}
```

### 9. OAuth/OpenID Security Configuration

Implement secure OAuth client settings:

```rust
use oauth2::{
    AuthUrl, ClientId, ClientSecret, TokenUrl, RedirectUrl,
    AuthorizationCode, PkceCodeChallenge, PkceCodeVerifier,
    CsrfToken, Scope, TokenResponse, StandardTokenResponse,
    basic::BasicClient, reqwest::http_client,
};
use openid::Claims;

// Create secure OAuth client for Google
fn create_google_oauth_client() -> BasicClient {
    BasicClient::new(
        ClientId::new(env::var("GOOGLE_CLIENT_ID").unwrap_or_default()),
        Some(ClientSecret::new(env::var("GOOGLE_CLIENT_SECRET").unwrap_or_default())),
        AuthUrl::new("https://accounts.google.com/o/oauth2/v2/auth".to_string()).unwrap(),
        Some(TokenUrl::new("https://oauth2.googleapis.com/token".to_string()).unwrap())
    )
    .set_redirect_uri(RedirectUrl::new(format!(
        "{}/auth/google/callback", 
        env::var("APP_URL").unwrap_or_default()
    )).unwrap())
    // Enable PKCE for public clients (recommended)
    .set_pkce_challenge_method(oauth2::PkceS256ChallengeMethod::Sha256)
}

// Start OAuth flow with PKCE and state parameter
async fn start_oauth_flow(session: Session) -> Result<HttpResponse, Error> {
    // Generate PKCE challenge
    let (pkce_challenge, pkce_verifier) = PkceCodeChallenge::new_random_sha256();
    
    // Store PKCE verifier in session (server side)
    session.insert("pkce_verifier", pkce_verifier.secret().clone())?;
    
    // Generate CSRF token
    let csrf_token = CsrfToken::new_random();
    session.insert("oauth_csrf", csrf_token.secret().clone())?;
    
    // Get OAuth client
    let client = create_google_oauth_client();
    
    // Generate authorization URL
    let (auth_url, _) = client
        .authorize_url(|| csrf_token)
        // Request OpenID Connect scopes
        .add_scope(Scope::new("openid".to_string()))
        .add_scope(Scope::new("email".to_string()))
        .add_scope(Scope::new("profile".to_string()))
        // Add PKCE challenge
        .set_pkce_challenge(pkce_challenge)
        .url();
    
    // Store timestamp to detect excessively long flows
    session.insert("oauth_start_time", SystemTime::now().elapsed().unwrap().as_secs())?;
    
    // Redirect to authorization URL
    Ok(HttpResponse::Found()
        .append_header((header::LOCATION, auth_url.to_string()))
        .finish())
}

// Handle OAuth callback with security validations
async fn oauth_callback(
    req: HttpRequest,
    params: web::Query<OAuthCallbackParams>,
    session: Session,
    db: web::Data<DbPool>,
) -> Result<HttpResponse, Error> {
    // Check for authorization code
    let code = match params.code.as_deref() {
        Some(code) => AuthorizationCode::new(code.to_string()),
        None => {
            log_security_event(
                SecurityEventType::FailedLogin,
                None,
                json!({
                    "error": "Missing authorization code in OAuth callback",
                    "params": params
                }),
                &req
            );
            return Ok(HttpResponse::BadRequest().body("Invalid OAuth callback"));
        }
    };
    
    // Validate state parameter (CSRF protection)
    let stored_csrf = session.get::<String>("oauth_csrf")?;
    let state_param = params.state.as_deref().unwrap_or("");
    
    if stored_csrf.as_deref() != Some(state_param) {
        log_security_event(
            SecurityEventType::FailedLogin,
            None,
            json!({
                "error": "Invalid CSRF state in OAuth callback",
                "expected": stored_csrf,
                "received": state_param
            }),
            &req
        );
        return Ok(HttpResponse::BadRequest().body("Invalid state parameter"));
    }
    
    // Check flow timeout
    if let Some(start_time) = session.get::<u64>("oauth_start_time")? {
        let current = SystemTime::now().elapsed().unwrap().as_secs();
        if current - start_time > 300 { // 5 minute timeout
            log_security_event(
                SecurityEventType::FailedLogin,
                None,
                json!({
                    "error": "OAuth flow timeout",
                    "elapsed_seconds": current - start_time
                }),
                &req
            );
            return Ok(HttpResponse::BadRequest().body("Authentication flow expired"));
        }
    }
    
    // Get PKCE verifier
    let pkce_verifier = match session.get::<String>("pkce_verifier")? {
        Some(v) => PkceCodeVerifier::new(v),
        None => {
            log_security_event(
                SecurityEventType::FailedLogin,
                None,
                json!({
                    "error": "Missing PKCE verifier in session"
                }),
                &req
            );
            return Ok(HttpResponse::BadRequest().body("Invalid authentication flow"));
        }
    };
    
    // Clear OAuth session data immediately after use
    session.remove("pkce_verifier");
    session.remove("oauth_csrf");
    session.remove("oauth_start_time");
    
    // Exchange code for token with PKCE verification
    let client = create_google_oauth_client();
    let token_result = client
        .exchange_code(code)
        .set_pkce_verifier(pkce_verifier)
        .request(http_client)
        .await;
    
    let token = match token_result {
        Ok(t) => t,
        Err(e) => {
            log_security_event(
                SecurityEventType::FailedLogin,
                None,
                json!({
                    "error": "Failed to exchange OAuth code",
                    "error_details": e.to_string()
                }),
                &req
            );
            return Ok(HttpResponse::InternalServerError().body("Authentication failed"));
        }
    };
    
    // Verify ID token if present (OpenID Connect)
    if let Some(id_token) = token.id_token() {
        // Parse and validate the ID token
        let claims: Claims = validate_id_token(id_token)?;
        
        // Find or create user
        let user = find_or_create_oauth_user(&db, &claims).await?;
        
        // Log successful OAuth login
        log_security_event(
            SecurityEventType::Login,
            Some(&user.id.to_string()),
            json!({
                "provider": "google",
                "email": claims.email
            }),
            &req
        );
        
        // Create new session
        session.renew();
        session.insert("user_id", user.id)?;
        
        // Redirect to dashboard
        return Ok(HttpResponse::Found()
            .append_header((header::LOCATION, "/dashboard"))
            .finish());
    }
    
    // Should not reach here for OpenID Connect flows
    Ok(HttpResponse::InternalServerError().body("Authentication failed"))
}
```

### 10. Secure Integration of Authentication Components

Implement a comprehensive authentication workflow:

```mermaid
flowchart TD
    A[Client Request] --> B{Has Session?}
    B -->|Yes| C{Session Valid?}
    B -->|No| D[Login Required]
    
    C -->|Yes| E{Access Authorized?}
    C -->|No| F[Session Invalid]
    
    E -->|Yes| G[Access Granted]
    E -->|No| H[Permission Denied]
    
    D --> I{Authentication Type?}
    
    I -->|Password| J[Password Login]
    I -->|OAuth| K[OAuth Flow]
    
    J --> L{MFA Required?}
    K --> M[Process OAuth]
    
    L -->|Yes| N[Verify MFA]
    L -->|No| O[Create Session]
    
    N --> O
    M --> O
    
    O --> P[Apply Rate Limiting]
    F --> P
    
    P --> Q[Log Security Event]
    H --> Q
    G --> Q
```

By implementing these security measures, your authentication and authorization systems will be protected against common attack vectors while providing robust and secure user identity management.
```

## Best Practices

1. **Use Strong Password Hashing**
   - Use Argon2id for password hashing (winner of PHC)
   - Set appropriate parameters for your hardware (memory, iterations)
   - Never store passwords in plaintext or with weak hashes (MD5, SHA1)

2. **Implement Proper Token Management**
   - Use short-lived JWTs (1 hour or less) with refresh tokens
   - Store tokens securely (HttpOnly, Secure cookies)
   - Implement token revocation for logout

3. **Apply the Principle of Least Privilege**
   - Grant the minimum permissions necessary
   - Regularly audit user permissions and roles
   - Implement time-based access for sensitive operations

4. **Secure Headers and Cookies**
   - Set SameSite=Strict for cookies to prevent CSRF
   - Use Content-Security-Policy to prevent XSS
   - Implement HTTP Strict Transport Security (HSTS)

5. **Implement Multi-Factor Authentication**
   - Offer MFA for all users, especially admins
   - Support app-based TOTP (like Google Authenticator)
   - Consider WebAuthn for passwordless authentication

6. **Monitor and Log Authentication Events**
   - Log all authentication attempts (success and failure)
   - Alert on suspicious patterns (multiple failures)
   - Implement IP-based blocking after repeated failures

## Knowledge Check

1. **What's the primary difference between authentication and authorization?**
   - Authentication verifies who a user is, while authorization determines what they are allowed to do.

2. **Why is it important to use a specialized password hashing algorithm like Argon2 instead of a general-purpose hash function like SHA-256?**
   - Password hashing algorithms are deliberately slow and resource-intensive to resist brute force attacks, and they include salts to prevent rainbow table attacks.

3. **What security benefits does JWT provide, and what are its limitations?**
   - Benefits: stateless, self-contained, can include claims. Limitations: can't be invalidated before expiration, size limitations, vulnerabilities if not properly implemented.

4. **Why is MFA considered more secure than single-factor authentication?**
   - It requires something you know (password) plus something you have (device), making attacks significantly harder even if one factor is compromised.

## Integration

- Integrate authentication with your session management system (Module 19)
- Use authentication results to feed into your middleware pipeline (Module 25)
- Apply rate limiting to login attempts to prevent brute force (Module 21)
- Ensure authorization checks are performed before processing requests

## Additional Resources

- [OWASP Authentication Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
- [Rust Argon2 Crate Documentation](https://docs.rs/argon2/latest/argon2/)
- [JWT.io](https://jwt.io/) - Debugger and library information for JWT
- [OAuth 2.0 Simplified](https://aaronparecki.com/oauth-2-simplified/)

[Previous: Session Management](19-session-management.md) | [Next: Rate Limiting and DoS Protection](21-rate-limiting-dos.md)

## Quiz
1. What is the difference between authentication and authorization?
2. Name a Rust crate for JWT support.

## Diagram
```mermaid
graph TD
    A[Client] -- Login --> B[Webserver]
    B -- JWT --> A
    B -- Role Check --> C[Resource]
```
