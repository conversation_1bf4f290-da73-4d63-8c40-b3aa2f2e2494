# Middleware System

## Learning Objectives
- Understand the concept and importance of middleware in webservers
- Design and implement custom middleware components in Rust
- Apply the middleware pattern for cross-cutting concerns
- Create middleware chains with proper ordering
- Integrate middleware with your webserver architecture

## Prerequisites
- Understanding of HTTP request/response cycle
- Familiarity with Rust traits and async programming
- Basic knowledge of web server architecture

## Introduction

Middleware is a powerful concept in webserver architecture that allows for processing requests and responses in a modular, composable way. Middleware components sit between the client and the application's main handlers, enabling cross-cutting concerns like logging, authentication, compression, and more to be handled separately from core business logic.

## Middleware Fundamentals

Middleware functions typically:
1. Receive a request
2. Perform pre-processing on the request (optional)
3. Pass the request to the next component in the chain
4. Receive the response from downstream components
5. Perform post-processing on the response (optional)
6. Return the response

This pattern enables a clean separation of concerns and allows for modular, reusable components.

## Implementing a Middleware System in Rust

### 1. Basic Middleware Trait

Let's start by defining a middleware trait that can be implemented by different middleware components:

```rust
use std::future::Future;
use std::pin::Pin;
use std::task::{Context, Poll};
use http::{Request, Response};

// The core middleware trait
pub trait Middleware<B> {
    // Process a request and produce a response
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>>;
}

// Type alias for the next middleware in the chain
pub type Next<'a, B> = Box<
    dyn FnOnce(Request<B>) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> + Send + 'a
>;
```

### 2. Middleware Chain

Now, let's implement a middleware chain that can hold and execute multiple middleware components:

```rust
use futures::future::BoxFuture;
use std::sync::Arc;

// A chain of middleware components
pub struct MiddlewareChain<B> {
    middlewares: Vec<Arc<dyn Middleware<B> + Send + Sync>>,
}

impl<B: 'static + Send> MiddlewareChain<B> {
    pub fn new() -> Self {
        Self {
            middlewares: Vec::new(),
        }
    }
    
    // Add a middleware to the chain
    pub fn add<M>(&mut self, middleware: M) -> &mut Self
    where
        M: Middleware<B> + Send + Sync + 'static,
    {
        self.middlewares.push(Arc::new(middleware));
        self
    }
    
    // Execute the middleware chain
    pub fn run(
        &self,
        req: Request<B>,
        endpoint: impl Fn(Request<B>) -> BoxFuture<'static, Response<B>> + Send + Sync + 'static,
    ) -> BoxFuture<'static, Response<B>> {
        let mut chain = self.middlewares.clone();
        chain.reverse(); // Reverse so we can pop from the end
        
        // Create nested middleware calls
        let handler = chain.into_iter().fold(
            Box::new(endpoint) as Box<dyn Fn(Request<B>) -> BoxFuture<'static, Response<B>> + Send + Sync>,
            |next, middleware| {
                Box::new(move |req| {
                    let middleware = middleware.clone();
                    let next = Box::new(move |req| next(req));
                    middleware.handle(req, next)
                })
            },
        );
        
        // Run the chain with the request
        handler(req)
    }
}
```

### 3. Common Middleware Implementations

#### Logging Middleware

```rust
use log::{info, warn};
use std::time::Instant;

// Middleware that logs request information
pub struct LoggingMiddleware;

impl<B: Send> Middleware<B> for LoggingMiddleware {
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Pre-processing: Log the request
            let start = Instant::now();
            let method = request.method().clone();
            let uri = request.uri().clone();
            
            info!("Request started: {} {}", method, uri);
            
            // Call the next middleware or handler
            let response = next(request).await;
            
            // Post-processing: Log the response
            let status = response.status();
            let duration = start.elapsed();
            
            info!(
                "Request completed: {} {} - {} in {:?}",
                method, uri, status, duration
            );
            
            response
        })
    }
}
```

#### CORS Middleware

```rust
use http::{header, Method, HeaderValue};

// Middleware for Cross-Origin Resource Sharing
pub struct CorsMiddleware {
    allow_origins: Vec<String>,
    allow_methods: Vec<Method>,
    allow_headers: Vec<String>,
    allow_credentials: bool,
    max_age: Option<u32>,
}

impl CorsMiddleware {
    pub fn new() -> Self {
        Self {
            allow_origins: vec!["*".to_string()],
            allow_methods: vec![Method::GET, Method::POST, Method::PUT, Method::DELETE],
            allow_headers: vec![
                "Content-Type".to_string(),
                "Authorization".to_string(),
                "Accept".to_string(),
            ],
            allow_credentials: false,
            max_age: Some(86400), // 24 hours
        }
    }
    
    // Builder methods for configuration
    pub fn allow_origin(mut self, origin: impl Into<String>) -> Self {
        self.allow_origins = vec![origin.into()];
        self
    }
    
    pub fn allow_origins(mut self, origins: Vec<String>) -> Self {
        self.allow_origins = origins;
        self
    }
    
    pub fn allow_method(mut self, method: Method) -> Self {
        self.allow_methods.push(method);
        self
    }
    
    pub fn allow_credentials(mut self, allow: bool) -> Self {
        self.allow_credentials = allow;
        self
    }
}

impl<B: Send> Middleware<B> for CorsMiddleware {
    fn handle<'a>(
        &'a self,
        mut request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Get the origin from the request
            let origin = request
                .headers()
                .get(header::ORIGIN)
                .and_then(|v| v.to_str().ok())
                .unwrap_or("");
                
            // Check if this is a preflight OPTIONS request
            let is_preflight = request.method() == Method::OPTIONS &&
                request.headers().contains_key(header::ACCESS_CONTROL_REQUEST_METHOD);
                
            // Handle preflight request
            if is_preflight {
                let mut response = Response::builder()
                    .status(200)
                    .body(().into())
                    .unwrap();
                    
                let headers = response.headers_mut();
                
                // Set CORS headers for preflight
                if self.allow_origins.contains(&"*".to_string()) || self.allow_origins.contains(&origin.to_string()) {
                    headers.insert(
                        header::ACCESS_CONTROL_ALLOW_ORIGIN,
                        HeaderValue::from_str(origin).unwrap(),
                    );
                }
                
                let methods = self.allow_methods
                    .iter()
                    .map(|m| m.as_str())
                    .collect::<Vec<_>>()
                    .join(", ");
                    
                headers.insert(
                    header::ACCESS_CONTROL_ALLOW_METHODS,
                    HeaderValue::from_str(&methods).unwrap(),
                );
                
                let headers_list = self.allow_headers
                    .join(", ");
                    
                headers.insert(
                    header::ACCESS_CONTROL_ALLOW_HEADERS,
                    HeaderValue::from_str(&headers_list).unwrap(),
                );
                
                if self.allow_credentials {
                    headers.insert(
                        header::ACCESS_CONTROL_ALLOW_CREDENTIALS,
                        HeaderValue::from_static("true"),
                    );
                }
                
                if let Some(max_age) = self.max_age {
                    headers.insert(
                        header::ACCESS_CONTROL_MAX_AGE,
                        HeaderValue::from(max_age),
                    );
                }
                
                return response;
            }
            
            // Process normal request
            let mut response = next(request).await;
            
            // Add CORS headers to response
            let headers = response.headers_mut();
            
            if self.allow_origins.contains(&"*".to_string()) || self.allow_origins.contains(&origin.to_string()) {
                headers.insert(
                    header::ACCESS_CONTROL_ALLOW_ORIGIN,
                    HeaderValue::from_str(origin).unwrap(),
                );
            }
            
            if self.allow_credentials {
                headers.insert(
                    header::ACCESS_CONTROL_ALLOW_CREDENTIALS,
                    HeaderValue::from_static("true"),
                );
            }
            
            response
        })
    }
}
```

#### Authentication Middleware

```rust
use http::{StatusCode, header};
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};
use serde::{Deserialize, Serialize};

// JWT Claims structure
#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,    // Subject (user ID)
    exp: usize,     // Expiration time
    role: String,   // User role
}

// Authentication middleware
pub struct AuthMiddleware {
    jwt_secret: String,
}

impl AuthMiddleware {
    pub fn new(jwt_secret: impl Into<String>) -> Self {
        Self {
            jwt_secret: jwt_secret.into(),
        }
    }
}

impl<B: Send> Middleware<B> for AuthMiddleware {
    fn handle<'a>(
        &'a self,
        mut request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Check for Authorization header
            let auth_header = match request.headers().get(header::AUTHORIZATION) {
                Some(header) => header,
                None => {
                    return Response::builder()
                        .status(StatusCode::UNAUTHORIZED)
                        .body(().into())
                        .unwrap();
                }
            };
            
            // Extract token from header
            let auth_str = match auth_header.to_str() {
                Ok(s) => s,
                Err(_) => {
                    return Response::builder()
                        .status(StatusCode::UNAUTHORIZED)
                        .body(().into())
                        .unwrap();
                }
            };
            
            // Check for "Bearer " prefix
            if !auth_str.starts_with("Bearer ") {
                return Response::builder()
                    .status(StatusCode::UNAUTHORIZED)
                    .body(().into())
                    .unwrap();
            }
            
            let token = &auth_str[7..]; // Remove "Bearer " prefix
            
            // Validate JWT
            let validation = Validation::new(Algorithm::HS256);
            let token_data = match decode::<Claims>(
                token,
                &DecodingKey::from_secret(self.jwt_secret.as_bytes()),
                &validation,
            ) {
                Ok(data) => data,
                Err(err) => {
                    log::warn!("JWT validation failed: {}", err);
                    return Response::builder()
                        .status(StatusCode::UNAUTHORIZED)
                        .body(().into())
                        .unwrap();
                }
            };
            
            // Add user info to request extensions for downstream middleware/handlers
            request.extensions_mut().insert(token_data.claims);
            
            // Continue to next middleware or handler
            next(request).await
        })
    }
}
```

#### Compression Middleware

```rust
use async_compression::tokio::bufread::GzipEncoder;
use futures::TryStreamExt;
use http::header;
use tokio_util::io::StreamReader;
use tokio::io::AsyncReadExt;

pub struct CompressionMiddleware {
    min_size: usize,  // Minimum size for compression
}

impl CompressionMiddleware {
    pub fn new(min_size: usize) -> Self {
        Self { min_size }
    }
}

impl<B: Send + 'static> Middleware<B> for CompressionMiddleware {
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Check if client accepts gzip encoding
            let accepts_gzip = request
                .headers()
                .get(header::ACCEPT_ENCODING)
                .and_then(|h| h.to_str().ok())
                .map(|h| h.contains("gzip"))
                .unwrap_or(false);

            // Process the request through the rest of the chain
            let mut response = next(request).await;
            
            // Only compress if client accepts it and response is of suitable type
            if accepts_gzip {
                let content_type = response
                    .headers()
                    .get(header::CONTENT_TYPE)
                    .and_then(|h| h.to_str().ok())
                    .unwrap_or("");
                    
                // Only compress text, JSON, etc.
                let should_compress = content_type.starts_with("text/") || 
                    content_type == "application/json" || 
                    content_type == "application/xml" ||
                    content_type.starts_with("application/");
                    
                if should_compress {
                    // Check content length
                    if let Some(len) = response.headers().get(header::CONTENT_LENGTH) {
                        if let Ok(len) = len.to_str().ok().and_then(|s| s.parse::<usize>().ok()) {
                            if len >= self.min_size {
                                // Compress the response body
                                // Note: This is a simplified example. 
                                // In a real implementation, you would handle streaming responses
                                response.headers_mut().insert(
                                    header::CONTENT_ENCODING,
                                    HeaderValue::from_static("gzip"),
                                );
                                // Remove content-length as it will change
                                response.headers_mut().remove(header::CONTENT_LENGTH);
                            }
                        }
                    }
                }
            }
            
            response
        })
    }
}
```

#### Rate Limiting Middleware

```rust
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

// Rate limiting configuration
struct RateLimit {
    max_requests: u32,
    window_seconds: u64,
}

// Rate limiting state for a client
struct ClientRateLimit {
    window_start: Instant,
    request_count: u32,
}

// Rate limiting middleware
pub struct RateLimitMiddleware {
    limits: Arc<RwLock<HashMap<String, ClientRateLimit>>>,
    config: RateLimit,
}

impl RateLimitMiddleware {
    pub fn new(max_requests: u32, window_seconds: u64) -> Self {
        Self {
            limits: Arc::new(RwLock::new(HashMap::new())),
            config: RateLimit {
                max_requests,
                window_seconds,
            },
        }
    }
    
    // Extract client identifier (typically IP address)
    fn get_client_id(req: &Request<B>) -> String {
        // In a real implementation, you would use X-Forwarded-For, etc.
        req.extensions()
            .get::<String>()
            .cloned()
            .unwrap_or_else(|| "unknown".to_string())
    }
}

impl<B: Send> Middleware<B> for RateLimitMiddleware {
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        let limits = self.limits.clone();
        let config = self.config.clone();
        
        Box::pin(async move {
            let client_id = Self::get_client_id(&request);
            let now = Instant::now();
            
            // Check and update rate limit
            let is_rate_limited = {
                let mut limits = limits.write().await;
                
                // Get or create client entry
                let entry = limits.entry(client_id).or_insert_with(|| ClientRateLimit {
                    window_start: now,
                    request_count: 0,
                });
                
                // Reset if window has expired
                if now.duration_since(entry.window_start) > Duration::from_secs(config.window_seconds) {
                    entry.window_start = now;
                    entry.request_count = 1;
                    false
                } else {
                    // Increment count and check limit
                    entry.request_count += 1;
                    entry.request_count > config.max_requests
                }
            };
            
            // Return 429 Too Many Requests if rate limited
            if is_rate_limited {
                return Response::builder()
                    .status(StatusCode::TOO_MANY_REQUESTS)
                    .header(
                        "Retry-After", 
                        config.window_seconds.to_string()
                    )
                    .body(().into())
                    .unwrap();
            }
            
            // Continue to next middleware or handler
            next(request).await
        })
    }
}
```

### 4. Integrating Middleware with a Web Server

Let's see how to integrate our middleware system with a simple web server:

```rust
use std::net::SocketAddr;
use tokio::net::TcpListener;
use http::{Request, Response, StatusCode};

// Simple HTTP server with middleware support
struct HttpServer {
    middleware_chain: MiddlewareChain<Vec<u8>>,
}

impl HttpServer {
    pub fn new() -> Self {
        Self {
            middleware_chain: MiddlewareChain::new(),
        }
    }
    
    // Add middleware to the chain
    pub fn with<M>(&mut self, middleware: M) -> &mut Self 
    where
        M: Middleware<Vec<u8>> + Send + Sync + 'static,
    {
        self.middleware_chain.add(middleware);
        self
    }
    
    // Start the server
    pub async fn listen(&self, addr: &str) -> Result<(), Box<dyn std::error::Error>> {
        let listener = TcpListener::bind(addr).await?;
        println!("Server listening on {}", addr);
        
        loop {
            let (socket, _) = listener.accept().await?;
            let middleware_chain = self.middleware_chain.clone();
            
            // Spawn a new task for each connection
            tokio::spawn(async move {
                // Process the connection (simplified)
                let request = http::Request::builder()
                    .uri("/")
                    .body(Vec::new())
                    .unwrap();
                
                // Run request through middleware chain to handler
                let response = middleware_chain.run(request, |req| {
                    // Final handler
                    Box::pin(async move {
                        Response::builder()
                            .status(StatusCode::OK)
                            .body(b"Hello, World!".to_vec())
                            .unwrap()
                    })
                }).await;
                
                // Send response (simplified)
                // ...
            });
        }
    }
}

// Example usage
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create and configure server with middleware
    let mut server = HttpServer::new();
    
    server
        .with(LoggingMiddleware)
        .with(CorsMiddleware::new())
        .with(CompressionMiddleware::new(1024))
        .with(RateLimitMiddleware::new(100, 60));
        
    // Authentication middleware only for specific paths
    // server.with(AuthMiddleware::new("secret_key").paths(vec!["/api/private"]));
    
    // Start server
    server.listen("127.0.0.1:8080").await?;
    
    Ok(())
}
```

## Advanced Middleware Patterns

### 1. Conditional Middleware

Execute middleware only in certain conditions:

```rust
pub struct ConditionalMiddleware<M, F> {
    middleware: M,
    condition: F,
}

impl<M, F, B> ConditionalMiddleware<M, F>
where
    M: Middleware<B>,
    F: Fn(&Request<B>) -> bool + Send + Sync,
    B: Send,
{
    pub fn new(middleware: M, condition: F) -> Self {
        Self { middleware, condition }
    }
}

impl<M, F, B> Middleware<B> for ConditionalMiddleware<M, F>
where
    M: Middleware<B> + Send + Sync,
    F: Fn(&Request<B>) -> bool + Send + Sync,
    B: Send,
{
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        // Check condition
        if (self.condition)(&request) {
            // Execute middleware if condition is met
            self.middleware.handle(request, next)
        } else {
            // Skip middleware if condition is not met
            next(request)
        }
    }
}

// Usage example
server.with(ConditionalMiddleware::new(
    LoggingMiddleware,
    |req| req.uri().path().starts_with("/api")
));
```

### 2. Path-Based Middleware

Apply middleware only to specific paths:

```rust
pub struct PathMiddleware<M> {
    middleware: M,
    paths: Vec<String>,
}

impl<M, B> PathMiddleware<M>
where
    M: Middleware<B>,
    B: Send,
{
    pub fn new(middleware: M, paths: Vec<String>) -> Self {
        Self { middleware, paths }
    }
}

impl<M, B> Middleware<B> for PathMiddleware<M>
where
    M: Middleware<B> + Send + Sync,
    B: Send,
{
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        // Check if the request path matches any in our list
        let path = request.uri().path();
        let matches = self.paths.iter().any(|p| {
            if p.ends_with('*') {
                // Handle wildcard paths
                let prefix = &p[..p.len() - 1];
                path.starts_with(prefix)
            } else {
                // Exact path match
                path == p
            }
        });
        
        if matches {
            // Apply middleware
            self.middleware.handle(request, next)
        } else {
            // Skip middleware
            next(request)
        }
    }
}

// Usage example
server.with(PathMiddleware::new(
    AuthMiddleware::new("secret_key"),
    vec!["/api/private/*".to_string(), "/admin".to_string()]
));
```

### 3. Method-Based Middleware

Apply middleware only to specific HTTP methods:

```rust
pub struct MethodMiddleware<M> {
    middleware: M,
    methods: Vec<Method>,
}

impl<M, B> MethodMiddleware<M>
where
    M: Middleware<B>,
    B: Send,
{
    pub fn new(middleware: M, methods: Vec<Method>) -> Self {
        Self { middleware, methods }
    }
}

impl<M, B> Middleware<B> for MethodMiddleware<M>
where
    M: Middleware<B> + Send + Sync,
    B: Send,
{
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        // Check if the request method matches any in our list
        if self.methods.contains(request.method()) {
            // Apply middleware
            self.middleware.handle(request, next)
        } else {
            // Skip middleware
            next(request)
        }
    }
}

// Usage example
server.with(MethodMiddleware::new(
    ValidateJsonMiddleware,
    vec![Method::POST, Method::PUT, Method::PATCH]
));
```

### 4. Error Recovery Middleware

Catch errors and provide fallback responses:

```rust
pub struct RecoveryMiddleware;

impl<B: Send> Middleware<B> for RecoveryMiddleware {
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Use a future to catch panics
            let result = std::panic::AssertUnwindSafe(next(request)).catch_unwind().await;
            
            match result {
                Ok(response) => response,
                Err(err) => {
                    // Log the error
                    if let Some(error_str) = err.downcast_ref::<String>() {
                        log::error!("Panic in handler: {}", error_str);
                    } else {
                        log::error!("Panic in handler: Unknown error");
                    }
                    
                    // Return 500 Internal Server Error
                    Response::builder()
                        .status(StatusCode::INTERNAL_SERVER_ERROR)
                        .body(().into())
                        .unwrap()
                }
            }
        })
    }
}
```

## Testing Middleware

Unit testing middleware components is important to ensure they function correctly:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use http::{Request, Response, StatusCode};
    
    #[tokio::test]
    async fn test_logging_middleware() {
        // Create middleware
        let middleware = LoggingMiddleware;
        
        // Create a request
        let request = Request::builder()
            .uri("/test")
            .body(())
            .unwrap();
            
        // Create a simple next handler
        let next = |req| {
            Box::pin(async {
                Response::builder()
                    .status(200)
                    .body(())
                    .unwrap()
            })
        };
        
        // Test middleware
        let response = middleware.handle(request, Box::new(next)).await;
        
        // Verify response
        assert_eq!(response.status(), StatusCode::OK);
    }
    
    #[tokio::test]
    async fn test_auth_middleware() {
        // Create middleware
        let middleware = AuthMiddleware::new("test_secret");
        
        // Create a request with invalid token
        let request = Request::builder()
            .uri("/private")
            .header("Authorization", "Bearer invalid_token")
            .body(())
            .unwrap();
            
        // Create a simple next handler
        let next = |req| {
            Box::pin(async {
                Response::builder()
                    .status(200)
                    .body(())
                    .unwrap()
            })
        };
        
        // Test middleware
        let response = middleware.handle(request, Box::new(next)).await;
        
        // Verify response shows unauthorized
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
        
        // Create a request with valid token (would require creating a real JWT)
        // ...
    }
}
```

## Best Practices for Middleware Design

1. **Single Responsibility Principle**: Each middleware should have a single, focused responsibility.

2. **Order Matters**: Be aware of middleware execution order:
   - Authentication should come before authorization
   - Logging often comes first to capture all requests
   - Compression should come near the end of the chain
   - Recovery middleware should be first to catch all errors

3. **Performance Considerations**:
   - Keep middleware lightweight
   - Use async code for I/O operations
   - Consider short-circuiting for efficiency (e.g., early auth rejections)

4. **Composability**:
   - Design middleware to be independently usable
   - Avoid tight coupling between middleware components
   - Use extensions to pass data between middleware

5. **Error Handling**:
   - Provide clear error responses
   - Log failures for debugging
   - Use recovery middleware to prevent crashes

6. **Configuration**:
   - Make middleware configurable
   - Use builder patterns for complex configuration
   - Provide sensible defaults

## Middleware Integration with Other Systems

### 1. Plugin System Integration

Middleware can be integrated with a plugin system for extensibility:

```rust
// Plugin trait with middleware support
pub trait Plugin {
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn register_middleware(&self, chain: &mut MiddlewareChain<Vec<u8>>);
}

// Plugin manager
pub struct PluginManager {
    plugins: Vec<Box<dyn Plugin>>,
}

impl PluginManager {
    pub fn new() -> Self {
        Self { plugins: Vec::new() }
    }
    
    pub fn register_plugin<P: Plugin + 'static>(&mut self, plugin: P) {
        self.plugins.push(Box::new(plugin));
    }
    
    pub fn configure_middleware(&self, chain: &mut MiddlewareChain<Vec<u8>>) {
        for plugin in &self.plugins {
            plugin.register_middleware(chain);
        }
    }
}

// Example plugin
pub struct LoggingPlugin;

impl Plugin for LoggingPlugin {
    fn name(&self) -> &str {
        "logging"
    }
    
    fn description(&self) -> &str {
        "Adds request and response logging"
    }
    
    fn register_middleware(&self, chain: &mut MiddlewareChain<Vec<u8>>) {
        chain.add(LoggingMiddleware);
    }
}

// Server with plugin support
impl HttpServer {
    pub fn with_plugins(&mut self, plugin_manager: &PluginManager) -> &mut Self {
        plugin_manager.configure_middleware(&mut self.middleware_chain);
        self
    }
}
```

### 2. Configuration System Integration

Middleware can be configured through a central configuration system:

```rust
use serde::Deserialize;

#[derive(Deserialize)]
pub struct ServerConfig {
    pub middleware: MiddlewareConfig,
}

#[derive(Deserialize)]
pub struct MiddlewareConfig {
    pub logging: bool,
    pub cors: CorsConfig,
    pub compression: CompressionConfig,
    pub rate_limit: RateLimitConfig,
    pub auth: Option<AuthConfig>,
}

#[derive(Deserialize)]
pub struct CorsConfig {
    pub enabled: bool,
    pub allow_origins: Vec<String>,
    pub allow_credentials: bool,
}

// Other config structs...

// Configure middleware from config file
fn configure_middleware(config: &ServerConfig) -> MiddlewareChain<Vec<u8>> {
    let mut chain = MiddlewareChain::new();
    
    // Always add recovery middleware first
    chain.add(RecoveryMiddleware);
    
    // Add logging if enabled
    if config.middleware.logging {
        chain.add(LoggingMiddleware);
    }
    
    // Add CORS if enabled
    if config.middleware.cors.enabled {
        let cors = CorsMiddleware::new()
            .allow_origins(config.middleware.cors.allow_origins.clone())
            .allow_credentials(config.middleware.cors.allow_credentials);
        chain.add(cors);
    }
    
    // Add other middleware based on config...
    
    chain
}
```

## Security Considerations

Middleware plays a critical role in implementing security controls within your web server. Properly designed security middleware can provide defense in depth, while poorly implemented middleware can introduce vulnerabilities. This section covers security considerations for middleware implementation.

### Threat Model for Middleware Systems

Understanding the threat landscape for middleware is essential for implementing proper security controls:

```mermaid
flowchart TD
    A[Attacker] -->|1. Middleware bypass| M[Middleware Chain]
    A -->|2. Middleware ordering attacks| M
    A -->|3. Dependency injection| M
    A -->|4. Middleware DoS| M
    A -->|5. Data tampering| M
    A -->|6. Information leakage| M
    
    M --> H[Request Handlers]
    H --> D[(Data Storage)]
    
    M -->|Response Interception| C[Client]
    
    subgraph "Middleware Weaknesses"
        M1[Missing Authorization]
        M2[Incorrect Validation]
        M3[Insecure Configuration]
        M4[Logic Flaws]
    end
    
    class A fill:#f96,stroke:#333
    class M fill:#69f,stroke:#333
    class H,D fill:#6d9,stroke:#333
    class C fill:#d8bfd8,stroke:#333
    class M1,M2,M3,M4 fill:#f9d0c4,stroke:#333
```

### Secure Middleware Implementation

Implementing security-focused middleware requires careful attention to design and implementation details:

```rust
use http::{Request, Response, StatusCode, header};
use std::sync::Arc;
use tokio::sync::Mutex;
use ring::hmac;
use std::collections::HashSet;
use futures::Future;
use std::pin::Pin;
use std::time::{Duration, Instant};

// Security middleware for protecting against common vulnerabilities
pub struct SecurityMiddleware {
    config: SecurityConfig,
    blocked_ips: Arc<Mutex<HashSet<String>>>,
    trusted_hosts: HashSet<String>,
    hmac_key: hmac::Key,
}

struct SecurityConfig {
    // Whether to enforce HTTPS
    enforce_https: bool,
    // Allowed HTTP methods
    allowed_methods: Vec<String>,
    // Maximum request size in bytes
    max_request_size: usize,
    // Maximum URI length
    max_uri_length: usize,
    // Maximum header count
    max_headers: usize,
    // Content Security Policy
    content_security_policy: String,
    // CSRF protection enabled
    csrf_protection: bool,
    // Apply cache control headers
    cache_control: bool,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            enforce_https: true,
            allowed_methods: vec!["GET".to_string(), "POST".to_string(), 
                               "PUT".to_string(), "DELETE".to_string(), 
                               "HEAD".to_string(), "OPTIONS".to_string()],
            max_request_size: 1024 * 1024, // 1MB
            max_uri_length: 2048,          // 2KB
            max_headers: 100,
            content_security_policy: "default-src 'self'".to_string(),
            csrf_protection: true,
            cache_control: true,
        }
    }
}

impl SecurityMiddleware {
    pub fn new(config: SecurityConfig, hmac_key: &[u8]) -> Self {
        Self {
            config,
            blocked_ips: Arc::new(Mutex::new(HashSet::new())),
            trusted_hosts: HashSet::new(),
            hmac_key: hmac::Key::new(hmac::HMAC_SHA256, hmac_key),
        }
    }
    
    pub fn with_trusted_hosts(mut self, hosts: Vec<String>) -> Self {
        self.trusted_hosts = hosts.into_iter().collect();
        self
    }
    
    // Add an IP to the blocklist
    pub async fn block_ip(&self, ip: String, duration: Duration) {
        let ip_clone = ip.clone();
        let blocked_ips = self.blocked_ips.clone();
        
        // Add IP to blocked list
        {
            let mut blocked = blocked_ips.lock().await;
            blocked.insert(ip);
        }
        
        // Schedule removal after duration
        tokio::spawn(async move {
            tokio::time::sleep(duration).await;
            
            let mut blocked = blocked_ips.lock().await;
            blocked.remove(&ip_clone);
        });
    }
    
    // Verify if a host is trusted
    fn is_trusted_host(&self, host: &str) -> bool {
        if self.trusted_hosts.is_empty() {
            return true; // No restrictions if no trusted hosts specified
        }
        
        self.trusted_hosts.contains(host)
    }
    
    // Verify CSRF token
    fn verify_csrf_token(&self, req: &Request<B>, token: &str) -> bool {
        // Get session ID from cookie (simplified)
        let session_id = req.headers()
            .get("cookie")
            .and_then(|c| c.to_str().ok())
            .and_then(|c| {
                c.split(';')
                    .find(|part| part.trim().starts_with("session="))
                    .map(|s| s.trim().trim_start_matches("session="))
            })
            .unwrap_or("");
            
        if session_id.is_empty() {
            return false;
        }
        
        // Calculate expected token
        let tag = hmac::sign(&self.hmac_key, session_id.as_bytes());
        let expected_token = base64::encode(tag.as_ref());
        
        // Constant time comparison to prevent timing attacks
        ring::constant_time::verify_slices_are_equal(
            expected_token.as_bytes(),
            token.as_bytes()
        ).is_ok()
    }
    
    // Check if request is over size limits
    fn check_request_limits(&self, req: &Request<B>) -> Result<(), StatusCode> {
        // Check URI length
        if req.uri().to_string().len() > self.config.max_uri_length {
            return Err(StatusCode::URI_TOO_LONG);
        }
        
        // Check header count
        if req.headers().len() > self.config.max_headers {
            return Err(StatusCode::BAD_REQUEST);
        }
        
        // Check content length if present
        if let Some(content_length) = req.headers().get(header::CONTENT_LENGTH) {
            if let Ok(length) = content_length.to_str().ok().and_then(|s| s.parse::<usize>().ok()) {
                if length > self.config.max_request_size {
                    return Err(StatusCode::PAYLOAD_TOO_LARGE);
                }
            }
        }
        
        Ok(())
    }
    
    // Add security headers to response
    fn add_security_headers(&self, res: &mut Response<B>) {
        let headers = res.headers_mut();
        
        // Add X-Content-Type-Options header
        headers.insert(
            header::HeaderName::from_static("x-content-type-options"),
            header::HeaderValue::from_static("nosniff")
        );
        
        // Add X-Frame-Options header
        headers.insert(
            header::HeaderName::from_static("x-frame-options"),
            header::HeaderValue::from_static("SAMEORIGIN")
        );
        
        // Add X-XSS-Protection header
        headers.insert(
            header::HeaderName::from_static("x-xss-protection"),
            header::HeaderValue::from_static("1; mode=block")
        );
        
        // Add Content-Security-Policy header
        headers.insert(
            header::HeaderName::from_static("content-security-policy"),
            header::HeaderValue::from_str(&self.config.content_security_policy).unwrap()
        );
        
        // Add Strict-Transport-Security header if HTTPS is enforced
        if self.config.enforce_https {
            headers.insert(
                header::HeaderName::from_static("strict-transport-security"),
                header::HeaderValue::from_static("max-age=31536000; includeSubDomains")
            );
        }
        
        // Add Referrer-Policy header
        headers.insert(
            header::HeaderName::from_static("referrer-policy"),
            header::HeaderValue::from_static("strict-origin-when-cross-origin")
        );
        
        // Add cache control headers if enabled
        if self.config.cache_control {
            headers.insert(
                header::CACHE_CONTROL,
                header::HeaderValue::from_static("no-store, max-age=0")
            );
            
            headers.insert(
                header::PRAGMA,
                header::HeaderValue::from_static("no-cache")
            );
        }
    }
}

impl<B: Send> Middleware<B> for SecurityMiddleware {
    fn handle<'a>(
        &'a self,
        req: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Extract client IP
            let client_ip = req
                .headers()
                .get("x-forwarded-for")
                .and_then(|h| h.to_str().ok())
                .unwrap_or("unknown")
                .split(',')
                .next()
                .unwrap_or("unknown")
                .trim()
                .to_string();
                
            // Check if IP is blocked
            {
                let blocked_ips = self.blocked_ips.lock().await;
                if blocked_ips.contains(&client_ip) {
                    return Response::builder()
                        .status(StatusCode::FORBIDDEN)
                        .body(().into())
                        .unwrap();
                }
            }
            
            // Check HTTP method
            let method = req.method().to_string();
            if !self.config.allowed_methods.iter().any(|m| m == &method) {
                return Response::builder()
                    .status(StatusCode::METHOD_NOT_ALLOWED)
                    .body(().into())
                    .unwrap();
            }
            
            // Check if HTTPS is required
            if self.config.enforce_https {
                let is_https = req
                    .headers()
                    .get("x-forwarded-proto")
                    .and_then(|h| h.to_str().ok())
                    .map(|proto| proto == "https")
                    .unwrap_or(false);
                    
                if !is_https {
                    return Response::builder()
                        .status(StatusCode::PERMANENT_REDIRECT)
                        .header(
                            header::LOCATION,
                            format!("https://{}{}", 
                                req.headers().get(header::HOST).and_then(|h| h.to_str().ok()).unwrap_or(""),
                                req.uri()
                            )
                        )
                        .body(().into())
                        .unwrap();
                }
            }
            
            // Check request limits
            if let Err(status) = self.check_request_limits(&req) {
                return Response::builder()
                    .status(status)
                    .body(().into())
                    .unwrap();
            }
            
            // Validate Host header against trusted hosts
            if let Some(host) = req.headers().get(header::HOST).and_then(|h| h.to_str().ok()) {
                if !self.is_trusted_host(host) {
                    return Response::builder()
                        .status(StatusCode::FORBIDDEN)
                        .body(().into())
                        .unwrap();
                }
            }
            
            // Check CSRF token for state-changing requests
            if self.config.csrf_protection && 
               (method == "POST" || method == "PUT" || method == "DELETE" || method == "PATCH") {
                let csrf_token = req
                    .headers()
                    .get("x-csrf-token")
                    .and_then(|h| h.to_str().ok())
                    .unwrap_or("");
                    
                if !self.verify_csrf_token(&req, csrf_token) {
                    // Log potential CSRF attempt
                    log::warn!("CSRF validation failed for request from {}", client_ip);
                    
                    // Return 403 Forbidden
                    return Response::builder()
                        .status(StatusCode::FORBIDDEN)
                        .body(().into())
                        .unwrap();
                }
            }
            
            // Process the request through the rest of the middleware chain
            let mut response = next(req).await;
            
            // Add security headers to the response
            self.add_security_headers(&mut response);
            
            response
        })
    }
}
```

### Security Middleware for Prevention of Common Vulnerabilities

Create specialized middleware to prevent common web vulnerabilities:

```rust
// XSS Protection Middleware
pub struct XssProtectionMiddleware {
    // HTML sanitizer configuration
    sanitizer: ammonia::Builder,
}

impl XssProtectionMiddleware {
    pub fn new() -> Self {
        let mut sanitizer = ammonia::Builder::default();
        
        // Configure HTML sanitizer to prevent XSS
        sanitizer
            .tags(hashset!["p", "br", "b", "i", "u", "strong", "em"])
            .clean_content_tags(hashset!["script", "style", "iframe"])
            .strip_comments(true)
            .link_rel(Some("noopener noreferrer nofollow"));
            
        Self { sanitizer }
    }
}

impl<B: Send + 'static> Middleware<B> for XssProtectionMiddleware {
    fn handle<'a>(
        &'a self,
        mut request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Process the request through the middleware chain
            let mut response = next(request).await;
            
            // Sanitize HTML content in responses
            if let Some(content_type) = response.headers().get(header::CONTENT_TYPE) {
                if let Ok(content_type_str) = content_type.to_str() {
                    if content_type_str.contains("text/html") || 
                       content_type_str.contains("application/xhtml+xml") {
                        // This is a simplified example - in a real implementation,
                        // you would need to handle the body in a streaming fashion
                        if let Some(body_bytes) = response.body_mut().as_mut() {
                            if let Ok(html) = std::str::from_utf8(body_bytes) {
                                let clean_html = self.sanitizer.clean(html).to_string();
                                *body_bytes = clean_html.into_bytes();
                            }
                        }
                    }
                }
            }
            
            response
        })
    }
}

// SQL Injection Protection Middleware
pub struct SqlInjectionProtectionMiddleware {
    patterns: Vec<regex::Regex>,
}

impl SqlInjectionProtectionMiddleware {
    pub fn new() -> Self {
        let patterns = vec![
            regex::Regex::new(r"(?i)(\b)(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|CREATE|TRUNCATE)(\s)").unwrap(),
            regex::Regex::new(r"(?i)((\b|'|\")OR(\b|'|\")(\s*)(\d|\w|'|\"|=)(\s*))").unwrap(),
            regex::Regex::new(r"(?i)((\b|'|\")AND(\b|'|\")(\s*)(\d|\w|'|\"|=)(\s*))").unwrap(),
            regex::Regex::new(r"(?i)((\b|'|\")UNION(\b|'|\")(\s*)(\d|\w|'|\"|=)(\s*))").unwrap(),
            regex::Regex::new(r"--").unwrap(),
            regex::Regex::new(r"/\*").unwrap(),
        ];
        
        Self { patterns }
    }
    
    // Check if a string contains SQL injection patterns
    fn contains_sql_injection(&self, input: &str) -> bool {
        self.patterns.iter().any(|pattern| pattern.is_match(input))
    }
}

impl<B: Send> Middleware<B> for SqlInjectionProtectionMiddleware {
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Check URL query parameters
            if let Some(query) = request.uri().query() {
                if self.contains_sql_injection(query) {
                    return Response::builder()
                        .status(StatusCode::BAD_REQUEST)
                        .body(().into())
                        .unwrap();
                }
            }
            
            // Check request headers
            for (name, value) in request.headers() {
                if let Ok(value_str) = value.to_str() {
                    if self.contains_sql_injection(value_str) {
                        return Response::builder()
                            .status(StatusCode::BAD_REQUEST)
                            .body(().into())
                            .unwrap();
                    }
                }
            }
            
            // Continue with request processing
            next(request).await
        })
    }
}

// JWT Authentication and Verification Middleware
pub struct JwtAuthMiddleware {
    jwt_secret: hmac::Key,
    required_paths: Vec<String>,
    exempt_paths: Vec<String>,
}

impl JwtAuthMiddleware {
    pub fn new(jwt_secret: &[u8]) -> Self {
        Self {
            jwt_secret: hmac::Key::new(hmac::HMAC_SHA256, jwt_secret),
            required_paths: Vec::new(),
            exempt_paths: Vec::new(),
        }
    }
    
    // Specify paths that require authentication
    pub fn require_auth(mut self, paths: Vec<String>) -> Self {
        self.required_paths = paths;
        self
    }
    
    // Specify paths that are exempt from authentication
    pub fn exempt(mut self, paths: Vec<String>) -> Self {
        self.exempt_paths = paths;
        self
    }
    
    // Check if a path requires authentication
    fn requires_auth(&self, path: &str) -> bool {
        // Check exemptions first
        if self.exempt_paths.iter().any(|p| {
            if p.ends_with('*') {
                path.starts_with(&p[..p.len() - 1])
            } else {
                path == p
            }
        }) {
            return false;
        }
        
        // If required paths is empty, all non-exempt paths require auth
        if self.required_paths.is_empty() {
            return true;
        }
        
        // Check if path matches any required paths
        self.required_paths.iter().any(|p| {
            if p.ends_with('*') {
                path.starts_with(&p[..p.len() - 1])
            } else {
                path == p
            }
        })
    }
    
    // Validate JWT token
    fn validate_token(&self, token: &str) -> Result<Claims, &'static str> {
        let parts: Vec<&str> = token.split('.').collect();
        if parts.len() != 3 {
            return Err("Invalid token format");
        }
        
        // This is simplified JWT validation
        // In a real implementation, use a proper JWT library
        
        // Decode header and payload
        let header_json = base64::decode(parts[0])
            .map_err(|_| "Invalid token header")?;
            
        let payload_json = base64::decode(parts[1])
            .map_err(|_| "Invalid token payload")?;
            
        // Parse payload
        let claims: Claims = serde_json::from_slice(&payload_json)
            .map_err(|_| "Invalid token payload format")?;
            
        // Verify token is not expired
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .expect("Time went backwards")
            .as_secs() as usize;
            
        if claims.exp < now {
            return Err("Token expired");
        }
        
        // Verify signature
        let message = format!("{}.{}", parts[0], parts[1]);
        let tag = hmac::sign(&self.jwt_secret, message.as_bytes());
        let expected_sig = base64::encode(tag.as_ref());
        
        if expected_sig != parts[2] {
            return Err("Invalid token signature");
        }
        
        Ok(claims)
    }
}

impl<B: Send> Middleware<B> for JwtAuthMiddleware {
    fn handle<'a>(
        &'a self,
        mut request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Check if this path requires authentication
            let path = request.uri().path();
            if !self.requires_auth(path) {
                return next(request).await;
            }
            
            // Extract token from Authorization header
            let token = match request.headers().get(header::AUTHORIZATION) {
                Some(auth_header) => {
                    match auth_header.to_str() {
                        Ok(auth_str) if auth_str.starts_with("Bearer ") => {
                            &auth_str[7..]
                        },
                        _ => {
                            return Response::builder()
                                .status(StatusCode::UNAUTHORIZED)
                                .header("WWW-Authenticate", "Bearer")
                                .body(().into())
                                .unwrap();
                        }
                    }
                },
                None => {
                    return Response::builder()
                        .status(StatusCode::UNAUTHORIZED)
                        .header("WWW-Authenticate", "Bearer")
                        .body(().into())
                        .unwrap();
                }
            };
            
            // Validate token
            match self.validate_token(token) {
                Ok(claims) => {
                    // Add claims to request extensions
                    request.extensions_mut().insert(claims);
                    
                    // Continue with request processing
                    next(request).await
                },
                Err(_) => {
                    Response::builder()
                        .status(StatusCode::UNAUTHORIZED)
                        .header("WWW-Authenticate", "Bearer error=\"invalid_token\"")
                        .body(().into())
                        .unwrap()
                }
            }
        })
    }
}
```

### Security Monitoring Middleware

Implement middleware for security monitoring and threat detection:

```rust
use std::sync::atomic::{AtomicUsize, Ordering};
use std::collections::HashMap;
use std::time::Instant;
use tokio::sync::RwLock;

// Security monitoring middleware
pub struct SecurityMonitoringMiddleware {
    // Track request counts per client
    request_counts: Arc<RwLock<HashMap<String, ClientStats>>>,
    // Track 4xx and 5xx responses
    error_counts: Arc<AtomicUsize>,
    // Alert threshold
    alert_threshold: usize,
}

struct ClientStats {
    // Request count in current window
    request_count: usize,
    // Error count in current window  
    error_count: usize,
    // Window start time
    window_start: Instant,
    // Last seen paths
    recent_paths: Vec<String>,
    // Last access time
    last_access: Instant,
}

impl SecurityMonitoringMiddleware {
    pub fn new(alert_threshold: usize) -> Self {
        Self {
            request_counts: Arc::new(RwLock::new(HashMap::new())),
            error_counts: Arc::new(AtomicUsize::new(0)),
            alert_threshold,
        }
    }
    
    // Start background monitoring task
    pub fn start_monitoring(self: Arc<Self>) {
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                interval.tick().await;
                self.check_for_anomalies().await;
            }
        });
    }
    
    // Check for security anomalies
    async fn check_for_anomalies(&self) {
        let now = Instant::now();
        let mut suspicious_clients = Vec::new();
        let window_size = Duration::from_secs(60);
        
        // Check client statistics
        let mut counts = self.request_counts.write().await;
        
        for (client, stats) in counts.iter_mut() {
            // Reset window if needed
            if now.duration_since(stats.window_start) > window_size {
                stats.request_count = 0;
                stats.error_count = 0;
                stats.window_start = now;
                stats.recent_paths.clear();
                continue;
            }
            
            // Check for high request rate
            if stats.request_count > self.alert_threshold {
                suspicious_clients.push((
                    client.clone(),
                    format!("High request rate: {} requests in last minute", stats.request_count)
                ));
            }
            
            // Check for high error rate
            if stats.error_count > 0 && 
               (stats.error_count as f32 / stats.request_count as f32) > 0.5 {
                suspicious_clients.push((
                    client.clone(),
                    format!("High error rate: {}% errors", 
                           (stats.error_count * 100) / stats.request_count)
                ));
            }
            
            // Check for suspicious path sequences (e.g., scanning)
            let unique_paths = stats.recent_paths
                .iter()
                .collect::<HashSet<_>>()
                .len();
                
            if unique_paths > 10 && stats.recent_paths.len() > 20 {
                suspicious_clients.push((
                    client.clone(),
                    format!("Possible scanning activity: {} unique paths in {} requests", 
                           unique_paths, stats.recent_paths.len())
                ));
            }
        }
        
        // Generate alerts for suspicious clients
        for (client, reason) in suspicious_clients {
            log::warn!("Security alert: {} - {}", client, reason);
            
            // In a real implementation, you might:
            // 1. Send an alert to a security monitoring system
            // 2. Add the client to a temporary block list
            // 3. Increase logging for this client
            // 4. Apply additional verification to requests from this client
        }
    }
}

impl<B: Send> Middleware<B> for SecurityMonitoringMiddleware {
    fn handle<'a>(
        &'a self,
        request: Request<B>,
        next: Next<'a, B>,
    ) -> Pin<Box<dyn Future<Output = Response<B>> + Send + 'a>> {
        Box::pin(async move {
            // Extract client identifier
            let client_id = extract_client_id(&request);
            let path = request.uri().path().to_string();
            let now = Instant::now();
            
            // Update client stats
            {
                let mut counts = self.request_counts.write().await;
                let entry = counts
                    .entry(client_id.clone())
                    .or_insert_with(|| ClientStats {
                        request_count: 0,
                        error_count: 0,
                        window_start: now,
                        recent_paths: Vec::new(),
                        last_access: now,
                    });
                
                entry.request_count += 1;
                entry.last_access = now;
                
                // Keep a limited history of recent paths
                entry.recent_paths.push(path);
                if entry.recent_paths.len() > 30 {
                    entry.recent_paths.remove(0);
                }
            }
            
            // Process the request
            let start_time = Instant::now();
            let response = next(request).await;
            let duration = start_time.elapsed();
            
            // Update metrics based on response
            let status = response.status().as_u16();
            
            if status >= 400 {
                // Count errors
                if status >= 500 {
                    self.error_counts.fetch_add(1, Ordering::Relaxed);
                }
                
                // Update client error count
                if let Ok(mut counts) = self.request_counts.write().await {
                    if let Some(stats) = counts.get_mut(&client_id) {
                        stats.error_count += 1;
                    }
                }
                
                // Log suspicious activity
                if status == 403 || status == 401 {
                    log::warn!(
                        "Security: Authentication/authorization failure from client {} with status {}",
                        client_id, status
                    );
                }
            }
            
            // Check for slow requests that might indicate attacks
            if duration > Duration::from_millis(500) {
                log::info!(
                    "Slow request: {} took {:?} from client {}", 
                    response.status(), duration, client_id
                );
            }
            
            response
        })
    }
}

// Helper function to extract client identifier
fn extract_client_id<B>(request: &Request<B>) -> String {
    request
        .headers()
        .get("x-forwarded-for")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown")
        .split(',')
        .next()
        .unwrap_or("unknown")
        .trim()
        .to_string()
}
```

### Middleware Chain Security

Ensure your middleware chain is properly structured for security:

```rust
// Create a secure middleware chain
fn create_secure_middleware_chain() -> MiddlewareChain<Vec<u8>> {
    let mut chain = MiddlewareChain::new();
    
    // Security-critical middleware must be in the correct order:
    
    // 1. Recovery middleware first (to catch panics)
    chain.add(RecoveryMiddleware);
    
    // 2. Request validation before any processing
    chain.add(RequestValidationMiddleware);
    
    // 3. Rate limiting to prevent DoS
    chain.add(RateLimitMiddleware::new(100, 60));
    
    // 4. Logging to capture all requests (even blocked ones)
    chain.add(LoggingMiddleware);
    
    // 5. CORS configuration (must come before auth)
    chain.add(CorsMiddleware::new()
        .allow_origins(vec!["https://example.com".to_string()])
        .allow_credentials(true));
    
    // 6. Security headers and controls
    chain.add(SecurityMiddleware::new(
        SecurityConfig::default(),
        "your-secret-key-here".as_bytes()
    ));
    
    // 7. Authentication and identification
    chain.add(JwtAuthMiddleware::new("your-jwt-secret".as_bytes())
        .exempt(vec!["/login".to_string(), "/public/*".to_string()]));
    
    // 8. Authorization (depends on authentication)
    chain.add(AuthorizationMiddleware::new());
    
    // 9. Request sanitization for XSS and injections
    chain.add(XssProtectionMiddleware::new());
    chain.add(SqlInjectionProtectionMiddleware::new());
    
    // 10. Monitoring (after security checks, before business logic)
    let monitoring = Arc::new(SecurityMonitoringMiddleware::new(100));
    monitoring.clone().start_monitoring();
    chain.add(monitoring);
    
    // 11. Business logic middleware follows...
    chain.add(BusinessLogicMiddleware);
    
    // 12. Response compression (typically last)
    chain.add(CompressionMiddleware::new(1024));
    
    chain
}
```

### Best Practices for Middleware Security

1. **Security Middleware Order Matters**:
   - Recovery middleware should be first to prevent crashes
   - Authentication should come before authorization
   - Input validation should be early in the chain
   - Response processing like compression should be late in the chain

2. **Prevent Middleware Bypass**:
   - Ensure security middleware can't be bypassed
   - Don't rely on route configuration for security
   - Implement security at the middleware level, not just in handlers
   - Use conditional middleware carefully, with secure defaults

3. **Defense in Depth**:
   - Implement multiple layers of security middleware
   - Don't rely on a single middleware for critical security controls
   - Combine authentication, authorization, validation, and monitoring
   - Consider security in both request and response processing

4. **Careful Error Handling**:
   - Don't expose sensitive information in error responses
   - Log security failures but don't reveal details to clients
   - Use generic error messages for authentication failures
   - Implement proper HTTP status codes for security issues

5. **Monitor Performance**:
   - Watch for middleware that could cause DoS vulnerabilities
   - Be cautious with computationally expensive operations
   - Implement timeouts for all blocking operations
   - Profile your middleware chain to identify bottlenecks

6. **Secure Configuration**:
   - Don't hardcode secrets in middleware
   - Use environment-specific configuration
   - Apply secure defaults that must be explicitly overridden
   - Validate security-critical configuration at startup

7. **Audit and Testing**:
   - Log security-relevant operations in middleware
   - Write security-focused tests for middleware
   - Test middleware in isolation and as part of the chain
   - Verify security middleware can't be bypassed

8. **Keep Dependencies Updated**:
   - Regularly update security middleware dependencies
   - Monitor CVEs for libraries used in your middleware
   - Have a process for emergency security updates
   - Consider a security review for third-party middleware

## Knowledge Check

1. What is middleware in the context of a web server?
   - Middleware components are modular pieces of code that process HTTP requests and responses in a chain, handling cross-cutting concerns such as logging, authentication, compression, etc.

2. Why is middleware order important?
   - The order determines the sequence in which requests and responses are processed.
   - Some middleware depends on others (e.g., authorization requires authentication)
   - Early middleware like logging can capture information about all requests
   - Some middleware should only run after others (e.g., compression usually comes later)

3. What is the difference between pre-processing and post-processing in middleware?
   - Pre-processing: Operations performed on the request before it reaches the handler
   - Post-processing: Operations performed on the response after the handler generates it

4. What are some common use cases for middleware?
   - Logging and request tracing
   - Authentication and authorization
   - CORS handling
   - Rate limiting and DoS protection
   - Compression
   - Response caching
   - Error handling and recovery

5. How can you apply middleware conditionally?
   - By path or URL pattern
   - By HTTP method
   - Based on request properties (headers, query parameters)
   - Using custom logic in conditional middleware wrappers

## Additional Resources

- [Tower middleware framework](https://docs.rs/tower/latest/tower/)
- [Actix-web middleware](https://actix.rs/docs/middleware/)
- [Warp filters](https://docs.rs/warp/latest/warp/filters/index.html)
- [HTTP Filters and Middleware](https://developer.mozilla.org/en-US/docs/Web/HTTP)
- [Design Patterns: Decorator Pattern](https://refactoring.guru/design-patterns/decorator) (middleware is an application of this pattern)
