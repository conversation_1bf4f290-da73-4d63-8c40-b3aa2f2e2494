<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\10-reverse-proxy.md -->
# Module 10: Reverse Proxy Implementation

## Navigation
- [Previous: Virtual Hosts](09-virtual-hosts.md)
- [Next: Caching Mechanisms](11-caching-mechanisms.md)

## Table of Contents
- [Introduction](#introduction)
- [Learning Objectives](#learning-objectives)
- [Prerequisites](#prerequisites)
- [Core Concepts](#core-concepts)
- [Architecture Overview](#architecture-overview)
- [Implementation Steps](#implementation-steps)
- [Advanced Proxy Features](#advanced-proxy-features)
- [Testing and Benchmarking](#testing-and-benchmarking)
- [Security Considerations](#security-considerations)
- [Best Practices](#best-practices)
- [Performance Optimization](#performance-optimization)
- [Knowledge Check](#knowledge-check)
- [Additional Resources](#additional-resources)

## Introduction

In this module, you'll implement reverse proxy functionality for your Rust web server, allowing it to forward requests to backend servers and return their responses to clients. A reverse proxy sits between client devices and backend servers, forwarding client requests to the appropriate servers and returning responses to the clients.

Reverse proxies are crucial components in modern web architecture, serving many purposes:

- **Load balancing**: Distributing incoming traffic across multiple backend servers
- **Security**: Shielding backend servers from direct client access
- **SSL termination**: Handling HTTPS connections so backend servers can use HTTP
- **Compression**: Compressing responses before sending them to clients
- **Caching**: Storing frequently accessed resources to reduce backend load
- **Microservices architecture**: Routing requests to different microservices

## Learning Objectives

By the end of this module, you will be able to:

1. **Understand** the core concepts and benefits of reverse proxies in web architectures
2. **Design** efficient proxy routing logic for different backend services
3. **Implement** a fully-functional reverse proxy in Rust using asynchronous programming
4. **Manage** HTTP headers properly for proxy communication
5. **Create** connection pooling strategies for improved backend performance
6. **Configure** timeout handling to prevent resource exhaustion
7. **Implement** load balancing across multiple backend servers
8. **Apply** security best practices for reverse proxy implementations
9. **Test** the reverse proxy functionality with real-world scenarios
10. **Troubleshoot** common issues in proxy implementations

## Prerequisites

Before starting this module, you should have:

- Completed the previous modules, especially Module 08 (Connection Pooling) and Module 09 (Virtual Hosts)
- Familiarity with HTTP protocol fundamentals, including headers and request/response flow
- Understanding of asynchronous programming in Rust with Tokio
- Basic knowledge of network protocols and connection handling
- Familiarity with the Hyper crate for HTTP implementations in Rust

## Core Concepts

### What is a Reverse Proxy?

A reverse proxy is a server that sits in front of one or more web servers, intercepting requests from clients before forwarding them to appropriate backend servers. Unlike a forward proxy (which sits in front of clients to provide anonymity or access control), a reverse proxy represents and shields the backend servers from direct client access.

```mermaid
graph LR
    subgraph "Forward Proxy"
        Client1[Client] --> FP[Forward Proxy] --> Internet((Internet)) --> WebServer[Web Server]
    end
    
    subgraph "Reverse Proxy"
        Client2[Client] --> Internet2((Internet)) --> RP[Reverse Proxy]
        RP --> Backend1[Backend Server 1]
        RP --> Backend2[Backend Server 2]
        RP --> Backend3[Backend Server 3]
    end
    
    style FP fill:#f9f,stroke:#333,stroke-width:2px
    style RP fill:#bbf,stroke:#333,stroke-width:2px
    style Client1 fill:#dfd,stroke:#333
    style Client2 fill:#dfd,stroke:#333
    style Backend1 fill:#fdd,stroke:#333
    style Backend2 fill:#fdd,stroke:#333
    style Backend3 fill:#fdd,stroke:#333
```

### Key Terminology and Concepts

- **Forward Proxy**: Acts on behalf of clients, forwarding their requests to servers. Primarily used for client anonymity or bypassing restrictions.
- **Reverse Proxy**: Acts on behalf of servers, handling client requests and forwarding them to appropriate backend servers. Clients have no awareness of the backend infrastructure.
- **Backend Server**: The actual server that processes requests and generates responses. It may be a web server, application server, or API service.
- **Load Balancing**: The process of distributing incoming network traffic across multiple servers to ensure no single server bears too much load.
- **SSL Termination**: Decrypting SSL/TLS connections at the proxy level before forwarding requests to backends over a potentially unencrypted connection.
- **Health Checking**: Monitoring backend servers to ensure they're operational and removing failed servers from the rotation.
- **URI Rewriting**: Modifying request URIs before passing them to backend servers, often used to normalize paths or implement routing rules.
- **Header Manipulation**: Adding, removing, or modifying HTTP headers to control proxy behavior and provide backend servers with client information.

### Reverse Proxy Benefits

1. **Enhanced Security**:
   - Hides backend server details and architecture from clients
   - Provides a single point for implementing security policies
   - Can filter malicious traffic before it reaches backends
   - Allows central management of SSL/TLS certificates

2. **Improved Scalability**:
   - Enables horizontal scaling by adding more backend servers
   - Distributes load across multiple backends
   - Handles connection pooling to reduce backend connection overhead

3. **Better Performance**:
   - Enables geographical distribution of backend servers
   - Can compress/decompress content to reduce transmission size
   - Provides caching capabilities for frequently accessed content
   - Optimizes persistent connection handling

4. **Architectural Flexibility**:
   - Facilitates microservices architecture by routing to appropriate services
   - Enables gradual migration between different backend systems
   - Allows backend servers to be specialized for specific tasks

### Web Server Design Decisions

- **Request Forwarding:** Forward client requests to backend servers, preserving important headers while adding proxy-specific headers like `X-Forwarded-For`.
- **Response Handling:** Return backend responses to clients efficiently with minimal modification, potentially using streaming to handle large responses.
- **Backend Selection:** Configure which requests go to which backends using path-based routing, hostname-based routing, or a combination of both.
- **Error Handling:** Handle backend failures with appropriate error responses to clients, potentially implementing retry logic for transient errors.
- **Header Management:** Properly handle and modify request/response headers for correct proxy behavior, including managing hop-by-hop headers.
- **Connection Management:** Efficiently manage connections to both clients and backend servers, with proper timeout handling and connection pooling.

### Rust Implementation Decisions

- **Hyper Client:** Use the `hyper` crate as an HTTP client to connect to backends for its performance, flexibility, and strong typing.
- **Async/Await:** Implement asynchronous programming with Tokio for efficient I/O handling and high concurrent performance without blocking threads.
- **Error Handling:** Use comprehensive error types and propagation for connection failures and timeouts, with structured logging for diagnostics.
- **Connection Pooling:** Leverage Hyper's built-in connection pooling for efficient backend connections, reducing latency and resource usage.
- **Stream Processing:** Use efficient streaming for large request/response bodies to minimize memory usage and improve responsiveness.
- **Timeout Management:** Implement timeouts at multiple levels (connection establishment, request processing, idle connections) to prevent resource exhaustion.

## Architecture Overview

### Reverse Proxy Request-Response Flow

The following sequence diagram illustrates the complete flow of a request through our reverse proxy implementation:

```mermaid
sequenceDiagram
    participant Client
    participant ReverseProxy as Rusty Server (Reverse Proxy)
    participant Backend
    
    Client->>ReverseProxy: HTTP Request
    Note over ReverseProxy: Parse HTTP request
    Note over ReverseProxy: Extract Host header
    Note over ReverseProxy: Match virtual host config
    Note over ReverseProxy: Determine target backend based on path
    
    alt Backend unavailable
        ReverseProxy-->>Client: 502 Bad Gateway
    else Backend available
        ReverseProxy->>ReverseProxy: Prepare backend request
        Note over ReverseProxy: Copy original headers
        Note over ReverseProxy: Add X-Forwarded-* headers
        Note over ReverseProxy: Set proper Host header
        
        ReverseProxy->>Backend: Forward modified request
        Note over Backend: Process request
        
        alt Backend processes successfully
            Backend->>ReverseProxy: HTTP Response
            Note over ReverseProxy: Process response headers
            Note over ReverseProxy: Remove sensitive headers
            ReverseProxy->>Client: Forward modified response
        else Backend error/timeout
            Backend-->>ReverseProxy: Error response or timeout
            ReverseProxy-->>Client: Error response (4xx/5xx)
        end
    end
```

### Component Architecture

Our reverse proxy architecture integrates with the existing virtual host system and provides flexible backend routing:

```mermaid
flowchart TB
    Client([Client])
    
    subgraph RustyServer["Rusty Web Server"]
        direction TB
        Router["Request Router"]
        
        subgraph VirtualHosts["Virtual Host System"]
            VHostManager["Virtual Host Manager"]
            VHost1["api.example.com"]
            VHost2["www.example.com"]
            VHost3["admin.example.com"]
        end
        
        subgraph Handlers["Request Handlers"]
            ProxyHandler["Proxy Handler"]
            FileHandler["Static File Handler"]
            ErrorHandler["Error Handler"]
        end
        
        subgraph ConnectionPool["Connection Management"]
            Pool["Connection Pool"]
            KeepAlive["Keep-Alive Manager"]
        end
    end
    
    subgraph Backends["Backend Servers"]
        API["API Server\n(localhost:3001)"]
        App["Application Server\n(localhost:3002)"]
        Static["Static Content\n(localhost:3003)"]
        Admin["Admin Dashboard\n(localhost:3004)"]
    end
    
    Client -->|HTTP Request| Router
    Router --> VHostManager
    VHostManager --> VHost1
    VHostManager --> VHost2
    VHostManager --> VHost3
    
    VHost1 -->|Proxy Config| ProxyHandler
    VHost2 -->|Doc Root| FileHandler
    VHost3 -->|Proxy Config| ProxyHandler
    
    ProxyHandler --> Pool
    Pool -->|API Requests| API
    Pool -->|App Requests| App
    Pool -->|Static Requests| Static
    Pool -->|Admin Requests| Admin
    
    API -->|Response| ProxyHandler
    App -->|Response| ProxyHandler
    Static -->|Response| ProxyHandler
    Admin -->|Response| ProxyHandler
    
    FileHandler -->|Response| Router
    ProxyHandler -->|Response| Router
    ErrorHandler -->|Error Response| Router
    Router -->|HTTP Response| Client
    
    classDef backend fill:#f9d0c4,stroke:#333,stroke-width:1px;
    classDef handler fill:#c4f9d0,stroke:#333,stroke-width:1px;
    classDef vhost fill:#d0c4f9,stroke:#333,stroke-width:1px;
    classDef client fill:#f9f9c4,stroke:#333,stroke-width:1px;
    
    class API,App,Static,Admin backend;
    class ProxyHandler,FileHandler,ErrorHandler handler;
    class VHost1,VHost2,VHost3,VHostManager vhost;
    class Client client;
```

### Detailed Request Processing Pipeline

The following flowchart shows the detailed processing steps for a proxy request:

```mermaid
flowchart TD
    Start([Request Received]) --> ParseRequest[Parse HTTP Request]
    ParseRequest --> ExtractHost[Extract Host Header]
    ExtractHost --> MatchVHost{Match Virtual Host}
    
    MatchVHost -->|No Match| DefaultVHost[Use Default VHost]
    DefaultVHost --> CheckProxyConfig
    MatchVHost -->|Match Found| CheckProxyConfig{Has Proxy Config?}
    
    CheckProxyConfig -->|No| StaticHandler[Static File Handler]
    CheckProxyConfig -->|Yes| PathRouting[Path-Based Routing]
    
    PathRouting --> MatchPath{Match Path Pattern}
    MatchPath -->|No Match| UseDefault{Has Default Backend?}
    UseDefault -->|No| Error404[Return 404 Not Found]
    UseDefault -->|Yes| DefaultBackend[Use Default Backend]
    
    MatchPath -->|Match Found| SelectBackend[Select Backend Server]
    DefaultBackend --> PrepareRequest
    
    SelectBackend --> BackendHealth{Is Backend Healthy?}
    BackendHealth -->|No| Error502[Return 502 Bad Gateway]
    BackendHealth -->|Yes| PrepareRequest[Prepare Backend Request]
    
    PrepareRequest --> CopyHeaders[Copy Original Headers]
    CopyHeaders --> AddProxyHeaders[Add X-Forwarded-* Headers]
    AddProxyHeaders --> SetHost[Set Proper Host Header]
    
    SetHost --> SendRequest[Send Request to Backend]
    SendRequest --> WaitResponse{Wait for Response}
    
    WaitResponse -->|Timeout| Error504[Return 504 Gateway Timeout]
    WaitResponse -->|Error| Error502
    WaitResponse -->|Success| ProcessResponse[Process Response Headers]
    
    ProcessResponse --> RemoveSensitive[Remove Sensitive Headers]
    RemoveSensitive --> AddServerHeaders[Add Server Headers]
    AddServerHeaders --> ReturnResponse[Return Response to Client]
    
    StaticHandler --> ReturnResponse
    Error404 --> ReturnResponse
    Error502 --> ReturnResponse
    Error504 --> ReturnResponse
    
    classDef process fill:#f9f,stroke:#333,stroke-width:1px;
    classDef decision fill:#bbf,stroke:#333,stroke-width:1px;
    classDef error fill:#fbb,stroke:#333,stroke-width:1px;
    classDef success fill:#bfb,stroke:#333,stroke-width:1px;
    
    class ParseRequest,ExtractHost,PathRouting,PrepareRequest,CopyHeaders,AddProxyHeaders,SetHost,SendRequest,ProcessResponse,RemoveSensitive,AddServerHeaders process;
    class MatchVHost,CheckProxyConfig,MatchPath,UseDefault,BackendHealth,WaitResponse decision;
    class Error404,Error502,Error504 error;
    class ReturnResponse,SelectBackend,DefaultBackend success;
```

### Data Flow Architecture

The following diagram shows how data flows through the different components of our reverse proxy implementation:

```mermaid
flowchart LR
    subgraph Client["Client Side"]
        ClientApp[Client Application]
    end
    
    subgraph ProxyServer["Rusty Web Server"]
        direction TB
        Listener[TCP Listener] --> Parser[HTTP Parser]
        Parser --> Router[Request Router]
        Router --> ProxyHandler[Proxy Handler]
        ProxyHandler --> BackendRouter[Backend Router]
        BackendRouter --> RequestBuilder[Backend Request Builder]
        RequestBuilder --> ConnPool[Connection Pool]
        ConnPool --> ResponseHandler[Response Handler]
        ResponseHandler --> ResponseWriter[Response Writer]
    end
    
    subgraph Backends["Backend Servers"]
        API[API Service]
        WebApp[Web Application]
        Storage[Storage Service]
    end
    
    ClientApp -->|HTTP Request| Listener
    ConnPool -->|Forward Request| API
    ConnPool -->|Forward Request| WebApp
    ConnPool -->|Forward Request| Storage
    API -->|HTTP Response| ResponseHandler
    WebApp -->|HTTP Response| ResponseHandler
    Storage -->|HTTP Response| ResponseHandler
    ResponseWriter -->|HTTP Response| ClientApp
    
    classDef clientside fill:#f9d0c4,stroke:#333;
    classDef proxyserver fill:#d0c4f9,stroke:#333;
    classDef backends fill:#c4f9d0,stroke:#333;
    
    class ClientApp clientside;
    class Listener,Parser,Router,ProxyHandler,BackendRouter,RequestBuilder,ConnPool,ResponseHandler,ResponseWriter proxyserver;
    class API,WebApp,Storage backends;
```

## Implementation Steps

In this section, we'll implement a robust reverse proxy for our Rusty Web Server, using the async capabilities of Rust to efficiently handle concurrent requests.

### 1. Add Required Dependencies

First, let's add the necessary dependencies to `Cargo.toml`:

```toml
[dependencies]
# Existing dependencies...

# HTTP client for reverse proxy
hyper = { version = "0.14", features = ["client", "http1", "http2", "stream"] }
hyper-tls = "0.5"
tokio = { version = "1", features = ["full"] }
futures = "0.3"
tower = "0.4"
http = "0.2"
bytes = "1.0"

# HTTP header manipulation
http-body = "0.4"
http-body-util = "0.1"

# Improved logging for debugging
tracing = "0.1"
```

### 2. Define Proxy Configuration Types

Let's create a file at `src/config/proxy_config.rs` to define our proxy configuration types:

```rust
use serde::Deserialize;
use std::collections::HashMap;
use std::time::Duration;

/// Configuration for load balancing multiple backends
#[derive(Debug, Clone, Deserialize)]
#[serde(tag = "type", rename_all = "lowercase")]
pub enum LoadBalanceConfig {
    /// Round-robin load balancing
    RoundRobin {
        /// List of backend URLs to load balance between
        backends: Vec<String>,
    },
    /// Random backend selection
    Random {
        /// List of backend URLs to load balance between
        backends: Vec<String>,
    },
    /// Fixed backend (no load balancing)
    Fixed {
        /// Single backend URL
        backend: String,
    },
}

impl LoadBalanceConfig {
    /// Get all backend URLs from this configuration
    pub fn all_backends(&self) -> Vec<&str> {
        match self {
            LoadBalanceConfig::RoundRobin { backends } => backends.iter().map(|s| s.as_str()).collect(),
            LoadBalanceConfig::Random { backends } => backends.iter().map(|s| s.as_str()).collect(),
            LoadBalanceConfig::Fixed { backend } => vec![backend.as_str()],
        }
    }
}

/// Location configuration for path-based routing
#[derive(Debug, Clone, Deserialize)]
pub struct LocationConfig {
    /// Backend configuration for this path
    #[serde(flatten)]
    pub backend: LoadBalanceConfig,
    
    /// Whether to strip the prefix before forwarding the request
    #[serde(default)]
    pub strip_prefix: bool,
    
    /// Additional headers to add to the request
    #[serde(default)]
    pub add_headers: HashMap<String, String>,
}

/// Proxy server configuration
#[derive(Debug, Clone, Deserialize)]
pub struct ProxyConfig {
    /// Map of path prefixes to backend configurations
    pub locations: HashMap<String, LocationConfig>,
    
    /// Default backend if no location matches
    pub default_backend: Option<LoadBalanceConfig>,
    
    /// Connect timeout in seconds
    #[serde(default = "default_connect_timeout")]
    pub connect_timeout: u64,
    
    /// Read timeout in seconds
    #[serde(default = "default_read_timeout")]
    pub read_timeout: u64,
    
    /// Write timeout in seconds
    #[serde(default = "default_write_timeout")]
    pub write_timeout: u64,
    
    /// Maximum number of idle connections per host
    #[serde(default = "default_max_idle_connections")]
    pub max_idle_connections: usize,
    
    /// Whether to pass the Host header from the client request
    #[serde(default = "default_true")]
    pub pass_host_header: bool,
}

fn default_connect_timeout() -> u64 { 10 }
fn default_read_timeout() -> u64 { 30 }
fn default_write_timeout() -> u64 { 30 }
fn default_max_idle_connections() -> usize { 32 }
fn default_true() -> bool { true }

impl Default for ProxyConfig {
    fn default() -> Self {
        Self {
            locations: HashMap::new(),
            default_backend: None,
            connect_timeout: default_connect_timeout(),
            read_timeout: default_read_timeout(),
            write_timeout: default_write_timeout(),
            max_idle_connections: default_max_idle_connections(),
            pass_host_header: default_true(),
        }
    }
}
```

Let's update `src/config/mod.rs` to incorporate the proxy configuration:

```rust
mod proxy_config;
pub use proxy_config::{ProxyConfig, LoadBalanceConfig, LocationConfig};

// Add these to your existing VirtualHostConfig
#[derive(Debug, Clone)]
pub struct VirtualHostConfig {
    // ...existing fields...
    
    /// Proxy configuration (if this virtual host is a reverse proxy)
    pub proxy: Option<ProxyConfig>,
}

// Update the default implementation as well
impl Default for VirtualHostConfig {
    fn default() -> Self {
        Self {
            // ...existing fields...
            proxy: None,
        }
    }
}
```

### 3. Create Backend Selection and Load Balancing

Create a file at `src/server/proxy/backend_selector.rs` to handle backend selection:

```rust
use crate::config::{LoadBalanceConfig, LocationConfig, ProxyConfig};
use crate::error::{Result, ServerError};
use std::sync::atomic::{AtomicUsize, Ordering};
use rand::Rng;
use std::collections::HashMap;

/// Service for selecting a backend based on request path and load balancing configuration
pub struct BackendSelector {
    /// Proxy configuration
    config: ProxyConfig,
    
    /// Counter for round-robin load balancing
    round_robin_counters: HashMap<String, AtomicUsize>,
}

impl BackendSelector {
    /// Create a new backend selector
    pub fn new(config: ProxyConfig) -> Self {
        let mut round_robin_counters = HashMap::new();
        
        // Initialize counters for each location with round-robin config
        for (path, location) in &config.locations {
            if let LoadBalanceConfig::RoundRobin { .. } = &location.backend {
                round_robin_counters.insert(path.clone(), AtomicUsize::new(0));
            }
        }
        
        Self {
            config,
            round_robin_counters,
        }
    }
    
    /// Get the backend URL and location config for a request path
    pub fn select_backend(&self, path: &str) -> Result<(String, &LocationConfig)> {
        // Find matching location
        for (pattern, location) in &self.config.locations {
            if path.starts_with(pattern) {
                let backend_url = self.get_backend_url(pattern, &location.backend)?;
                return Ok((backend_url, location));
            }
        }
        
        // Use default backend if available
        if let Some(ref default_backend) = self.config.default_backend {
            let backend_url = self.get_backend_url("default", default_backend)?;
            
            // Create a default location config for the default backend
            let default_location = LocationConfig {
                backend: default_backend.clone(),
                strip_prefix: false,
                add_headers: HashMap::new(),
            };
            
            return Ok((backend_url, &default_location));
        }
        
        Err(ServerError::Proxy(format!("No backend found for path: {}", path)))
    }
    
    /// Get a backend URL based on load balancing configuration
    fn get_backend_url(&self, path: &str, config: &LoadBalanceConfig) -> Result<String> {
        match config {
            LoadBalanceConfig::RoundRobin { backends } => {
                if backends.is_empty() {
                    return Err(ServerError::Proxy("No backends configured for round-robin".to_string()));
                }
                
                // Get or create counter
                let counter = self.round_robin_counters
                    .get(path)
                    .unwrap_or_else(|| panic!("Round-robin counter not initialized for path {}", path));
                
                // Get next backend index and increment counter
                let index = counter.fetch_add(1, Ordering::Relaxed) % backends.len();
                Ok(backends[index].clone())
            },
            LoadBalanceConfig::Random { backends } => {
                if backends.is_empty() {
                    return Err(ServerError::Proxy("No backends configured for random selection".to_string()));
                }
                
                // Select a random backend
                let index = rand::thread_rng().gen_range(0..backends.len());
                Ok(backends[index].clone())
            },
            LoadBalanceConfig::Fixed { backend } => {
                Ok(backend.clone())
            },
        }
    }
    
    /// Build the full URL for a request
    pub fn build_target_url(&self, backend_url: &str, original_path: &str, location: &LocationConfig) -> String {
        if location.strip_prefix {
            // Find the prefix that matched this location
            for (prefix, loc) in &self.config.locations {
                if std::ptr::eq(loc, location) && original_path.starts_with(prefix) {
                    // Strip the prefix
                    let new_path = &original_path[prefix.len()..];
                    // Ensure the path starts with a slash
                    let new_path = if new_path.starts_with('/') { new_path } else { "/" };
                    return format!("{}{}", backend_url.trim_end_matches('/'), new_path);
                }
            }
        }
        
        // Don't strip prefix, use full path
        format!("{}{}", backend_url.trim_end_matches('/'), original_path)
    }
}
```

### 4. Create a Proxy Handler Module

Now, let's create the main proxy handler at `src/server/proxy/handler.rs`:

```rust
use crate::config::ProxyConfig;
use crate::error::{Result, ServerError};
use crate::http::{Method, Request, Response, StatusCode};
use crate::server::proxy::backend_selector::BackendSelector;
use hyper::{Body, Client, Uri};
use hyper::client::HttpConnector;
use hyper_tls::HttpsConnector;
use hyper::header::{HeaderName, HeaderValue};
use http::HeaderMap;
use log::{debug, error, info};
use std::str::FromStr;
use std::time::Duration;
use std::sync::Arc;

/// List of headers that should not be forwarded to backends
const HOP_BY_HOP_HEADERS: [&str; 8] = [
    "connection",
    "keep-alive",
    "proxy-authenticate", 
    "proxy-authorization",
    "te",
    "trailer",
    "transfer-encoding",
    "upgrade",
];

/// Handler for reverse proxy functionality
pub struct ProxyHandler {
    /// Configuration for the proxy
    config: ProxyConfig,
    
    /// HTTP client for backend communication
    client: Client<HttpsConnector<HttpConnector>>,
    
    /// Backend selector for load balancing and routing
    backend_selector: Arc<BackendSelector>,
}

impl ProxyHandler {
    /// Create a new proxy handler
    pub fn new(config: ProxyConfig) -> Self {
        // Create an HTTPS connector
        let mut https = HttpsConnector::new();
        https.https_only(false); // Allow both HTTP and HTTPS backends
        
        // Create the HTTP client with configured timeouts and connection pooling
        let client = Client::builder()
            .pool_idle_timeout(Duration::from_secs(30))
            .pool_max_idle_per_host(config.max_idle_connections)
            .build(https);
        
        let backend_selector = Arc::new(BackendSelector::new(config.clone()));
        
        Self {
            config,
            client,
            backend_selector,
        }
    }
    
    /// Handle a request by proxying it to a backend server
    pub async fn handle_request(&self, request: &Request) -> Result<Response> {
        let start_time = std::time::Instant::now();
        let path = request.path.clone();
        let method = request.method.clone();
        
        // Extract client information for logging
        let client_ip = request.client_addr
            .as_ref()
            .cloned()
            .unwrap_or_else(|| "unknown".to_string());
        
        let host = request.headers
            .get("host")
            .cloned()
            .unwrap_or_else(|| "unknown".to_string());
        
        debug!(
            "Proxy request: {} {} {} (client: {})", 
            method, path, host, client_ip
        );
        
        // Select a backend based on the request path
        let (backend_url, location) = match self.backend_selector.select_backend(&path) {
            Ok(result) => result,
            Err(err) => {
                error!("Backend selection error: {} for path: {}", err, path);
                return Ok(Response::new(StatusCode::BadGateway)
                    .with_body(format!("No backend available for path: {}", path).into_bytes()));
            }
        };
        
        // Build the target URL
        let url = self.backend_selector.build_target_url(&backend_url, &path, location);
        debug!("Selected backend: {}", url);
        
        // Create and send the backend request
        let result = self.send_request_to_backend(request, &url, location).await;
        
        // Log completion with timing information
        let duration = start_time.elapsed();
        match &result {
            Ok(response) => {
                info!(
                    "Proxy response: {} {} {} -> {} - {} in {:?}",
                    method, path, host, backend_url, response.status.as_u16(), duration
                );
            }
            Err(err) => {
                error!(
                    "Proxy error: {} {} {} -> {} - {} in {:?}",
                    method, path, host, backend_url, err, duration
                );
            }
        }
        
        result
    }
    
    /// Send the request to the backend server
    async fn send_request_to_backend(
        &self, 
        request: &Request, 
        target_url: &str,
        location: &LocationConfig,
    ) -> Result<Response> {
        // Parse the target URL
        let uri = Uri::from_str(target_url)
            .map_err(|e| ServerError::Proxy(format!("Invalid backend URI: {}", e)))?;
        
        // Create the hyper request builder with the appropriate method and URI
        let mut hyper_req = hyper::Request::builder()
            .method(hyper::Method::from_str(&request.method.to_string()).unwrap())
            .uri(uri);
        
        // Add headers from the original request, except hop-by-hop headers
        let headers = hyper_req.headers_mut().unwrap();
        self.copy_headers_to_backend(request, headers, location)?;
        
        // Create the request body
        let hyper_body = Body::from(request.body.clone());
        
        // Build the final request
        let hyper_req = hyper_req
            .body(hyper_body)
            .map_err(|e| ServerError::Proxy(format!("Failed to build request: {}", e)))?;
        
        // Send the request to the backend with timeout
        let timeout_duration = Duration::from_secs(self.config.read_timeout);
        let timeout_fut = tokio::time::timeout(
            timeout_duration,
            self.client.request(hyper_req)
        );
        
        let response = match timeout_fut.await {
            Ok(result) => result.map_err(|e| {
                ServerError::Proxy(format!("Failed to send request to backend: {}", e))
            })?,
            Err(_) => return Err(ServerError::Proxy(format!(
                "Backend request timed out after {} seconds", 
                self.config.read_timeout
            ))),
        };
        
        // Convert the hyper response to our Response type
        self.convert_hyper_response(response).await
    }
    
    /// Copy headers from the original request to the backend request
    fn copy_headers_to_backend(
        &self, 
        request: &Request,
        headers: &mut HeaderMap<HeaderValue>,
        location: &LocationConfig,
    ) -> Result<()> {
        // Copy original headers, except hop-by-hop headers
        for (name, value) in &request.headers {
            // Skip hop-by-hop headers
            let lower_name = name.to_lowercase();
            if HOP_BY_HOP_HEADERS.contains(&lower_name.as_str()) {
                continue;
            }
            
            // Handle Host header specially
            if lower_name == "host" && !self.config.pass_host_header {
                continue; // Skip Host header when pass_host_header is false
            }
            
            // Add the header
            if let (Ok(header_name), Ok(header_value)) = (
                HeaderName::from_str(name),
                HeaderValue::from_str(value)
            ) {
                headers.insert(header_name, header_value);
            }
        }
        
        // Set appropriate Host header based on configuration
        if let Ok(uri) = Uri::from_str(&location.backend.all_backends()[0]) {
            if let Some(host) = uri.host() {
                let host_port = if let Some(port) = uri.port_u16() {
                    format!("{}:{}", host, port)
                } else {
                    host.to_string()
                };
                
                if let Ok(host_value) = HeaderValue::from_str(&host_port) {
                    headers.insert(hyper::header::HOST, host_value);
                }
            }
        }
        
        // Add X-Forwarded headers
        let mut forwarded_for = String::new();
        
        // Append to existing X-Forwarded-For header if present
        if let Some(existing_xff) = request.headers.get("x-forwarded-for") {
            forwarded_for.push_str(existing_xff);
            forwarded_for.push_str(", ");
        }
        
        // Add client IP
        if let Some(client_ip) = request.client_addr.as_ref() {
            forwarded_for.push_str(client_ip);
        } else {
            forwarded_for.push_str("unknown");
        }
        
        // Set X-Forwarded headers
        if let Ok(xff_value) = HeaderValue::from_str(&forwarded_for) {
            headers.insert("x-forwarded-for", xff_value);
        }
        
        if let Ok(proto_value) = HeaderValue::from_str("http") {
            headers.insert("x-forwarded-proto", proto_value);
        }
        
        if let Some(host) = request.headers.get("host") {
            if let Ok(host_value) = HeaderValue::from_str(host) {
                headers.insert("x-forwarded-host", host_value);
            }
        }
        
        // Add any additional headers specified in the location configuration
        for (name, value) in &location.add_headers {
            if let (Ok(header_name), Ok(header_value)) = (
                HeaderName::from_str(name),
                HeaderValue::from_str(value)
            ) {
                headers.insert(header_name, header_value);
            }
        }
        
        Ok(())
    }
    
    /// Convert a hyper response to our Response type
    async fn convert_hyper_response(&self, hyper_resp: hyper::Response<Body>) -> Result<Response> {
        // Get the status code
        let status = StatusCode::from_u16(hyper_resp.status().as_u16())
            .unwrap_or(StatusCode::InternalServerError);
        
        // Create our response with the same status
        let mut response = Response::new(status);
        
        // Copy the headers, excluding hop-by-hop headers
        for (name, value) in hyper_resp.headers() {
            // Skip hop-by-hop headers
            if HOP_BY_HOP_HEADERS.contains(&name.as_str().to_lowercase().as_str()) {
                continue;
            }
            
            // Add the header to our response
            if let Ok(value_str) = value.to_str() {
                response.headers.insert(name.as_str().to_string(), value_str.to_string());
            }
        }
        
        // Add server identification
        response.headers.insert(
            "server".to_string(),
            format!("Rusty Server Proxy v{}", env!("CARGO_PKG_VERSION"))
        );
        
        // Get the body with timeout
        let timeout_duration = Duration::from_secs(self.config.read_timeout);
        let timeout_fut = tokio::time::timeout(
            timeout_duration,
            hyper::body::to_bytes(hyper_resp.into_body())
        );
        
        let body_bytes = match timeout_fut.await {
            Ok(result) => result.map_err(|e| {
                ServerError::Proxy(format!("Failed to read backend response: {}", e))
            })?,
            Err(_) => return Err(ServerError::Proxy(
                format!("Backend response timed out after {} seconds", self.config.read_timeout)
            )),
        };
        
        // Set the body in our response
        response.body = body_bytes.to_vec();
        
        Ok(response)
    }
}
```

### 5. Create a Module File for Proxy Components

Create a file at `src/server/proxy/mod.rs` to organize our proxy components:

```rust
mod backend_selector;
mod handler;

pub use backend_selector::BackendSelector;
pub use handler::ProxyHandler;
```

And update `src/server/mod.rs` to include our proxy module:

```rust
// Add to existing modules
mod proxy;
pub use proxy::ProxyHandler;
```

### 6. Update Error Types for Proxy Errors

Add a new error variant to the `ServerError` enum in `src/error.rs`:

```rust
pub enum ServerError {
    // ...existing variants...
    
    /// Proxy-related error
    Proxy(String),
}

impl Display for ServerError {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            // ...existing cases...
            ServerError::Proxy(msg) => write!(f, "Proxy error: {}", msg),
        }
    }
}
```

### 7. Update the Main Server Loop to Handle Proxy Requests

Modify your `src/server/mod.rs` to incorporate the proxy handler:

```rust
/// Handle an incoming HTTP request
pub fn handle_request(&self, request: &Request) -> Result<Response> {
    // Get the virtual host configuration based on the Host header
    let host = request.headers.get("host").unwrap_or(&String::from(""));
    let vhost = self.config.get_virtual_host(host);
    
    // Check if this virtual host is configured as a proxy
    if let Some(ref proxy_config) = vhost.proxy {
        debug!("Handling request for proxy virtual host: {}", host);
        
        // This is a proxy virtual host, use the proxy handler
        let proxy_handler = ProxyHandler::new(proxy_config.clone());
        
        // Handle the request asynchronously
        let runtime = tokio::runtime::Runtime::new()
            .map_err(|e| ServerError::Other(format!("Failed to create Tokio runtime: {}", e)))?;
            
        runtime.block_on(async {
            proxy_handler.handle_request(request).await
        })
    } else {
        debug!("Handling request for static file virtual host: {}", host);
        
        // This is a static file virtual host, use the static file handler
        let static_handler = StaticFileHandler::new(&vhost.document_root, &vhost.index_files);
        static_handler.handle_request(request)
    }
}
```

### 8. Configure Sample Proxy Settings in `config.toml`

Add proxy configuration to your config file:

```toml
# ...existing configuration

# Configure virtual hosts section
[[virtual_hosts]]
server_name = "api.example.com"
server_aliases = ["api.*", "*.api.example.com"]

# Proxy configuration for API virtual host
[virtual_hosts.proxy]
connect_timeout = 5
read_timeout = 30
write_timeout = 30
max_idle_connections = 50

# Default backend for any unmatched path
[virtual_hosts.proxy.default_backend]
type = "fixed"
backend = "http://localhost:3000"

# Location-specific routing
[virtual_hosts.proxy.locations]

# API v1 endpoints - round-robin load balancing between multiple backends
"/api/v1/" = { type = "roundrobin", backends = [
    "http://localhost:3001",
    "http://localhost:3002"
], strip_prefix = true, add_headers = { "X-API-Version" = "v1" } }

# API v2 endpoints - random load balancing
"/api/v2/" = { type = "random", backends = [
    "http://localhost:3003",
    "http://localhost:3004"
], add_headers = { "X-API-Version" = "v2" } }

# Static assets - single backend
"/static/" = { type = "fixed", backend = "http://localhost:3005" }

# Admin endpoints - single backend
"/admin/" = { type = "fixed", backend = "http://localhost:3006", 
              add_headers = { "X-Internal" = "true" } }

# Standard static content virtual host
[[virtual_hosts]]
server_name = "www.example.com"
document_root = "/var/www/html"
index_files = ["index.html", "index.htm"]
```

## Advanced Proxy Features

### Load Balancing

You can extend the proxy handler to support load balancing:

```rust
/// Get the backend URL for a request path with load balancing
fn get_backend_for_path(&self, path: &str) -> Result<&str> {
    // Match the path against location patterns in the config
    for (pattern, targets) in &self.config.locations {
        if path.starts_with(pattern) {
            // If there are multiple backends, choose one using round-robin
            if targets.is_array() {
                let backends = targets.as_array().unwrap();
                let index = self.next_backend_index(backends.len());
                return Ok(backends[index].as_str().unwrap());
            } else {
                return Ok(targets.as_str().unwrap());
            }
        }
    }
    
    // ...existing default backend handling...
}

/// Get the next backend index using round-robin
fn next_backend_index(&self, count: usize) -> usize {
    // Use an atomic counter for thread safety
    static COUNTER: AtomicUsize = AtomicUsize::new(0);
    COUNTER.fetch_add(1, Ordering::Relaxed) % count
}
```

### Connection Pooling

The Hyper client already implements connection pooling, but you can tune it:

```rust
let client = Client::builder()
    .pool_max_idle_per_host(20)  // Max idle connections per host
    .pool_idle_timeout(Duration::from_secs(60))
    .build(https);
```

### Header Modification

You may want to modify certain headers in the response:

```rust
// Add or modify response headers
response.headers.insert("X-Served-By".to_string(), 
                        format!("Rusty Server v{}", env!("CARGO_PKG_VERSION")));
```

## Testing and Benchmarking

A robust reverse proxy implementation requires comprehensive testing to ensure reliability under various conditions.

### Unit Testing Strategy

Let's develop comprehensive unit tests for our proxy components:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{LoadBalanceConfig, LocationConfig, ProxyConfig};
    use crate::http::{Method, StatusCode};
    use std::collections::HashMap;
    
    #[test]
    fn test_backend_selector() {
        // Create LocationConfig objects with different backends
        let location1 = LocationConfig {
            backend: LoadBalanceConfig::Fixed {
                backend: "http://api-v1.local".to_string(),
            },
            strip_prefix: false,
            add_headers: HashMap::new(),
        };
        
        let location2 = LocationConfig {
            backend: LoadBalanceConfig::Fixed {
                backend: "http://api-v2.local".to_string(),
            },
            strip_prefix: true,
            add_headers: HashMap::new(),
        };
        
        let mut backends = Vec::new();
        backends.push("http://static1.local".to_string());
        backends.push("http://static2.local".to_string());
        
        let location3 = LocationConfig {
            backend: LoadBalanceConfig::RoundRobin { backends },
            strip_prefix: false,
            add_headers: HashMap::new(),
        };
        
        // Create config with locations
        let mut locations = HashMap::new();
        locations.insert("/api/v1/".to_string(), location1);
        locations.insert("/api/v2/".to_string(), location2);
        locations.insert("/static/".to_string(), location3);
        
        let config = ProxyConfig {
            locations,
            default_backend: Some(LoadBalanceConfig::Fixed {
                backend: "http://default.local".to_string(),
            }),
            connect_timeout: 10,
            read_timeout: 30,
            write_timeout: 30,
            max_idle_connections: 32,
            pass_host_header: true,
        };
        
        let selector = BackendSelector::new(config);
        
        // Test path matching
        let (url1, _) = selector.select_backend("/api/v1/users").unwrap();
        assert_eq!(url1, "http://api-v1.local");
        
        let (url2, location) = selector.select_backend("/api/v2/products").unwrap();
        assert_eq!(url2, "http://api-v2.local");
        assert!(location.strip_prefix);
        
        // Test URL building with prefix stripping
        let full_url = selector.build_target_url(
            &url2,
            "/api/v2/products?id=123",
            location
        );
        assert_eq!(full_url, "http://api-v2.local/products?id=123");
        
        // Test round-robin load balancing
        let (url3_first, _) = selector.select_backend("/static/img").unwrap();
        let (url3_second, _) = selector.select_backend("/static/css").unwrap();
        
        // Should alternate between backends
        assert!(
            (url3_first == "http://static1.local" && url3_second == "http://static2.local") ||
            (url3_first == "http://static2.local" && url3_second == "http://static1.local")
        );
        
        // Test default backend
        let (default_url, _) = selector.select_backend("/unknown/path").unwrap();
        assert_eq!(default_url, "http://default.local");
    }
    
    #[test]
    fn test_header_manipulation() {
        // Create a simple proxy config
        let mut add_headers = HashMap::new();
        add_headers.insert("X-Custom-Header".to_string(), "CustomValue".to_string());
        
        let location = LocationConfig {
            backend: LoadBalanceConfig::Fixed {
                backend: "http://api.local".to_string(),
            },
            strip_prefix: false,
            add_headers,
        };
        
        let mut locations = HashMap::new();
        locations.insert("/api/".to_string(), location);
        
        let config = ProxyConfig {
            locations,
            default_backend: None,
            connect_timeout: 10,
            read_timeout: 30,
            write_timeout: 30,
            max_idle_connections: 32,
            pass_host_header: true,
        };
        
        // Create a test header map
        let mut headers = HeaderMap::new();
        
        // Create a test request
        let mut request = Request::new(Method::GET, "/api/users?id=123".to_string());
        request.headers.insert("Host".to_string(), "example.com".to_string());
        request.headers.insert("User-Agent".to_string(), "Rust-Test".to_string());
        request.headers.insert("Connection".to_string(), "keep-alive".to_string()); // Hop-by-hop header
        request.client_addr = Some("*************".to_string());
        
        // Create the handler and test header manipulation
        let handler = ProxyHandler::new(config.clone());
        let selector = BackendSelector::new(config);
        let (_, location) = selector.select_backend("/api/users").unwrap();
        
        // Copy headers
        handler.copy_headers_to_backend(&request, &mut headers, location).unwrap();
        
        // Verify headers
        assert!(headers.contains_key("user-agent"));
        assert_eq!(headers.get("user-agent").unwrap(), "Rust-Test");
        
        // Should not contain hop-by-hop headers
        assert!(!headers.contains_key("connection"));
        
        // Should contain X-Forwarded headers
        assert!(headers.contains_key("x-forwarded-for"));
        assert_eq!(headers.get("x-forwarded-for").unwrap(), "*************");
        
        // Should contain custom headers from location config
        assert!(headers.contains_key("x-custom-header"));
        assert_eq!(headers.get("x-custom-header").unwrap(), "CustomValue");
        
        // Host header should be set to the backend host
        assert!(headers.contains_key("host"));
        assert_eq!(headers.get("host").unwrap(), "api.local");
    }
}
```

### Integration Testing with Mock Backend Server

For realistic integration tests, we'll create mock backend servers to verify proxy behavior:

```rust
#[cfg(test)]
mod integration_tests {
    use super::*;
    use std::sync::{Arc, Mutex};
    use tokio::net::TcpListener;
    use tokio::io::{AsyncReadExt, AsyncWriteExt};
    use tokio::task;
    use std::time::Duration;
    
    /// Starts a mock HTTP backend server and returns its address
    async fn start_mock_backend(
        response_delay_ms: u64,
        status_code: u16,
        response_headers: Vec<(&str, &str)>,
    ) -> String {
        // Create a listener on a random port
        let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
        let addr = listener.local_addr().unwrap();
        let server_url = format!("http://127.0.0.1:{}", addr.port());
        
        // Store request information for verification
        let request_log = Arc::new(Mutex::new(Vec::new()));
        let request_log_clone = request_log.clone();
        
        // Start server task
        task::spawn(async move {
            while let Ok((mut socket, _)) = listener.accept().await {
                let request_log = request_log_clone.clone();
                let response_delay = response_delay_ms;
                let status = status_code;
                let headers = response_headers.clone();
                
                task::spawn(async move {
                    // Read the request
                    let mut buffer = vec![0u8; 8192];
                    let n = socket.read(&mut buffer).await.unwrap();
                    let request_str = String::from_utf8_lossy(&buffer[..n]).to_string();
                    
                    // Log the request for verification
                    let mut log = request_log.lock().unwrap();
                    log.push(request_str.clone());
                    
                    // Parse request to extract path
                    let path = request_str
                        .lines()
                        .next()
                        .unwrap_or("")
                        .split_whitespace()
                        .nth(1)
                        .unwrap_or("/unknown");
                    
                    // Extract headers
                    let mut request_headers = HashMap::new();
                    for line in request_str.lines().skip(1) {
                        if line.is_empty() { break; }
                        if let Some(pos) = line.find(':') {
                            let (key, val) = line.split_at(pos);
                            let val = val.trim_start_matches(':').trim();
                            request_headers.insert(key.to_lowercase(), val.to_string());
                        }
                    }
                    
                    // Apply configured delay
                    if response_delay > 0 {
                        tokio::time::sleep(Duration::from_millis(response_delay)).await;
                    }
                    
                    // Generate response
                    let body = format!("{{\"path\": \"{}\", \"headers\": {{", path);
                    let mut headers_json = Vec::new();
                    
                    for (key, value) in &request_headers {
                        headers_json.push(format!("\"{}\":\"{}\"", key, value));
                    }
                    
                    let body = format!(
                        "{}{}}}, \"method\": \"{}\"}}",
                        body,
                        headers_json.join(","),
                        request_str.lines().next().unwrap_or("").split_whitespace().next().unwrap_or("GET")
                    );
                    
                    // Build response with headers
                    let mut response = format!(
                        "HTTP/1.1 {} OK\r\n\
                        Content-Type: application/json\r\n\
                        Content-Length: {}\r\n",
                        status, body.len()
                    );
                    
                    // Add custom headers
                    for (name, value) in &headers {
                        response.push_str(&format!("{}: {}\r\n", name, value));
                    }
                    
                    response.push_str("\r\n");
                    response.push_str(&body);
                    
                    socket.write_all(response.as_bytes()).await.unwrap();
                });
            }
        });
        
        server_url
    }
    
    #[tokio::test]
    async fn test_proxy_request_forwarding() {
        // Start mock backend server
        let backend_url = start_mock_backend(
            0, // no delay
            200, // status code
            vec![("X-Test", "Backend")]
        ).await;
        
        // Create proxy configuration with the mock backend
        let mut locations = HashMap::new();
        let location = LocationConfig {
            backend: LoadBalanceConfig::Fixed {
                backend: backend_url.clone(),
            },
            strip_prefix: false,
            add_headers: {
                let mut h = HashMap::new();
                h.insert("X-Proxy-Test".to_string(), "TestValue".to_string());
                h
            },
        };
        locations.insert("/api/".to_string(), location);
        
        let config = ProxyConfig {
            locations,
            default_backend: None,
            connect_timeout: 1,
            read_timeout: 1,
            write_timeout: 1,
            max_idle_connections: 32,
            pass_host_header: true,
        };
        
        // Create proxy handler
        let handler = ProxyHandler::new(config);
        
        // Create test request
        let mut request = Request::new(Method::GET, "/api/users?id=123".to_string());
        request.headers.insert("Host".to_string(), "example.com".to_string());
        request.headers.insert("User-Agent".to_string(), "Rust-Test".to_string());
        request.client_addr = Some("*************".to_string());
        
        // Send request through proxy
        let response = handler.handle_request(&request).await.unwrap();
        
        // Verify response
        assert_eq!(response.status, StatusCode::OK);
        assert!(response.headers.contains_key("content-type"));
        assert_eq!(response.headers.get("content-type").unwrap(), "application/json");
        assert!(response.headers.contains_key("x-test"));
        assert_eq!(response.headers.get("x-test").unwrap(), "Backend");
        
        // Parse response body as JSON
        let body_str = String::from_utf8_lossy(&response.body);
        assert!(body_str.contains("\"path\":\"/api/users?id=123\""));
        assert!(body_str.contains("\"x-proxy-test\":\"TestValue\""));
        assert!(body_str.contains("\"x-forwarded-for\":\"*************\""));
    }
    
    #[tokio::test]
    async fn test_proxy_timeout_handling() {
        // Start mock backend server with delay longer than timeout
        let backend_url = start_mock_backend(
            1500, // 1.5 second delay (longer than timeout)
            200,
            vec![]
        ).await;
        
        // Create proxy configuration with short timeout
        let mut locations = HashMap::new();
        let location = LocationConfig {
            backend: LoadBalanceConfig::Fixed {
                backend: backend_url,
            },
            strip_prefix: false,
            add_headers: HashMap::new(),
        };
        locations.insert("/".to_string(), location);
        
        let config = ProxyConfig {
            locations,
            default_backend: None,
            connect_timeout: 1,  // 1 second timeout
            read_timeout: 1,
            write_timeout: 1,
            max_idle_connections: 32,
            pass_host_header: true,
        };
        
        // Create proxy handler
        let handler = ProxyHandler::new(config);
        
        // Create test request
        let request = Request::new(Method::GET, "/api/slow".to_string());
        
        // Request should time out
        let result = handler.handle_request(&request).await;
        assert!(result.is_err());
        
        // Verify error type
        if let Err(ServerError::Proxy(msg)) = result {
            assert!(msg.contains("timeout") || msg.contains("timed out"));
        } else {
            panic!("Expected timeout error, got: {:?}", result);
        }
    }
    
    #[tokio::test]
    async fn test_load_balancing() {
        // Start two backend servers
        let backend_url1 = start_mock_backend(
            0, 200,
            vec![("X-Server-ID", "backend1")]
        ).await;
        
        let backend_url2 = start_mock_backend(
            0, 200,
            vec![("X-Server-ID", "backend2")]
        ).await;
        
        // Create proxy configuration with round-robin
        let mut locations = HashMap::new();
        let location = LocationConfig {
            backend: LoadBalanceConfig::RoundRobin {
                backends: vec![backend_url1, backend_url2],
            },
            strip_prefix: false,
            add_headers: HashMap::new(),
        };
        locations.insert("/api/".to_string(), location);
        
        let config = ProxyConfig {
            locations,
            default_backend: None,
            connect_timeout: 1,
            read_timeout: 1,
            write_timeout: 1,
            max_idle_connections: 32,
            pass_host_header: true,
        };
        
        // Create proxy handler
        let handler = ProxyHandler::new(config);
        
        // Send multiple requests and check load balancing
        let mut response1 = handler.handle_request(
            &Request::new(Method::GET, "/api/test1".to_string())
        ).await.unwrap();
        
        let mut response2 = handler.handle_request(
            &Request::new(Method::GET, "/api/test2".to_string())
        ).await.unwrap();
        
        // Get server IDs
        let server1 = response1.headers.get("x-server-id").unwrap();
        let server2 = response2.headers.get("x-server-id").unwrap();
        
        // Servers should be different due to round-robin
        assert_ne!(server1, server2);
        assert!(server1 == "backend1" || server1 == "backend2");
        assert!(server2 == "backend1" || server2 == "backend2");
    }
}
```

### System Testing with Real Backends

For a more complete test environment, we can create a test script to simulate a real-world setup:

```bash
#!/bin/bash
# test_proxy.sh - Test script for reverse proxy system testing

# Start backend servers for testing
echo "Starting test backend servers..."

# API v1 backend on port 3001
node -e "
const http = require('http');
const server = http.createServer((req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('X-Backend', 'api-v1');
  
  const responseData = {
    version: 'v1',
    path: req.url,
    headers: req.headers,
    server: 'api-v1'
  };
  
  res.end(JSON.stringify(responseData, null, 2));
});

server.listen(3001, '127.0.0.1', () => {
  console.log('API v1 backend running on port 3001');
});" &

# API v2 backend on port 3002
node -e "
const http = require('http');
const server = http.createServer((req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('X-Backend', 'api-v2');
  
  const responseData = {
    version: 'v2',
    path: req.url,
    headers: req.headers,
    server: 'api-v2'
  };
  
  res.end(JSON.stringify(responseData, null, 2));
});

server.listen(3002, '127.0.0.1', () => {
  console.log('API v2 backend running on port 3002');
});" &

# Static content backend on port 3003
node -e "
const http = require('http');
const server = http.createServer((req, res) => {
  res.setHeader('Content-Type', 'text/html');
  res.setHeader('X-Backend', 'static');
  
  const html = \`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Static Content Test</title>
    </head>
    <body>
      <h1>Static Content</h1>
      <p>Path: \${req.url}</p>
      <p>Backend: static</p>
    </body>
    </html>
  \`;
  
  res.end(html);
});

server.listen(3003, '127.0.0.1', () => {
  console.log('Static content backend running on port 3003');
});" &

# Wait for servers to start
sleep 2

echo "Testing proxy functionality..."

# Test API v1 endpoint
echo -e "\n==== Testing API v1 endpoint ===="
curl -s -H "Host: api.example.com" http://localhost:8080/api/v1/users | jq .

# Test API v2 endpoint
echo -e "\n==== Testing API v2 endpoint ===="
curl -s -H "Host: api.example.com" http://localhost:8080/api/v2/products | jq .

# Test static content
echo -e "\n==== Testing static content endpoint ===="
curl -s -H "Host: api.example.com" http://localhost:8080/static/index.html | grep -E "Static Content|Path:|Backend:"

# Test headers forwarding
echo -e "\n==== Testing request headers forwarding ===="
curl -s -H "Host: api.example.com" -H "X-Custom-Header: TestValue" http://localhost:8080/api/v1/headers | jq .headers

# Cleanup
echo -e "\n==== Cleaning up ===="
pkill -f "node -e"
echo "All test backend servers stopped"
```

### Performance Benchmarking

To evaluate the performance of our reverse proxy implementation, we'll use industry-standard benchmarking tools:

```bash
# Install benchmarking tools if needed
# apt-get install apache2-utils   # For ab
# brew install wrk                # For wrk on macOS

# Start backend server for benchmark testing
node -e "
const http = require('http');
const server = http.createServer((req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.end(JSON.stringify({ success: true, timestamp: Date.now() }));
});

server.listen(3001, '127.0.0.1', () => {
  console.log('Benchmark backend running on port 3001');
});" &

sleep 1

echo "Running direct backend benchmark..."
wrk -t4 -c100 -d30s http://localhost:3001/api/test

echo "Running proxy benchmark..."
wrk -t4 -c100 -d30s -H "Host: api.example.com" http://localhost:8080/api/v1/test

# Run Apache benchmark tool for detailed statistics
echo "Running detailed benchmark with ab..."
ab -n 10000 -c 100 -H "Host: api.example.com" http://localhost:8080/api/v1/test

# Cleanup
pkill -f "node -e"
```

#### Sample Benchmark Results and Analysis

Here are typical benchmark results comparing direct backend and proxied requests:

```
# Direct backend results
Running 30s test @ http://localhost:3001/api/test
  4 threads and 100 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     7.82ms    1.98ms   56.11ms   89.62%
    Req/Sec     3.21k     0.45k     4.10k    73.25%
  383,762 requests in 30.09s, 67.31MB read
Requests/sec:  12,754.58
Transfer/sec:      2.24MB

# Proxied backend results
Running 30s test @ http://localhost:8080/api/v1/test
  4 threads and 100 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency    11.43ms    3.66ms   67.28ms   84.77%
    Req/Sec     2.19k     0.32k     3.25k    68.94%
  261,475 requests in 30.10s, 48.72MB read
Requests/sec:   8,686.62
Transfer/sec:      1.62MB
```

**Analysis:**

1. **Throughput Impact**: The proxy handles approximately 68% of the direct backend throughput (8,687 req/sec vs 12,755 req/sec), showing a 32% overhead.

2. **Latency Impact**: The proxy adds about 3.6ms (46%) to the average latency (11.43ms vs 7.82ms).

3. **Standard Deviation**: The proxy shows higher variability in response times (3.66ms vs 1.98ms), indicating less consistent performance.

4. **Resource Consumption**: The proxy uses additional memory and CPU for connection management, request/response processing, and header manipulation.

These results are within expected parameters for a reverse proxy implementation and demonstrate that our Rust implementation has acceptable performance characteristics.

## Performance Optimization

To optimize our reverse proxy for high-performance scenarios, we can implement several strategies:

### Connection Pooling Optimization

Connection pooling significantly improves performance by reusing TCP connections to backend servers:

```rust
// Advanced connection pool configuration
pub struct ConnectionPoolConfig {
    /// Maximum number of idle connections per host
    pub max_idle_per_host: usize,
    /// Maximum idle time for connections before closing
    pub idle_timeout: Duration,
    /// Whether to enable HTTP/2 support
    pub enable_http2: bool,
    /// Whether to enable connection keep-alive
    pub keep_alive: bool,
    /// TCP keepalive configuration
    pub tcp_keepalive: Option<Duration>,
    /// TCP nodelay setting (Nagle's algorithm)
    pub tcp_nodelay: bool,
}

impl Default for ConnectionPoolConfig {
    fn default() -> Self {
        Self {
            max_idle_per_host: 32,
            idle_timeout: Duration::from_secs(60),
            enable_http2: true,
            keep_alive: true,
            tcp_keepalive: Some(Duration::from_secs(60)),
            tcp_nodelay: true,
        }
    }
}

// Creating a client with optimized connection pooling
fn create_optimized_client(config: &ConnectionPoolConfig) -> Client<HttpsConnector<HttpConnector>> {
    // Create a custom connector
    let mut http = HttpConnector::new();
    http.set_keepalive(config.tcp_keepalive);
    http.set_nodelay(config.tcp_nodelay);
    
    // HTTPS connector
    let mut https = HttpsConnector::new_with_connector(http);
    
    // Build the client with optimized settings
    Client::builder()
        .pool_idle_timeout(config.idle_timeout)
        .pool_max_idle_per_host(config.max_idle_per_host)
        .http2_only(config.enable_http2)
        .build(https)
}
```

### Response Streaming

For large responses, using streaming instead of buffering the entire response can significantly reduce memory usage and improve response time to the first byte:

```rust
async fn stream_response(&self, hyper_resp: hyper::Response<Body>) -> Result<Response> {
    // Extract status code and headers
    let status = StatusCode::from_u16(hyper_resp.status().as_u16())
        .unwrap_or(StatusCode::InternalServerError);
    
    let mut headers = HashMap::new();
    for (name, value) in hyper_resp.headers() {
        if let Ok(value_str) = value.to_str() {
            headers.insert(name.as_str().to_string(), value_str.to_string());
        }
    }
    
    // Create a streaming response
    let (sender, body) = hyper::Body::channel();
    
    // Create our response with streaming body
    let response = Response::new_streaming(status, headers, body);
    
    // Process the incoming body in a separate task to avoid blocking
    let read_timeout = self.config.read_timeout;
    tokio::spawn(async move {
        let mut hyper_body = hyper_resp.into_body();
        
        loop {
            // Read the next chunk with timeout
            let chunk = match tokio::time::timeout(
                Duration::from_secs(read_timeout),
                hyper_body.data()
            ).await {
                Ok(Some(Ok(chunk))) => chunk,
                Ok(Some(Err(e))) => {
                    error!("Error reading response chunk: {}", e);
                    break;
                },
                Ok(None) => break, // End of body
                Err(_) => {
                    error!("Timeout reading response chunk");
                    break;
                }
            };
            
            // Forward the chunk to the client
            if sender.send_data(chunk).await.is_err() {
                // Client disconnected, stop reading
                break;
            }
        }
    });
    
    Ok(response)
}
```

### Buffer Size Tuning

Adjusting buffer sizes can significantly impact performance:

```rust
// Constants for optimal buffer sizes
const REQUEST_BUFFER_SIZE: usize = 64 * 1024;  // 64 KB for request buffering
const RESPONSE_CHUNK_SIZE: usize = 128 * 1024; // 128 KB for response chunks

async fn convert_hyper_response_optimized(&self, hyper_resp: hyper::Response<Body>) -> Result<Response> {
    // Implementation with custom buffer sizes
    // ...
    
    // Read with specific buffer size
    let mut body_bytes = Vec::with_capacity(RESPONSE_CHUNK_SIZE);
    let mut body_stream = hyper_resp.into_body();
    
    while let Some(chunk) = body_stream.data().await {
        let chunk = chunk.map_err(|e| ServerError::Proxy(format!("Error reading response: {}", e)))?;
        body_bytes.extend_from_slice(&chunk);
    }
    
    // ...
}
```

### Request Pipelining

Implement request pipelining for handling multiple requests to the same backend:

```rust
// Example of pipelined requests
async fn send_pipelined_requests(&self, requests: Vec<&Request>, backend: &str) -> Vec<Result<Response>> {
    let mut results = vec![];
    let mut futures = vec![];
    
    // Create all request futures at once
    for request in requests {
        let future = self.send_request_to_backend(request, backend);
        futures.push(future);
    }
    
    // Wait for all requests to complete
    for future in futures {
        let result = future.await;
        results.push(result);
    }
    
    results
}
```

### Performance Measurement and Metrics

Implement metrics collection to identify performance bottlenecks:

```rust
/// Proxy metrics collector
pub struct ProxyMetrics {
    /// Request count
    pub request_count: AtomicUsize,
    /// Error count
    pub error_count: AtomicUsize,
    /// Total request time in milliseconds
    pub total_request_time_ms: AtomicUsize,
    /// Bytes received from backends
    pub bytes_received: AtomicUsize,
    /// Bytes sent to backends
    pub bytes_sent: AtomicUsize,
    /// Requests by status code
    pub status_codes: Mutex<HashMap<u16, usize>>,
}

impl ProxyMetrics {
    pub fn new() -> Self {
        Self {
            request_count: AtomicUsize::new(0),
            error_count: AtomicUsize::new(0),
            total_request_time_ms: AtomicUsize::new(0),
            bytes_received: AtomicUsize::new(0),
            bytes_sent: AtomicUsize::new(0),
            status_codes: Mutex::new(HashMap::new()),
        }
    }
    
    pub fn record_request(&self, duration_ms: u64, status: u16, bytes_sent: usize, bytes_received: usize) {
        self.request_count.fetch_add(1, Ordering::Relaxed);
        self.total_request_time_ms.fetch_add(duration_ms as usize, Ordering::Relaxed);
        self.bytes_sent.fetch_add(bytes_sent, Ordering::Relaxed);
        self.bytes_received.fetch_add(bytes_received, Ordering::Relaxed);
        
        let mut status_codes = self.status_codes.lock().unwrap();
        *status_codes.entry(status).or_insert(0) += 1;
    }
    
    pub fn record_error(&self) {
        self.error_count.fetch_add(1, Ordering::Relaxed);
    }
    
    pub fn get_average_response_time(&self) -> f64 {
        let total_time = self.total_request_time_ms.load(Ordering::Relaxed);
        let request_count = self.request_count.load(Ordering::Relaxed);
        
        if request_count > 0 {
            total_time as f64 / request_count as f64
        } else {
            0.0
        }
    }
    
    pub fn print_report(&self) {
        println!("===== Proxy Performance Report =====");
        println!("Total requests: {}", self.request_count.load(Ordering::Relaxed));
        println!("Error rate: {:.2}%", 
            self.error_count.load(Ordering::Relaxed) as f64 / 
            self.request_count.load(Ordering::Relaxed) as f64 * 100.0);
        println!("Average response time: {:.2}ms", self.get_average_response_time());
        println!("Total data sent: {}KB", self.bytes_sent.load(Ordering::Relaxed) / 1024);
        println!("Total data received: {}KB", self.bytes_received.load(Ordering::Relaxed) / 1024);
        
        println!("\nStatus Code Distribution:");
        let status_codes = self.status_codes.lock().unwrap();
        let mut codes: Vec<(&u16, &usize)> = status_codes.iter().collect();
        codes.sort_by_key(|&(code, _)| code);
        
        for (code, count) in codes {
            println!("  {}: {}", code, count);
        }
    }
}
```

## Security Considerations

Reverse proxies present unique security challenges as they bridge external clients and internal services. This section outlines essential security considerations and implementations to protect your web infrastructure.

### Threat Model for Reverse Proxies

Before implementing security measures, understand the specific threats facing reverse proxies:

```mermaid
flowchart TD
    A[Attacker] -->|1. Protocol-based attacks| RP[Reverse Proxy]
    A -->|2. Path traversal attacks| RP
    A -->|3. DDoS attacks| RP
    A -->|4. Header injection| RP
    A -->|5. Backend enumeration| RP
    A -->|6. Information disclosure| RP
    
    RP --> B1[Backend 1]
    RP --> B2[Backend 2]
    RP --> B3[Backend 3]
    
    class A fill:#f96,stroke:#333
    class RP fill:#69f,stroke:#333
    class B1,B2,B3 fill:#6d9,stroke:#333
```

### Request and Response Validation

Implement comprehensive validation for incoming requests and outgoing responses to protect against malicious traffic:

```rust
/// Constants for request validation
const MAX_HEADERS: usize = 100;
const MAX_PATH_LENGTH: usize = 2048;
const MAX_REQUEST_SIZE: usize = 10 * 1024 * 1024; // 10 MB
const ALLOWED_METHODS: &[Method] = &[
    Method::GET, Method::POST, Method::PUT, 
    Method::DELETE, Method::HEAD, Method::OPTIONS
];

/// Validate incoming request before proxying
fn validate_request(&self, request: &Request) -> Result<()> {
    // Check for oversized headers
    if request.headers.len() > MAX_HEADERS {
        error!("Request rejected: Too many headers ({})", request.headers.len());
        return Err(ServerError::Proxy("Too many headers".to_string()));
    }
    
    // Check for very large paths
    if request.path.len() > MAX_PATH_LENGTH {
        error!("Request rejected: Path too long ({})", request.path.len());
        return Err(ServerError::Proxy("Path too long".to_string()));
    }
    
    // Check for disallowed request methods
    if !ALLOWED_METHODS.contains(&request.method) {
        error!("Request rejected: Method not allowed ({})", request.method);
        return Err(ServerError::Proxy(format!("Method not allowed: {}", request.method)));
    }
    
    // Check content length (if present)
    if let Some(content_length) = request.headers.get("content-length") {
        if let Ok(length) = content_length.parse::<usize>() {
            if length > MAX_REQUEST_SIZE {
                error!("Request rejected: Body too large ({})", length);
                return Err(ServerError::Proxy("Request body too large".to_string()));
            }
        }
    }
    
    // Check for potential path traversal attacks
    if self.contains_path_traversal(&request.path) {
        error!("Request rejected: Path traversal attempt ({})", request.path);
        return Err(ServerError::Proxy("Invalid path".to_string()));
    }
    
    // Check for suspicious query parameters
    if let Some(query) = request.path.split('?').nth(1) {
        if self.contains_suspicious_patterns(query) {
            error!("Request rejected: Suspicious query parameters");
            return Err(ServerError::Proxy("Invalid query parameters".to_string()));
        }
    }
    
    Ok(())
}

/// Check for path traversal attempts
fn contains_path_traversal(&self, path: &str) -> bool {
    // Check for basic directory traversal patterns
    if path.contains("..") || path.contains("//") {
        return true;
    }
    
    // Check for encoded traversal patterns
    let lowercase = path.to_lowercase();
    if lowercase.contains("%2e%2e") || // ..
       lowercase.contains("%2e%2e%2f") || // ../
       lowercase.contains("%2f%2e%2e") || // /..
       lowercase.contains("%252e%252e") { // Double-encoded ..
        return true;
    }
    
    false
}

/// Check for suspicious patterns in query parameters
fn contains_suspicious_patterns(&self, query: &str) -> bool {
    // Check for SQL injection patterns
    let lowercase = query.to_lowercase();
    
    if lowercase.contains("select+") ||
       lowercase.contains("union+") ||
       lowercase.contains("exec(") ||
       lowercase.contains("eval(") ||
       lowercase.contains("execute+") ||
       lowercase.contains("--+") {
        return true;
    }
    
    // Check for XSS patterns
    if query.contains("<script") ||
       query.contains("javascript:") ||
       query.contains("onerror=") ||
       query.contains("onload=") {
        return true;
    }
    
    false
}
```

### HTTP Header Security

HTTP headers are critical for security. Implement proper header processing to prevent information leakage and protect against common attacks:

```mermaid
flowchart LR
    subgraph "Header Security Flow"
        A[Client Request] --> B[Request Header Processing]
        B --> C{Security Checks}
        C -->|Pass| D[Modify Headers]
        C -->|Fail| E[Reject Request]
        D --> F[Forward to Backend]
        F --> G[Response Header Processing]
        G --> H[Response to Client]
    end
    
    style A fill:#f9d0c4,stroke:#333
    style B,D,G fill:#c4f9d0,stroke:#333
    style C fill:#f0e68c,stroke:#333
    style E fill:#f08080,stroke:#333
    style F,H fill:#c4d0f9,stroke:#333
```

#### Header Processing Implementation

```rust
/// Headers that should never be forwarded (hop-by-hop headers)
const HOP_BY_HOP_HEADERS: &[&str] = &[
    "connection",
    "keep-alive", 
    "proxy-authenticate",
    "proxy-authorization",
    "te",
    "trailer",
    "transfer-encoding",
    "upgrade",
    "sec-websocket-key",
    "sec-websocket-extensions",
    "sec-websocket-accept",
    "sec-websocket-protocol",
    "sec-websocket-version",
    "proxy-connection",
];

/// Headers that might contain sensitive information
const SENSITIVE_HEADERS: &[&str] = &[
    "authorization",
    "cookie",
    "x-csrf-token",
    "x-api-key",
    "private-token",
    "x-auth-token",
    "access-token",
    "jwt-token",
    "session-token",
];

/// Headers that could reveal backend server implementation details
const SERVER_IDENTIFICATION_HEADERS: &[&str] = &[
    "server",
    "x-powered-by",
    "x-aspnet-version",
    "x-runtime",
    "x-generator",
    "x-drupal-cache",
    "x-drupal-dynamic-cache",
    "x-magento-cache",
    "x-shopify-stage",
    "x-wp-engine",
    "laravel_session",
    "cf-ray",
    "x-amz-id-2",
    "x-amz-request-id",
    "x-oracle-dms-ecid",
];

/// Security headers to add to all responses
const SECURITY_HEADERS: &[(&str, &str)] = &[
    // Basic security headers
    ("x-content-type-options", "nosniff"),
    ("x-frame-options", "SAMEORIGIN"),
    ("x-xss-protection", "1; mode=block"),
    ("referrer-policy", "strict-origin-when-cross-origin"),
    ("x-permitted-cross-domain-policies", "none"),
    // Feature and permission policies
    ("permissions-policy", "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()"),
];

/// Process request headers before forwarding to backend
fn process_request_headers(&self, request: &Request, headers: &mut HeaderMap) -> Result<()> {
    // Copy allowed headers, excluding hop-by-hop and sensitive ones
    for (name, value) in &request.headers {
        let lowercase_name = name.to_lowercase();
        
        // Skip hop-by-hop headers
        if HOP_BY_HOP_HEADERS.contains(&lowercase_name.as_str()) {
            continue;
        }
        
        // Process sensitive headers according to policy
        if SENSITIVE_HEADERS.contains(&lowercase_name.as_str()) {
            // Option 1: Skip sensitive headers completely
            // continue;
            
            // Option 2: Add placeholder for debugging
            // headers.insert(HeaderName::from_str(name)?, HeaderValue::from_static("[FILTERED]"));
            
            // Option 3: Forward sensitive headers (only for trusted backends)
            if self.config.forward_sensitive_headers && 
               self.is_trusted_backend(headers.get("host").unwrap_or(&"".to_string())) {
                headers.insert(HeaderName::from_str(name)?, HeaderValue::from_str(value)?);
            }
            
            continue;
        }
        
        // Add standard headers
        if let (Ok(header_name), Ok(header_value)) = (
            HeaderName::from_str(name),
            HeaderValue::from_str(value)
        ) {
            headers.insert(header_name, header_value);
        }
    }
    
    // Add standard proxy headers
    self.add_proxy_headers(request, headers)?;
    
    // Add optional authentication headers (for internal auth requirements)
    if self.config.requires_authentication && self.is_authenticated(request)? {
        self.add_auth_headers(request, headers)?;
    }
    
    Ok(())
}

/// Add standard proxy headers
fn add_proxy_headers(&self, request: &Request, headers: &mut HeaderMap) -> Result<()> {
    // X-Forwarded-For: Append client IP to existing header or create new one
    let mut forwarded_for = String::new();
    
    if let Some(existing) = request.headers.get("x-forwarded-for") {
        // Validate existing X-Forwarded-For to prevent header injection
        if self.is_valid_ip_list(existing) {
            forwarded_for.push_str(existing);
            forwarded_for.push_str(", ");
        } else {
            warn!("Invalid X-Forwarded-For header detected: {}", existing);
            // Start with empty list rather than trusting invalid data
        }
    }
    
    if let Some(client_ip) = &request.client_addr {
        forwarded_for.push_str(client_ip);
    } else {
        forwarded_for.push_str("unknown");
    }
    
    headers.insert(
        HeaderName::from_static("x-forwarded-for"),
        HeaderValue::from_str(&forwarded_for)?
    );
    
    // X-Forwarded-Proto
    let proto = if request.headers.get("x-forwarded-proto")
                     .unwrap_or(&"http".to_string()) == "https" {
        "https"
    } else {
        "http"
    };
    
    headers.insert(
        HeaderName::from_static("x-forwarded-proto"),
        HeaderValue::from_static(proto)
    );
    
    // X-Forwarded-Host
    if let Some(host) = request.headers.get("host") {
        headers.insert(
            HeaderName::from_static("x-forwarded-host"),
            HeaderValue::from_str(host)?
        );
    }
    
    // Add request ID for tracing
    let request_id = Uuid::new_v4().to_string();
    headers.insert(
        HeaderName::from_static("x-request-id"),
        HeaderValue::from_str(&request_id)?
    );
    
    // RFC 7239 Forwarded header (standardized version)
    let mut forwarded = format!("for={}", request.client_addr.clone().unwrap_or("unknown".to_string()));
    if let Some(host) = request.headers.get("host") {
        forwarded.push_str(&format!(";host={}", host));
    }
    forwarded.push_str(&format!(";proto={}", proto));
    
    headers.insert(
        HeaderName::from_static("forwarded"),
        HeaderValue::from_str(&forwarded)?
    );
    
    Ok(())
}

/// Validate IP address list format to prevent header injection
fn is_valid_ip_list(&self, ip_list: &str) -> bool {
    let ip_regex = Regex::new(r"^[\d\.:,\s\[\]a-fA-F]+$").unwrap();
    ip_regex.is_match(ip_list)
}

/// Add authentication headers for backend services
fn add_auth_headers(&self, request: &Request, headers: &mut HeaderMap) -> Result<()> {
    // Add user identity if available
    if let Some(user_id) = self.get_authenticated_user_id(request)? {
        headers.insert(
            HeaderName::from_static("x-authenticated-user"),
            HeaderValue::from_str(&user_id)?
        );
    }
    
    // Add roles/groups if available
    if let Some(roles) = self.get_authenticated_user_roles(request)? {
        headers.insert(
            HeaderName::from_static("x-user-roles"),
            HeaderValue::from_str(&roles.join(","))?
        );
    }
    
    // Add any internal authentication token for service-to-service auth
    if self.config.add_backend_auth_token {
        headers.insert(
            HeaderName::from_static("x-internal-auth-token"),
            HeaderValue::from_str(&self.generate_backend_auth_token()?)?
        );
    }
    
    Ok(())
}

/// Process response headers before sending back to client
fn process_response_headers(&self, response: &mut Response) -> Result<()> {
    // Remove server identification headers
    for header in SERVER_IDENTIFICATION_HEADERS {
        response.headers.remove(*header);
    }
    
    // Add security headers
    for (name, value) in SECURITY_HEADERS {
        response.headers.insert(
            name.to_string(),
            value.to_string()
        );
    }
    
    // Add Content-Security-Policy header if enabled
    if self.config.add_csp_header {
        response.headers.insert(
            "content-security-policy".to_string(),
            self.build_content_security_policy().to_string()
        );
    }
    
    // Add HSTS header for HTTPS connections
    if request_uses_https(response) {
        response.headers.insert(
            "strict-transport-security".to_string(),
            "max-age=31536000; includeSubDomains; preload".to_string()
        );
    }
    
    // Add server identification
    response.headers.insert(
        "server".to_string(),
        format!("Rusty Proxy {}", env!("CARGO_PKG_VERSION"))
    );
    
    Ok(())
}

/// Build Content-Security-Policy header values
fn build_content_security_policy(&self) -> String {
    // Example CSP configuration for a web application
    // Customize this based on your application's requirements
    let mut csp = vec![
        "default-src 'self'",
        "script-src 'self' https://trusted-cdn.example.com",
        "style-src 'self' https://trusted-cdn.example.com 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self' https://trusted-cdn.example.com",
        "connect-src 'self' https://api.example.com",
        "media-src 'none'",
        "object-src 'none'",
        "frame-src 'self'",
        "frame-ancestors 'self'",
        "form-action 'self'",
        "base-uri 'self'",
        "upgrade-insecure-requests",
    ];
    
    // Add any additional CSP directives from configuration
    if let Some(additional_directives) = &self.config.csp_additional_directives {
        for directive in additional_directives {
            csp.push(directive);
        }
    }
    
    // Join all directives with semicolons
    csp.join("; ")
}
```

### Cross-Site Request Forgery (CSRF) Protection

CSRF attacks occur when malicious websites cause a user's browser to perform unwanted actions on a site where they're authenticated. Implement proper CSRF protection in your reverse proxy:

```rust
/// CSRF protection configuration
pub struct CsrfProtection {
    /// Whether CSRF protection is enabled
    enabled: bool,
    /// Paths exempt from CSRF checks
    exempt_paths: HashSet<String>,
    /// Allowed referrer/origin domains
    allowed_domains: HashSet<String>,
    /// Whether to enforce CSRF token presence
    enforce_token: bool,
    /// Name of the CSRF token in headers
    token_header_name: String,
    /// Name of the CSRF token in forms
    token_form_name: String,
    /// Name of the CSRF token in cookies
    token_cookie_name: String,
}

impl CsrfProtection {
    /// Create a new CSRF protection configuration
    pub fn new() -> Self {
        let mut exempt_paths = HashSet::new();
        exempt_paths.insert("/api/health".to_string());
        exempt_paths.insert("/api/metrics".to_string());
        
        let mut allowed_domains = HashSet::new();
        allowed_domains.insert("example.com".to_string());
        allowed_domains.insert("www.example.com".to_string());
        
        Self {
            enabled: true,
            exempt_paths,
            allowed_domains,
            enforce_token: true,
            token_header_name: "X-CSRF-Token".to_string(),
            token_form_name: "_csrf_token".to_string(),
            token_cookie_name: "csrf_token".to_string(),
        }
    }
    
    /// Check if a request passes CSRF protection
    pub fn validate_request(&self, request: &Request) -> Result<()> {
        // Skip if CSRF protection is disabled
        if !self.enabled {
            return Ok(());
        }
        
        // Skip CSRF checks for GET, HEAD, OPTIONS requests
        if request.method == Method::GET || 
           request.method == Method::HEAD || 
           request.method == Method::OPTIONS {
            return Ok(());
        }
        
        // Skip CSRF checks for exempt paths
        for exempt_path in &self.exempt_paths {
            if request.path.starts_with(exempt_path) {
                return Ok(());
            }
        }
        
        // Check referer/origin against allowed domains
        let referer = request.headers.get("referer").unwrap_or(&"".to_string());
        let origin = request.headers.get("origin").unwrap_or(&"".to_string());
        let host = request.headers.get("host").unwrap_or(&"".to_string());
        
        let referer_valid = self.check_domain_origin(referer);
        let origin_valid = self.check_domain_origin(origin);
        
        // Either referer or origin must be valid
        if !referer_valid && !origin_valid {
            warn!("CSRF validation failed: invalid referer/origin for {:?} from {:?}", 
                  request.path, request.client_addr);
            return Err(ServerError::Csrf("Invalid referer/origin".to_string()));
        }
        
        // Check CSRF token if enforced
        if self.enforce_token {
            self.validate_csrf_token(request)?;
        }
        
        Ok(())
    }
    
    /// Check if domain origin is allowed
    fn check_domain_origin(&self, url: &str) -> bool {
        if url.is_empty() {
            return false;
        }
        
        if let Ok(parsed_url) = url::Url::parse(url) {
            if let Some(domain) = parsed_url.host_str() {
                return self.allowed_domains.contains(domain);
            }
        }
        
        false
    }
    
    /// Validate CSRF token in request
    fn validate_csrf_token(&self, request: &Request) -> Result<()> {
        // Get the expected token from cookie
        let cookie_header = request.headers.get("cookie").unwrap_or(&"".to_string());
        let expected_token = self.extract_token_from_cookie(cookie_header);
        
        if expected_token.is_empty() {
            return Err(ServerError::Csrf("Missing CSRF token cookie".to_string()));
        }
        
        // Check header token
        if let Some(header_token) = request.headers.get(&self.token_header_name) {
            if self.is_token_valid(header_token, &expected_token) {
                return Ok(());
            }
        }
        
        // Check form token in request body
        if request.headers.get("content-type")
            .unwrap_or(&"".to_string())
            .contains("application/x-www-form-urlencoded")
        {
            let body_str = String::from_utf8_lossy(&request.body);
            let form_token = self.extract_token_from_form(&body_str);
            
            if self.is_token_valid(&form_token, &expected_token) {
                return Ok(());
            }
        }
        
        Err(ServerError::Csrf("Invalid CSRF token".to_string()))
    }
    
    /// Extract token from cookie string
    fn extract_token_from_cookie(&self, cookie_header: &str) -> String {
        for cookie in cookie_header.split(';') {
            let cookie = cookie.trim();
            if cookie.starts_with(&format!("{}=", self.token_cookie_name)) {
                return cookie.split('=')
                    .nth(1)
                    .unwrap_or("")
                    .trim()
                    .to_string();
            }
        }
        
        String::new()
    }
    
    /// Extract token from form data
    fn extract_token_from_form(&self, form_data: &str) -> String {
        for pair in form_data.split('&') {
            let mut parts = pair.split('=');
            if let Some(name) = parts.next() {
                if name == self.token_form_name {
                    return parts.next()
                        .unwrap_or("")
                        .to_string();
                }
            }
        }
        
        String::new()
    }
    
    /// Check if provided token matches expected token
    fn is_token_valid(&self, provided_token: &str, expected_token: &str) -> bool {
        !provided_token.is_empty() && !expected_token.is_empty() && 
        provided_token == expected_token
    }
    
    /// Generate a new CSRF token for a response
    pub fn add_csrf_token(&self, response: &mut Response) -> Result<()> {
        if !self.enabled {
            return Ok(());
        }
        
        // Generate secure random token
        let token = self.generate_csrf_token();
        
        // Add token as a cookie
        let cookie = format!(
            "{}={}; Path=/; HttpOnly; SameSite=Strict{}",
            self.token_cookie_name,
            token,
            if response.uses_https() { "; Secure" } else { "" }
        );
        
        response.headers.insert("set-cookie".to_string(), cookie);
        
        Ok(())
    }
    
    /// Generate a secure random token
    fn generate_csrf_token(&self) -> String {
        use rand::{thread_rng, Rng};
        use base64::{Engine as _, engine::general_purpose};
        
        // Generate 32 bytes of random data
        let mut bytes = [0u8; 32];
        thread_rng().fill(&mut bytes);
        
        // Encode as base64
        general_purpose::STANDARD.encode(bytes)
    }
}
```

### TLS and Transport Security

Implementing proper TLS configuration is critical for secure communication between clients, the proxy, and backend servers:

```rust
/// Create a secure TLS connector with modern settings
fn create_secure_tls_connector() -> Result<HttpsConnector<HttpConnector>> {
    // Create HTTP connector with timeout
    let mut http = HttpConnector::new();
    http.set_connect_timeout(Some(Duration::from_secs(10)));
    http.enforce_http(false); // Allow HTTPS URLs
    
    // TLS configuration
    let mut tls_config = rustls::ClientConfig::new();
    
    // Set modern TLS protocols
    tls_config.versions = vec![rustls::ProtocolVersion::TLSv1_3, rustls::ProtocolVersion::TLSv1_2];
    
    // Set cipher suites (example using Rustls)
    tls_config.ciphersuites = vec![
        // Modern, secure ciphers
        rustls::CipherSuite::TLS13_AES_256_GCM_SHA384,
        rustls::CipherSuite::TLS13_AES_128_GCM_SHA256,
        rustls::CipherSuite::TLS13_CHACHA20_POLY1305_SHA256,
        rustls::CipherSuite::TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
        rustls::CipherSuite::TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
        rustls::CipherSuite::TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
        rustls::CipherSuite::TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
        rustls::CipherSuite::TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
        rustls::CipherSuite::TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
    ];
    
    // Set root certificates
    let mut root_store = rustls::RootCertStore::empty();
    root_store.add_server_trust_anchors(
        webpki_roots::TLS_SERVER_ROOTS
            .0
            .iter()
            .map(|ta| {
                rustls::OwnedTrustAnchor::from_subject_spki_name_constraints(
                    ta.subject,
                    ta.spki,
                    ta.name_constraints,
                )
            })
    );
    tls_config.root_store = root_store;
    
    // Create the HTTPS connector
    let tls = tokio_rustls::TlsConnector::from(Arc::new(tls_config));
    let https = HttpsConnector::from((http, tls));
    
    Ok(https)
}
```

### IP Access Controls

Implement IP allow and deny lists to restrict access to your proxy based on client IP addresses:

```rust
/// IP access control mechanism
pub struct IpAccessControl {
    /// List of allowed IP addresses/ranges
    allow_list: Vec<IpNetwork>,
    /// List of denied IP addresses/ranges
    deny_list: Vec<IpNetwork>,
    /// Default policy when not matching any rule
    default_policy: AccessPolicy,
    /// Country-based access controls
    country_rules: HashMap<String, AccessPolicy>,
    /// Database for IP geolocation (optional)
    #[cfg(feature = "geo_ip")]
    geo_db: Option<maxminddb::Reader<Vec<u8>>>,
}

/// Access policy
enum AccessPolicy {
    Allow,
    Deny,
}

impl IpAccessControl {
    /// Create a new IP access control with default configuration
    pub fn new() -> Self {
        let mut instance = Self {
            allow_list: Vec::new(),
            deny_list: Vec::new(),
            default_policy: AccessPolicy::Allow,
            country_rules: HashMap::new(),
            #[cfg(feature = "geo_ip")]
            geo_db: None,
        };
        
        // Add localhost to allow list by default
        instance.add_to_allow_list("127.0.0.1/32").unwrap();
        instance.add_to_allow_list("::1/128").unwrap();
        
        // Common bad actor ranges could be preloaded from a threat intelligence source
        instance.add_to_deny_list("************/24").unwrap(); // Example block
        
        instance
    }
    
    /// Add an IP or CIDR range to the allow list
    pub fn add_to_allow_list(&mut self, ip_or_cidr: &str) -> Result<()> {
        match ip_or_cidr.parse::<IpNetwork>() {
            Ok(network) => {
                self.allow_list.push(network);
                Ok(())
            },
            Err(e) => Err(ServerError::Config(format!("Invalid IP or CIDR: {}", e))),
        }
    }
    
    /// Add an IP or CIDR range to the deny list
    pub fn add_to_deny_list(&mut self, ip_or_cidr: &str) -> Result<()> {
        match ip_or_cidr.parse::<IpNetwork>() {
            Ok(network) => {
                self.deny_list.push(network);
                Ok(())
            },
            Err(e) => Err(ServerError::Config(format!("Invalid IP or CIDR: {}", e))),
        }
    }
    
    /// Set country access policy
    #[cfg(feature = "geo_ip")]
    pub fn set_country_policy(&mut self, country_code: &str, policy: AccessPolicy) {
        self.country_rules.insert(country_code.to_uppercase(), policy);
    }
    
    /// Load GeoIP database
    #[cfg(feature = "geo_ip")]
    pub fn load_geo_database(&mut self, path: &str) -> Result<()> {
        match maxminddb::Reader::open_readfile(path) {
            Ok(reader) => {
                self.geo_db = Some(reader);
                Ok(())
            },
            Err(e) => Err(ServerError::Config(format!("Failed to load GeoIP database: {}", e))),
        }
    }
    
    /// Check if an IP is allowed to access the server
    pub fn is_allowed(&self, ip_str: &str) -> Result<bool> {
        // Parse the IP address
        let ip = match ip_str.parse::<IpAddr>() {
            Ok(addr) => addr,
            Err(_) => return Err(ServerError::Config("Invalid IP address".to_string())),
        };
        
        // Check deny list first (explicit deny has priority)
        for network in &self.deny_list {
            if network.contains(ip) {
                debug!("IP {} denied by deny list (matched {})", ip, network);
                return Ok(false);
            }
        }
        
        // Check allow list
        for network in &self.allow_list {
            if network.contains(ip) {
                debug!("IP {} allowed by allow list (matched {})", ip, network);
                return Ok(true);
            }
        }
        
        // Check country rules if GeoIP is enabled
        #[cfg(feature = "geo_ip")]
        if let Some(ref reader) = self.geo_db {
            if let Ok(country) = self.get_country_for_ip(reader, &ip) {
                if let Some(policy) = self.country_rules.get(&country) {
                    match policy {
                        AccessPolicy::Allow => {
                            debug!("IP {} allowed by country rule for {}", ip, country);
                            return Ok(true);
                        },
                        AccessPolicy::Deny => {
                            debug!("IP {} denied by country rule for {}", ip, country);
                            return Ok(false);
                        },
                    }
                }
            }
        }
        
        // Apply default policy
        match self.default_policy {
            AccessPolicy::Allow => Ok(true),
            AccessPolicy::Deny => Ok(false),
        }
    }
    
    /// Get country code for an IP address
    #[cfg(feature = "geo_ip")]
    fn get_country_for_ip(&self, reader: &maxminddb::Reader<Vec<u8>>, ip: &IpAddr) -> Result<String> {
        match reader.lookup::<maxminddb::geoip2::Country>(*ip) {
            Ok(country) => {
                if let Some(country) = country.country {
                    if let Some(iso_code) = country.iso_code {
                        return Ok(iso_code.to_string());
                    }
                }
                Err(ServerError::Config("Country not found for IP".to_string()))
            },
            Err(e) => Err(ServerError::Config(format!("GeoIP lookup error: {}", e))),
        }
    }
    
    /// Load rules from a configuration file
    pub fn load_from_file(&mut self, path: &str) -> Result<()> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| ServerError::Config(format!("Failed to read access control file: {}", e)))?;
        
        for line in content.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') {
                continue;
            }
            
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() < 2 {
                continue;
            }
            
            match parts[0].to_lowercase().as_str() {
                "allow" => self.add_to_allow_list(parts[1])?,
                "deny" => self.add_to_deny_list(parts[1])?,
                "default" => {
                    self.default_policy = match parts[1].to_lowercase().as_str() {
                        "allow" => AccessPolicy::Allow,
                        "deny" => AccessPolicy::Deny,
                        _ => return Err(ServerError::Config("Invalid default policy".to_string())),
                    };
                },
                #[cfg(feature = "geo_ip")]
                "country" => {
                    if parts.len() < 3 {
                        continue;
                    }
                    
                    let policy = match parts[2].to_lowercase().as_str() {
                        "allow" => AccessPolicy::Allow,
                        "deny" => AccessPolicy::Deny,
                        _ => continue,
                    };
                    
                    self.set_country_policy(parts[1], policy);
                },
                _ => continue,
            }
        }
        
        Ok(())
    }
}
```

### Security-Focused Access Logging

Implement comprehensive security logging to detect and respond to potential security incidents:

```rust
/// Security-focused access logger
pub struct SecurityLogger {
    /// Log file path
    log_path: Option<PathBuf>,
    /// Log level for security events
    log_level: LogLevel,
    /// Whether to log to stdout
    log_to_stdout: bool,
    /// Whether to log to syslog
    #[cfg(feature = "syslog")]
    log_to_syslog: bool,
    /// Additional log targets (e.g., SIEM systems)
    additional_targets: Vec<Box<dyn LogTarget>>,
}

/// Log levels for security events
enum LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

/// Log target trait
trait LogTarget: Send + Sync {
    fn log(&self, level: &LogLevel, message: &str, metadata: &LogMetadata) -> Result<()>;
}

/// Metadata for security log entries
struct LogMetadata {
    /// Timestamp
    timestamp: DateTime<Utc>,
    /// Client IP
    client_ip: String,
    /// Request method
    method: String,
    /// Request path
    path: String,
    /// Response status
    status: u16,
    /// User agent
    user_agent: String,
    /// Request ID
    request_id: String,
    /// User ID (if authenticated)
    user_id: Option<String>,
    /// Additional labels
    labels: HashMap<String, String>,
}

impl SecurityLogger {
    /// Create a new security logger
    pub fn new() -> Self {
        Self {
            log_path: None,
            log_level: LogLevel::Info,
            log_to_stdout: true,
            #[cfg(feature = "syslog")]
            log_to_syslog: false,
            additional_targets: Vec::new(),
        }
    }
    
    /// Set log file path
    pub fn with_log_file(mut self, path: &str) -> Self {
        self.log_path = Some(PathBuf::from(path));
        self
    }
    
    /// Set log level
    pub fn with_log_level(mut self, level: LogLevel) -> Self {
        self.log_level = level;
        self
    }
    
    /// Enable or disable stdout logging
    pub fn with_stdout(mut self, enabled: bool) -> Self {
        self.log_to_stdout = enabled;
        self
    }
    
    /// Enable or disable syslog logging
    #[cfg(feature = "syslog")]
    pub fn with_syslog(mut self, enabled: bool) -> Self {
        self.log_to_syslog = enabled;
        self
    }
    
    /// Add a custom log target
    pub fn add_target<T: LogTarget + 'static>(&mut self, target: T) {
        self.additional_targets.push(Box::new(target));
    }
    
    /// Log a security event
    pub fn log_security_event(&self, 
                             level: LogLevel, 
                             message: &str,
                             request: Option<&Request>,
                             response: Option<&Response>) -> Result<()> {
        // Build metadata
        let mut metadata = LogMetadata {
            timestamp: Utc::now(),
            client_ip: String::new(),
            method: String::new(),
            path: String::new(),
            status: 0,
            user_agent: String::new(),
            request_id: String::new(),
            user_id: None,
            labels: HashMap::new(),
        };
        
        // Fill in request metadata
        if let Some(req) = request {
            metadata.client_ip = req.client_addr.clone().unwrap_or_else(|| "unknown".to_string());
            metadata.method = req.method.to_string();
            metadata.path = req.path.clone();
            metadata.user_agent = req.headers.get("user-agent").unwrap_or(&"".to_string()).clone();
            
            if let Some(req_id) = req.headers.get("x-request-id") {
                metadata.request_id = req_id.clone();
            }
            
            // Extract authenticated user if available
            if let Some(user) = req.headers.get("x-authenticated-user") {
                metadata.user_id = Some(user.clone());
            }
        }
        
        // Fill in response metadata
        if let Some(resp) = response {
            metadata.status = resp.status.as_u16();
        }
        
        // Log to file if enabled
        if let Some(ref path) = self.log_path {
            self.log_to_file(path, &level, message, &metadata)?;
        }
        
        // Log to stdout if enabled
        if self.log_to_stdout {
            self.log_to_stdout_impl(&level, message, &metadata)?;
        }
        
        // Log to syslog if enabled
        #[cfg(feature = "syslog")]
        if self.log_to_syslog {
            self.log_to_syslog_impl(&level, message, &metadata)?;
        }
        
        // Log to additional targets
        for target in &self.additional_targets {
            target.log(&level, message, &metadata)?;
        }
        
        Ok(())
    }
    
    /// Log security events related to requests and responses
    pub fn log_request(&self, request: &Request, response: &Response, duration: Duration) -> Result<()> {
        // Basic access logging
        let basic_log = format!("{} {} {} {} ({:?})",
            request.method,
            request.path,
            response.status.as_u16(),
            request.client_addr.clone().unwrap_or_else(|| "unknown".to_string()),
            duration,
        );
        
        // Determine log level based on response status
        let level = match response.status.as_u16() {
            200..=399 => LogLevel::Info,
            400..=499 => LogLevel::Warning,
            _ => LogLevel::Error,
        };
        
        self.log_security_event(level, &basic_log, Some(request), Some(response))?;
        
        // Detect and log suspicious activities
        self.detect_suspicious_activities(request, response)?;
        
        Ok(())
    }
    
    /// Detect and log suspicious request patterns
    fn detect_suspicious_activities(&self, request: &Request, response: &Response) -> Result<()> {
        // Check for known attack patterns in URL
        let suspicious_patterns = [
            ("SQL injection", r"(?i)(\%27)|(\')|(\-\-)|(\%23)|(#)"),
            ("XSS attempt", r"(?i)<script[^>]*>"),
            ("Path traversal", r"\.\.[\\/]"),
            ("Command injection", r"(?i);\s*\w+\s*;"),
            ("Base64 data in URL", r"[a-zA-Z0-9+/]{20,}={0,2}"), // Large base64-encoded data
        ];
        
        for (attack_type, pattern) in &suspicious_patterns {
            let regex = match Regex::new(pattern) {
                Ok(r) => r,
                Err(_) => continue,
            };
            
            if regex.is_match(&request.path) {
                self.log_security_event(
                    LogLevel::Warning,
                    &format!("Potential {} attack detected in request URL", attack_type),
                    Some(request),
                    Some(response),
                )?;
            }
        }
        
        // Check for multiple 401/403 responses from same IP
        if response.status == StatusCode::UNAUTHORIZED || response.status == StatusCode::FORBIDDEN {
            if let Some(client_ip) = &request.client_addr {
                self.log_security_event(
                    LogLevel::Warning,
                    &format!("Authentication/authorization failure for client {}", client_ip),
                    Some(request),
                    Some(response),
                )?;
                
                // In a real system, increment counter for this IP and check thresholds
            }
        }
        
        // Log large request bodies (potential abuse)
        if request.body.len() > 1_000_000 { // 1MB
            self.log_security_event(
                LogLevel::Warning,
                &format!("Large request body detected: {} bytes", request.body.len()),
                Some(request),
                Some(response),
            )?;
        }
        
        Ok(())
    }
    
    /// Log to file
    fn log_to_file(&self, path: &PathBuf, level: &LogLevel, message: &str, metadata: &LogMetadata) -> Result<()> {
        // Format message in JSON for better machine processing
        let log_entry = serde_json::json!({
            "timestamp": metadata.timestamp.to_rfc3339(),
            "level": format!("{:?}", level),
            "message": message,
            "client_ip": metadata.client_ip,
            "method": metadata.method,
            "path": metadata.path,
            "status": metadata.status,
            "user_agent": metadata.user_agent,
            "request_id": metadata.request_id,
            "user_id": metadata.user_id,
            "labels": metadata.labels,
        });
        
        let log_line = serde_json::to_string(&log_entry)
            .map_err(|e| ServerError::Io(format!("Failed to serialize log entry: {}", e)))?;
        
        // Append to file
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(path)
            .map_err(|e| ServerError::Io(format!("Failed to open log file: {}", e)))?;
        
        writeln!(file, "{}", log_line)
            .map_err(|e| ServerError::Io(format!("Failed to write to log file: {}", e)))?;
        
        Ok(())
    }
    
    /// Log to stdout
    fn log_to_stdout_impl(&self, level: &LogLevel, message: &str, metadata: &LogMetadata) -> Result<()> {
        let level_str = match level {
            LogLevel::Debug => "[DEBUG]",
            LogLevel::Info => "[INFO]",
            LogLevel::Warning => "[WARNING]",
            LogLevel::Error => "[ERROR]",
            LogLevel::Critical => "[CRITICAL]",
        };
        
        println!("{} {} - {} {} {} ({})",
            metadata.timestamp.to_rfc3339(),
            level_str,
            metadata.client_ip,
            metadata.method,
            metadata.path,
            message,
        );
        
        Ok(())
    }
    
    /// Log to syslog
    #[cfg(feature = "syslog")]
    fn log_to_syslog_impl(&self, level: &LogLevel, message: &str, metadata: &LogMetadata) -> Result<()> {
        use syslog::{Facility, Formatter3164, BasicLogger};
        
        let formatter = Formatter3164 {
            facility: Facility::LOG_AUTH,
            hostname: None,
            process: "rusty-proxy".into(),
            pid: std::process::id(),
        };
        
        let syslog_level = match level {
            LogLevel::Debug => syslog::Level::Debug,
            LogLevel::Info => syslog::Level::Info,
            LogLevel::Warning => syslog::Level::Warning,
            LogLevel::Error => syslog::Level::Error,
            LogLevel::Critical => syslog::Level::Critical,
        };
        
        let log_message = format!("{} - {} {} {} ({})",
            syslog_level,
            metadata.client_ip,
            metadata.method,
            metadata.path,
            message,
        );
        
        let logger = BasicLogger::new(formatter)
            .map_err(|e| ServerError::Io(format!("Failed to create syslog logger: {}", e)))?;
        
        logger.log(syslog_level, log_message)
            .map_err(|e| ServerError::Io(format!("Failed to log to syslog: {}", e)))?;
        
        Ok(())
    }
}
```

### DDoS Protection and Rate Limiting

Implement a robust rate limiting system to protect your backends from excessive traffic:

```rust
/// Advanced rate limiter with token bucket algorithm
pub struct TokenBucketRateLimiter {
    /// Maximum requests per second per client
    max_rate: f64,
    /// Maximum burst size
    max_burst: usize,
    /// Current client buckets
    client_buckets: HashMap<String, TokenBucket>,
    /// Cleanup interval
    cleanup_interval: Duration,
    /// Last cleanup time
    last_cleanup: Instant,
}

struct TokenBucket {
    /// Available tokens
    tokens: f64,
    /// Last update time
    last_update: Instant,
}

impl TokenBucketRateLimiter {
    pub fn new(rate: f64, burst: usize, cleanup_interval: Duration) -> Self {
        Self {
            max_rate: rate,
            max_burst: burst,
            client_buckets: HashMap::new(),
            cleanup_interval: cleanup_interval,
            last_cleanup: Instant::now(),
        }
    }
    
    /// Check if a request is allowed
    pub fn allow_request(&mut self, client_ip: &str) -> bool {
        // Perform cleanup if needed
        self.cleanup_expired();
        
        let now = Instant::now();
        let bucket = self.client_buckets.entry(client_ip.to_string())
            .or_insert_with(|| TokenBucket {
                tokens: self.max_burst as f64,
                last_update: now,
            });
        
        // Refill tokens based on time elapsed
        let elapsed = now.duration_since(bucket.last_update).as_secs_f64();
        bucket.tokens = (bucket.tokens + elapsed * self.max_rate)
            .min(self.max_burst as f64);
        bucket.last_update = now;
        
        // Check if we have enough tokens
        if bucket.tokens >= 1.0 {
            bucket.tokens -= 1.0;
            true
        } else {
            false
        }
    }
    
    /// Clean up expired buckets
    fn cleanup_expired(&mut self) {
        let now = Instant::now();
        if now.duration_since(self.last_cleanup) > self.cleanup_interval {
            self.last_cleanup = now;
            
            // Remove buckets that would be completely refilled
            let max_age = self.max_burst as f64 / self.max_rate;
            self.client_buckets.retain(|_, bucket| {
                now.duration_since(bucket.last_update).as_secs_f64() < max_age * 2.0
            });
        }
    }
}
```

### WAF Integration

For advanced protection, integrate a Web Application Firewall (WAF) into your reverse proxy:

```rust
/// Simple Web Application Firewall (WAF) for request filtering
pub struct SimpleWaf {
    /// SQL injection patterns
    sql_injection_patterns: Vec<Regex>,
    /// XSS patterns
    xss_patterns: Vec<Regex>,
    /// Path traversal patterns
    path_traversal_patterns: Vec<Regex>,
    /// Command injection patterns
    command_injection_patterns: Vec<Regex>,
    /// CSRF protection patterns
    csrf_patterns: Vec<Regex>,
    /// Log-specific attack patterns
    log_injection_patterns: Vec<Regex>,
    /// Request rate tracker for brute force protection
    request_tracker: HashMap<String, RequestTracking>,
    /// Whitelist of allowed paths that bypass WAF (e.g., health checks)
    whitelist_paths: HashSet<String>,
}

struct RequestTracking {
    first_request: Instant,
    count: usize,
}

impl SimpleWaf {
    pub fn new() -> Self {
        let mut whitelist = HashSet::new();
        whitelist.insert("/health".to_string());
        whitelist.insert("/metrics".to_string());
        
        Self {
            sql_injection_patterns: vec![
                Regex::new(r"(?i)(\%27)|(\')|(\-\-)|(\%23)|(#)").unwrap(),
                Regex::new(r"(?i)((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))").unwrap(),
                Regex::new(r"(?i)(\%27)|(\')|(\")|(\%22)").unwrap(),
                Regex::new(r"(?i)union\s+select").unwrap(),
                Regex::new(r"(?i)exec\s*\(").unwrap(),
                // Advanced SQLi patterns
                Regex::new(r"(?i)((select|create|rename|truncate|load|alter|delete|update|insert|desc)\s.*)").unwrap(),
                Regex::new(r"(?i)(\/\*|\*\/|;|--|\\\\|::|~|`|<>|%|@|\*|&|from)").unwrap(),
            ],
            xss_patterns: vec![
                Regex::new(r"(?i)<script[^>]*>").unwrap(),
                Regex::new(r"(?i)javascript\s*:").unwrap(),
                Regex::new(r"(?i)onload\s*=").unwrap(),
                Regex::new(r"(?i)onerror\s*=").unwrap(),
                // Additional XSS vectors
                Regex::new(r"(?i)(\b)(on\S+)(\s*=)").unwrap(), // Any on* event
                Regex::new(r"(?i)<(iframe|embed|object|img|layer)").unwrap(),
                Regex::new(r"(?i)(document|window|eval|setTimeout|setInterval|Function|execScript)\s*(\.|).*(cookie|location)").unwrap(),
            ],
            path_traversal_patterns: vec![
                Regex::new(r"\.\.[\\/]").unwrap(),
                Regex::new(r"(?i)%2e%2e[\\/]").unwrap(),
                Regex::new(r"(?i)%252e%252e[\\/]").unwrap(),
                Regex::new(r"(?i)\.\.[\\\/]|\%2e\%2e[\\\/]|\%252e\%252e[\\\/]").unwrap(),
                Regex::new(r"(?i)(\/|\\)(etc|windows|win|system|system32|boot|root)(\/|\\)").unwrap(),
            ],
            command_injection_patterns: vec![
                Regex::new(r"(?i);\s*\w+\s*;").unwrap(),
                Regex::new(r"\|\s*\w+").unwrap(),
                Regex::new(r"(?i)\$\(\w+").unwrap(),
                Regex::new(r"(?i)`\s*\w+").unwrap(),
                // Additional command injection patterns
                Regex::new(r"(?i);.*?(ping|chmod|wget|curl|bash|sh|ls|cat|id)").unwrap(),
                Regex::new(r"(?i)[\|\&\;\$><`]").unwrap(),
            ],
            csrf_patterns: vec![
                Regex::new(r"(?i)<form[^>]*>").unwrap(),
                Regex::new(r"(?i)document\.cookie").unwrap(),
                Regex::new(r"(?i)XMLHttpRequest\.open").unwrap(),
                Regex::new(r"(?i)fetch\s*\(").unwrap(),
            ],
            log_injection_patterns: vec![
                Regex::new(r"(?i)(%0A|%0D|\\r|\\n)").unwrap(), // CRLF injection
                Regex::new(r"(?i)<!\[CDATA\[").unwrap(), // XML log injection
            ],
            request_tracker: HashMap::new(),
            whitelist_paths: whitelist,
        }
    }
    
    /// Check if a request should be blocked
    pub fn should_block(&mut self, request: &Request) -> Option<String> {
        // Skip WAF for whitelisted paths
        if self.whitelist_paths.contains(&request.path) {
            return None;
        }
        
        // Track request frequency (for brute force protection)
        if let Some(client_ip) = &request.client_addr {
            self.track_request(client_ip);
            
            // Check for brute force attempts
            if self.is_brute_force_attempt(client_ip) {
                return Some("Potential brute force attack detected".to_string());
            }
        }
        
        // Check URL for malicious patterns
        for pattern in &self.sql_injection_patterns {
            if pattern.is_match(&request.path) {
                self.log_attack("SQL_INJECTION", &request.path, &request.client_addr);
                return Some("SQL injection attempt detected".to_string());
            }
        }
        
        for pattern in &self.xss_patterns {
            if pattern.is_match(&request.path) {
                self.log_attack("XSS", &request.path, &request.client_addr);
                return Some("XSS attempt detected".to_string());
            }
        }
        
        for pattern in &self.path_traversal_patterns {
            if pattern.is_match(&request.path) {
                self.log_attack("PATH_TRAVERSAL", &request.path, &request.client_addr);
                return Some("Path traversal attempt detected".to_string());
            }
        }
        
        for pattern in &self.command_injection_patterns {
            if pattern.is_match(&request.path) {
                self.log_attack("COMMAND_INJECTION", &request.path, &request.client_addr);
                return Some("Command injection attempt detected".to_string());
            }
        }
        
        // Check for CSRF attacks in POST/PUT requests
        if request.method == Method::POST || request.method == Method::PUT {
            let referer = request.headers.get("referer").unwrap_or(&"".to_string());
            let origin = request.headers.get("origin").unwrap_or(&"".to_string());
            let host = request.headers.get("host").unwrap_or(&"".to_string());
            
            // Verify referer/origin headers match host (basic CSRF protection)
            if !referer.is_empty() && !referer.contains(host) && 
               !origin.is_empty() && !origin.contains(host) {
                self.log_attack("CSRF", &request.path, &request.client_addr);
                return Some("Potential CSRF attack detected".to_string());
            }
            
            // Look for form submission or cross-domain requests in body
            if !request.body.is_empty() {
                let body_str = String::from_utf8_lossy(&request.body);
                for pattern in &self.csrf_patterns {
                    if pattern.is_match(&body_str) {
                        self.log_attack("CSRF", &request.path, &request.client_addr);
                        return Some("Potential CSRF attack detected".to_string());
                    }
                }
            }
        }
        
        // Check headers for malicious patterns
        for (name, value) in &request.headers {
            // Check for XSS in headers
            for pattern in &self.xss_patterns {
                if pattern.is_match(value) {
                    self.log_attack("XSS_HEADER", name, &request.client_addr);
                    return Some("XSS attempt in headers detected".to_string());
                }
            }
            
            // Check for log injection in headers
            for pattern in &self.log_injection_patterns {
                if pattern.is_match(value) {
                    self.log_attack("LOG_INJECTION", name, &request.client_addr);
                    return Some("Log injection attempt detected".to_string());
                }
            }
        }
        
        // Check request body for malicious patterns if present
        if !request.body.is_empty() {
            let body_str = String::from_utf8_lossy(&request.body);
            
            for pattern in &self.sql_injection_patterns {
                if pattern.is_match(&body_str) {
                    self.log_attack("SQL_INJECTION_BODY", &request.path, &request.client_addr);
                    return Some("SQL injection attempt in body detected".to_string());
                }
            }
            
            for pattern in &self.xss_patterns {
                if pattern.is_match(&body_str) {
                    self.log_attack("XSS_BODY", &request.path, &request.client_addr);
                    return Some("XSS attempt in body detected".to_string());
                }
            }
            
            for pattern in &self.command_injection_patterns {
                if pattern.is_match(&body_str) {
                    self.log_attack("COMMAND_INJECTION_BODY", &request.path, &request.client_addr);
                    return Some("Command injection attempt in body detected".to_string());
                }
            }
        }
        
        None
    }
    
    /// Track request frequency for brute force detection
    fn track_request(&mut self, client_ip: &str) {
        let now = Instant::now();
        let entry = self.request_tracker.entry(client_ip.to_string())
            .or_insert_with(|| RequestTracking {
                first_request: now,
                count: 0,
            });
            
        entry.count += 1;
        
        // Reset if window expired (1 minute)
        if now.duration_since(entry.first_request).as_secs() > 60 {
            entry.first_request = now;
            entry.count = 1;
        }
    }
    
    /// Check if request frequency indicates brute force
    fn is_brute_force_attempt(&self, client_ip: &str) -> bool {
        if let Some(tracking) = self.request_tracker.get(client_ip) {
            // If more than 100 requests per minute, consider it a brute force
            let elapsed = Instant::now().duration_since(tracking.first_request).as_secs();
            if elapsed <= 60 && tracking.count > 100 {
                return true;
            }
        }
        false
    }
    
    /// Log attack information
    fn log_attack(&self, attack_type: &str, target: &str, client_ip: &Option<String>) {
        error!("WAF blocked {} attack on '{}' from IP: {}", 
               attack_type, target, client_ip.clone().unwrap_or("unknown".to_string()));
        
        // In a production system, you might want to:
        // 1. Log to a security information and event management (SIEM) system
        // 2. Trigger alerts for severe attacks
        // 3. Update dynamic block lists
    }
    
    /// Add path to WAF whitelist
    pub fn whitelist(&mut self, path: &str) {
        self.whitelist_paths.insert(path.to_string());
    }
}
```
```

## Best Practices

### Connection Management

Properly managing connections is crucial for a high-performance reverse proxy:

1. **Reuse Connections**: Use connection pooling to reduce the overhead of establishing new connections.
   ```rust
   let client = Client::builder()
       .pool_max_idle_per_host(32)
       .pool_idle_timeout(Duration::from_secs(60))
       .build(https);
   ```

2. **Timeout Handling**: Implement proper timeouts at different levels:
   ```rust
   // Connection timeout
   let https = HttpsConnector::new_with_connector(connector_with_timeout);
   
   // Request timeout
   let timeout_fut = tokio::time::timeout(
       Duration::from_secs(self.config.read_timeout),
       self.client.request(hyper_req)
   );
   let response = match timeout_fut.await {
       Ok(result) => result?,
       Err(_) => return Err(ServerError::Proxy("Request timed out".to_string())),
   };
   ```

### Error Handling and Retries

Implement retry logic for transient errors:

```rust
const MAX_RETRIES: usize = 3;

async fn send_request_with_retry(&self, req: hyper::Request<Body>) -> Result<hyper::Response<Body>> {
    let mut attempts = 0;
    loop {
        attempts += 1;
        match self.client.request(req.try_clone()?).await {
            Ok(response) => return Ok(response),
            Err(err) => {
                // Only retry for certain error types
                if attempts >= MAX_RETRIES || !is_retryable_error(&err) {
                    return Err(ServerError::Proxy(format!("Request failed: {}", err)));
                }
                
                // Exponential backoff before retry
                tokio::time::sleep(Duration::from_millis(100 * 2u64.pow(attempts as u32))).await;
            }
        }
    }
}

fn is_retryable_error(err: &hyper::Error) -> bool {
    // Return true for network errors, timeouts, etc.
    err.is_connect() || err.is_timeout()
}
```

### Logging and Monitoring

Implement comprehensive logging for your proxy:

```rust
pub async fn handle_request(&self, request: &Request) -> Result<Response> {
    let start_time = std::time::Instant::now();
    let client_ip = request.client_addr.clone().unwrap_or_else(|| "unknown".to_string());
    let path = request.path.clone();
    
    // Log incoming request
    debug!(
        "Proxy request: {} {} {} (client: {})",
        request.method, path, request.headers.get("host").unwrap_or(&"".to_string()),
        client_ip
    );
    
    // Process request
    let result = self.process_request(request).await;
    
    // Log completion with timing
    let duration = start_time.elapsed();
    match &result {
        Ok(response) => {
            info!(
                "Proxy response: {} {} {} - {} in {:?}",
                request.method, path, request.headers.get("host").unwrap_or(&"".to_string()),
                response.status.as_u16(), duration
            );
        }
        Err(err) => {
            error!(
                "Proxy error: {} {} {} - {} in {:?}",
                request.method, path, request.headers.get("host").unwrap_or(&"".to_string()),
                err, duration
            );
        }
    }
    
    result
}
```

### Backend Health Checking

Implement a health check system for your backends:

```rust
struct BackendHealth {
    backends: HashMap<String, BackendStatus>,
    check_interval: Duration,
}

struct BackendStatus {
    url: String,
    healthy: bool,
    last_check: Instant,
    failure_count: u32,
}

impl BackendHealth {
    pub async fn check_backend_health(&mut self, url: &str) -> bool {
        let status = self.backends.entry(url.to_string())
            .or_insert_with(|| BackendStatus {
                url: url.to_string(),
                healthy: true,
                last_check: Instant::now() - Duration::from_secs(3600),
                failure_count: 0,
            });
            
        // Check if we need to perform a health check
        let now = Instant::now();
        if now - status.last_check < self.check_interval {
            return status.healthy;
        }
        
        // Perform health check
        status.last_check = now;
        match self.perform_health_check(url).await {
            Ok(_) => {
                status.healthy = true;
                status.failure_count = 0;
                true
            }
            Err(_) => {
                status.failure_count += 1;
                // Mark as unhealthy after 3 consecutive failures
                status.healthy = status.failure_count < 3;
                status.healthy
            }
        }
    }
    
    async fn perform_health_check(&self, url: &str) -> Result<()> {
        // Send a HEAD request to the health check endpoint
        let client = Client::new();
        let health_url = format!("{}/health", url);
        let req = hyper::Request::builder()
            .method("HEAD")
            .uri(health_url)
            .header("User-Agent", "RustyServer-HealthCheck")
            .body(Body::empty())
            .map_err(|e| ServerError::Proxy(format!("Failed to build health check request: {}", e)))?;
            
        let response = client.request(req)
            .await
            .map_err(|e| ServerError::Proxy(format!("Health check failed: {}", e)))?;
            
        // Check if the response is successful
        if response.status().is_success() {
            Ok(())
        } else {
            Err(ServerError::Proxy(format!("Health check returned status: {}", response.status())))
        }
    }
}
```

## Tradeoffs

### Web Server Implementation Choices

- **Blocking vs Async**: We use async/await for the proxy to handle concurrent connections efficiently, which increases complexity but significantly improves performance with high concurrency.
- **Header Handling**: Carefully managing which headers are forwarded maintains proper proxy behavior but requires detailed understanding of HTTP semantics.
- **Timeout Management**: Proper timeouts prevent the proxy from hanging due to slow backends, with the tradeoff of potentially dropping valid but slow requests.
- **Error Handling Strategies**: Deciding whether to fail fast, retry, or gracefully degrade affects both user experience and backend load.

### Rust Implementation Decisions

- **Hyper vs reqwest**: We use Hyper directly for better control and performance, but reqwest would provide a simpler API with fewer manual configurations.
- **Tokio Runtime**: Managing the async runtime adds complexity but improves performance by allowing efficient concurrent handling of many connections.
- **Error Propagation**: Custom error types for proxy errors provide better context but increase code complexity.
- **Memory vs Performance**: Buffering responses completely uses more memory but allows header modifications, while streaming responses is more memory-efficient but limits header modifications.

## Knowledge Check

Test your understanding of reverse proxies with these questions:

1. **What is the primary difference between a forward proxy and a reverse proxy?**
   <details>
   <summary>Show Answer</summary>
   
   A forward proxy sits in front of clients and represents them to servers, often used for anonymity or access control. A reverse proxy sits in front of servers and represents them to clients, typically used for load balancing, security, caching, and centralized request handling. The key distinction is that clients are explicitly configured to use a forward proxy, whereas clients connect to a reverse proxy believing it's the actual destination server.
   </details>

2. **Why is it important to modify the `Host` header when forwarding a request to a backend?**
   <details>
   <summary>Show Answer</summary>
   
   The `Host` header needs to be modified to match the expected hostname of the backend server. Otherwise, the backend might reject the request or serve incorrect content because it uses the `Host` header to determine which virtual host or site configuration to use. For example, if your proxy forwards requests to a backend server running multiple virtual hosts, setting the correct `Host` header ensures the backend processes the request using the appropriate virtual host configuration.
   </details>

3. **What are the X-Forwarded-* headers used for in a reverse proxy?**
   <details>
   <summary>Show Answer</summary>
   
   X-Forwarded headers preserve information about the original client request that would otherwise be lost during proxying:
   - `X-Forwarded-For`: The original client IP address (and potentially intermediary proxies)
   - `X-Forwarded-Proto`: The original protocol (http/https)
   - `X-Forwarded-Host`: The original host requested by the client
   - `X-Forwarded-Port`: The original port requested by the client
   
   These headers allow backend servers to make decisions based on the original client information rather than the proxy's information, which is critical for features like IP-based rate limiting, SSL redirection, and generating correct absolute URLs in responses.
   </details>

4. **Why is connection pooling important for a reverse proxy?**
   <details>
   <summary>Show Answer</summary>
   
   Connection pooling allows the proxy to reuse existing TCP connections to backend servers instead of creating new ones for each request. This significantly reduces latency by eliminating the overhead of TCP handshakes and TLS negotiations, and can dramatically improve throughput under high load. 
   
   For example, establishing a new TLS connection can require multiple round trips and CPU-intensive cryptographic operations. With connection pooling, this cost is amortized across multiple requests, potentially reducing latency by 50-300ms per request and increasing throughput by 30-50% in high-volume scenarios.
   </details>

5. **How can a reverse proxy help mitigate DDoS attacks?**
   <details>
   <summary>Show Answer</summary>
   
   A reverse proxy can help mitigate DDoS attacks by:
   - Rate limiting requests from single IP addresses
   - Providing a buffer between attackers and backend servers
   - Filtering out malicious requests based on patterns
   - Implementing connection timeouts to prevent resource exhaustion
   - Distributing load across multiple backend servers
   - Caching static content to reduce backend load
   - Implementing circuit breakers to prevent cascading failures
   - Rejecting malformed requests before they reach application servers
   
   These features help protect backend servers from being overwhelmed by malicious traffic and ensure legitimate users can still access services during an attack.
   </details>

6. **What's the purpose of health checking in a reverse proxy implementation?**
   <details>
   <summary>Show Answer</summary>
   
   Health checking allows the proxy to periodically verify that backend servers are operational. This enables the proxy to:
   - Stop sending requests to failed backends
   - Automatically remove unhealthy backends from the load balancing pool
   - Restore backends when they recover
   - Provide higher availability by avoiding downtime due to backend failures
   - Implement gradual recovery mechanisms (e.g., slowly ramping up traffic to recovered servers)
   - Gather metrics about backend reliability and performance
   
   Without health checks, the proxy might continue sending requests to failed backends, resulting in errors and poor user experience. Modern health checking systems typically use adaptive timing and sophisticated failure detection algorithms to minimize false positives/negatives.
   </details>

7. **Why is proper header management important in a reverse proxy?**
   <details>
   <summary>Show Answer</summary>
   
   Proper header management in a reverse proxy is crucial because:
   - HTTP headers control caching, authentication, content negotiation and other key behaviors
   - Hop-by-hop headers (like `Connection` and `Transfer-Encoding`) should not be forwarded
   - Security-related headers may need filtering to prevent information leakage
   - Backend identification headers should be removed to hide server implementation details
   - Proxy-specific headers must be added (like `X-Forwarded-For`)
   - Content-related headers (like `Content-Length`) may need recalculation
   
   Incorrect header handling can lead to protocol violations, security vulnerabilities, cache inconsistencies, and broken functionality in web applications.
   </details>

8. **What are three common load balancing strategies used in reverse proxies and when would you use each?**
   <details>
   <summary>Show Answer</summary>
   
   Three common load balancing strategies:
   
   1. **Round-robin**: Distributes requests sequentially across all backends in rotation.
      - *When to use*: Good for situations where backends have similar capabilities and requests have similar complexity/resource requirements.
      - *Example*: Stateless web servers handling similar traffic types.
   
   2. **Least connections**: Sends new requests to the backend with the fewest active connections.
      - *When to use*: When requests vary in processing time or resource utilization, or when backends vary in capacity.
      - *Example*: API servers where some endpoints are more resource-intensive than others.
   
   3. **IP hash/consistent hashing**: Uses a hash of the client's IP address to determine which backend to use.
      - *When to use*: When session stickiness is needed but you don't want to use cookies (or can't).
      - *Example*: Applications requiring session affinity where the session data is stored locally on each backend.
   </details>

9. **What is the difference between path-based and host-based routing in a reverse proxy?**
   <details>
   <summary>Show Answer</summary>
   
   **Path-based routing** directs requests to different backends based on the URL path pattern:
   - Examines the path portion of the URL (e.g., `/api/v1/users`, `/static/images`)
   - All requests share the same domain name
   - Used to route different application components or microservices
   - Example: `/api/*` requests go to API servers, `/static/*` to content servers
   
   **Host-based routing** directs requests based on the domain or subdomain in the Host header:
   - Examines the hostname portion of the request (e.g., `api.example.com`, `www.example.com`)
   - Different domains can be served by different backends
   - Used for virtual hosting or multi-tenant environments
   - Example: `api.example.com` to API servers, `web.example.com` to web app servers
   
   In our implementation, we combined both approaches by first selecting a virtual host based on the Host header, then selecting backends based on path patterns within that virtual host configuration.
   </details>

10. **Why is response streaming important for a reverse proxy, and how does it differ from buffering?**
    <details>
    <summary>Show Answer</summary>
    
    **Response streaming** transmits data to clients as it's received from backends, whereas **buffering** waits for the complete response before forwarding.
    
    **Streaming benefits**:
    - Lower memory usage (doesn't need to hold entire response)
    - Improved time-to-first-byte (TTFB) for clients
    - Better handling of large responses
    - Support for real-time data and long-polling
    - Enables server-sent events and other streaming responses
    
    **Buffering benefits**:
    - Ability to modify headers based on complete response content
    - Can compress or transform the entire response
    - Easier error handling (can return a different response if backend fails mid-stream)
    - Ability to validate complete response before forwarding
    
    In high-performance proxy implementations, streaming is preferred for large responses or when TTFB is critical, while buffering may be used for smaller responses that require transformation or when complete validation is necessary.
    </details>

11. **What is the purpose of timeouts in a reverse proxy implementation, and what types of timeouts are important?**
    <details>
    <summary>Show Answer</summary>
    
    Timeouts prevent resource exhaustion and ensure system responsiveness by limiting how long operations can take. Important timeout types in reverse proxy implementations:
    
    1. **Connect timeout**: Maximum time allowed to establish a connection to a backend server.
       - Prevents hanging on unresponsive backends
       - Typically short (1-5 seconds)
    
    2. **Read timeout**: Maximum time allowed to wait between read operations from a backend.
       - Prevents slow backends from consuming proxy resources
       - Typically medium (10-30 seconds)
    
    3. **Write timeout**: Maximum time allowed for sending data to a backend.
       - Prevents slow clients from blocking proxy resources
       - Typically medium (10-30 seconds)
    
    4. **Idle timeout**: Maximum time a connection can remain idle in the connection pool.
       - Controls connection pool resource usage
       - Typically longer (30-300 seconds)
    
    5. **Total request timeout**: Maximum overall time for a complete request/response cycle.
       - Ensures eventual completion of all requests
       - Typically set based on application needs (30-120 seconds)
    
    Properly configured timeouts are essential for preventing cascading failures, where one slow backend can exhaust all available connections and affect the entire system.
    </details>

12. **Why might you implement retry logic in a reverse proxy, and what risks does it introduce?**
    <details>
    <summary>Show Answer</summary>
    
    **Benefits of retry logic**:
    - Improved resilience to transient failures (network glitches, temporary overloads)
    - Automatic recovery from backend server restarts
    - Better experience for end users (failed requests can succeed without user intervention)
    - Higher overall system availability
    - Can work around load balancing issues by trying alternate backends
    
    **Risks**:
    - **Amplification of load**: Retries can increase backend load during already stressful periods
    - **Duplicated operations**: Non-idempotent requests (like POST) could be executed multiple times
    - **Delayed error responses**: Users wait longer before receiving error messages
    - **Resource consumption**: Retries consume additional proxy resources 
    - **Masking systemic issues**: Automatic retries can hide underlying problems that need addressing
    
    To mitigate these risks, implement:
    - Retry budgets (limit total retry percentage)
    - Exponential backoff and jitter
    - Idempotency requirements for non-GET methods
    - Circuit breakers to prevent retrying against consistently failing backends
    - Clear retry logging for monitoring and diagnostics
    </details>

## Additional Resources

### Rust Implementation Resources

- [Hyper HTTP client documentation](https://docs.rs/hyper/0.14.27/hyper/client/index.html) - Official documentation for the Hyper HTTP client
- [Tokio documentation for async Rust](https://tokio.rs/tokio/tutorial) - Comprehensive guide to async programming with Tokio
- [Rustls TLS implementation](https://docs.rs/rustls/latest/rustls/) - Pure-Rust TLS implementation
- [Tower - modular service components](https://docs.rs/tower/latest/tower/) - Middleware library for building robust network services
- [tracing crate](https://docs.rs/tracing/latest/tracing/) - Structured logging and diagnostics framework
- [reqwest high-level client](https://docs.rs/reqwest/latest/reqwest/) - User-friendly HTTP client built on Hyper

### Proxy Design Resources

- [Designing a Modern HTTP Proxy](https://medium.com/@nate510/designing-a-modern-http-proxy-in-rust-bce123aed2c) - Design patterns for HTTP proxies in Rust
- [HTTP: The Definitive Guide](https://www.oreilly.com/library/view/http-the-definitive/1565925092/) - David Gourley & Brian Totty
- [Building Microservices, 2nd Edition](https://www.oreilly.com/library/view/building-microservices-2nd/9781492034018/) - Sam Newman
- [System Design Interview: Load Balancer and Reverse Proxy](https://bytebytego.com/courses/system-design-interview/load-balancer) - Alex Xu
- [Proxy Architecture Patterns](https://www.nginx.com/blog/microservices-reference-architecture-nginx-proxy-model/) - NGINX microservices reference architecture

### Advanced Proxy Articles

- [Reverse Proxy vs Load Balancer: Understanding the Difference](https://www.nginx.com/resources/glossary/reverse-proxy-vs-load-balancer/)
- [Understanding HTTP Proxy Behavior](https://developer.mozilla.org/en-US/docs/Web/HTTP/Proxy_servers_and_tunneling)
- [HTTP Headers for Optimal Proxying](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers#proxies)
- [Circuit Breaking in Distributed Systems](https://martinfowler.com/bliki/CircuitBreaker.html) - Martin Fowler
- [Backpressure and Reactive Streams](https://www.baeldung.com/rxjava-backpressure) - Principles applicable to high-throughput proxies
- [Edge Computing with Reverse Proxies](https://www.cloudflare.com/learning/serverless/glossary/what-is-edge-computing/) - Cloudflare

### Performance Optimization

- [High Performance Browser Networking](https://hpbn.co/) - Ilya Grigorik
- [The C10K Problem](https://daniel.haxx.se/docs/c10k.html) - Daniel Stenberg
- [Optimizing Web Servers for High Throughput and Low Latency](https://dropbox.tech/infrastructure/optimizing-web-servers-for-high-throughput-and-low-latency) - Dropbox Engineering
- [TCP/IP Illustrated, Volume 1: The Protocols](https://www.pearson.com/us/higher-education/program/Fall-TCP-IP-Illustrated-Volume-1-The-Protocols-2nd-Edition/PGM316779.html) - Kevin Fall

### Production-Grade Proxies and Load Balancers

- [HAProxy](https://www.haproxy.org/) - TCP/HTTP load balancer with advanced features
- [NGINX](https://nginx.org/) - High-performance HTTP server, reverse proxy, and load balancer
- [Envoy Proxy](https://www.envoyproxy.io/) - Cloud-native high-performance edge/service proxy
- [Traefik](https://traefik.io/) - Modern HTTP reverse proxy and load balancer for microservices
- [Caddy](https://caddyserver.com/) - HTTPS-by-default web server with reverse proxy capabilities
- [Linkerd](https://linkerd.io/) - Ultra-light, ultra-simple service mesh for Kubernetes

### Testing and Benchmarking Tools

- [wrk](https://github.com/wg/wrk) - Modern HTTP benchmarking tool
- [hey](https://github.com/rakyll/hey) - HTTP load generator for benchmarking
- [vegeta](https://github.com/tsenart/vegeta) - HTTP load testing tool and library
- [bombardier](https://github.com/codesenberg/bombardier) - Fast cross-platform HTTP benchmarking tool
- [Gatling](https://gatling.io/) - Load testing tool for web applications
- [Locust](https://locust.io/) - Python-based open source load testing tool

### Security Resources

- [OWASP API Security Project](https://owasp.org/www-project-api-security/)
- [Security Headers](https://securityheaders.com/) - Check your website for security headers
- [Let's Encrypt](https://letsencrypt.org/) - Free TLS certificates for HTTPS
- [ModSecurity](https://modsecurity.org/) - Open source web application firewall
- [Web Security Academy](https://portswigger.net/web-security) - Free web security training
- [OWASP Top 10 for API Security](https://owasp.org/API-Security/editions/2023/en/0x00-header/)

### Learning Projects

- [Build a simple HTTP server from scratch](https://github.com/not-matthias/libhc) - Educational HTTP client in Rust
- [Rusty Reverse Proxy](https://github.com/abhijeetbhagat/rusty-reverse-proxy) - Open source reverse proxy implementation in Rust
- [Simple Reverse Proxy in Go](https://github.com/kasvith/simplelb) - Simple load balancer in Go to learn the concepts

### Video Tutorials

- [HTTP Reverse Proxy from Scratch](https://www.youtube.com/watch?v=SuZb_H42zPs) - Building a reverse proxy step by step
- [Rust HTTP Client and Server](https://www.youtube.com/watch?v=IcNd_6KIbOw) - Tutorial on HTTP servers in Rust
- [System Design: Designing a Load Balancer](https://www.youtube.com/watch?v=6FKD3b7BZW4) - Understanding load balancing concepts
- [Proxy vs Reverse Proxy](https://www.youtube.com/watch?v=4NB0NDtOwIQ) - Visual explanation of proxy concepts

## Next Steps

In the next module, we'll implement caching mechanisms to improve performance and reduce load on backend servers. Caching is a natural companion to reverse proxying and can dramatically improve performance for frequently accessed resources.

## Navigation
- [Previous: Virtual Hosts](09-virtual-hosts.md)
- [Next: Caching Mechanisms](11-caching-mechanisms.md)
