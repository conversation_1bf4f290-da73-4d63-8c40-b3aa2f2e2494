<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\03-http-protocol.md -->
# HTTP Protocol Implementation

## Learning Objectives
- Understand the structure and components of the HTTP protocol
- Implement HTTP request parsing from raw TCP streams
- Design and build an HTTP response generation system
- Create a type-safe representation of HTTP methods, versions, and status codes
- Apply the Builder pattern for constructing flexible HTTP responses
- Integrate HTTP handling into our TCP server

## Prerequisites
- Completion of Module 02: Basic TCP Server
- Understanding of TCP socket programming in Rust
- Familiarity with Rust enums, structs, and traits
- Basic knowledge of HTTP concepts (methods, status codes, headers)
- Experience with error handling patterns in Rust

## Navigation
- [Previous: Basic TCP Server](02-basic-tcp-server.md)
- [Next: Static File Serving](04-static-file-serving.md)

## Introduction

In this module, we'll implement the HTTP protocol in our web server, focusing on proper request parsing and response generation according to the HTTP/1.1 specification. HTTP (Hypertext Transfer Protocol) is the foundation of data communication on the web, providing a standardized way for clients and servers to exchange information.

The HTTP protocol operates as a request-response protocol in the client-server computing model. A client submits an HTTP request to a server, which processes it and returns an HTTP response. This module will transform our basic TCP server into a proper HTTP server capable of understanding and responding to web requests.

### HTTP Protocol in the Web Stack

```
┌────────────────────┐
│  Web Application   │ Business logic, content generation
├────────────────────┤
│  HTTP Protocol     │ Request/response format, headers, status codes
├────────────────────┤
│  TCP               │ Reliable connection, data delivery
├────────────────────┤
│  IP                │ Routing between networks
└────────────────────┘
```

### HTTP Request/Response Flow

```mermaid
sequenceDiagram
    participant Client
    participant Server
    
    Client->>Server: TCP Connection Establishment
    Note over Client,Server: Three-way handshake
    
    Client->>Server: HTTP Request
    Note right of Client: GET /index.html HTTP/1.1<br>Host: example.com<br>User-Agent: Mozilla/5.0<br>...
    
    Server->>Client: HTTP Response
    Note left of Server: HTTP/1.1 200 OK<br>Content-Type: text/html<br>Content-Length: 1234<br><br><!DOCTYPE html>...
    
    Note over Client,Server: Connection kept alive or closed
```

This diagram shows the full HTTP exchange on top of a TCP connection, including the request and response structure.

## HTTP Protocol Overview

HTTP (Hypertext Transfer Protocol) is the foundation of data communication on the web. Before implementing it, let's understand its structure and key components in depth.

### Core HTTP Concepts

#### 1. Request Structure

Every HTTP request from a client contains:

```
┌───────────────────────────────────────────────┐
│ REQUEST LINE (Method URI HTTP-Version)        │
│ e.g., "GET /index.html HTTP/1.1"              │
├───────────────────────────────────────────────┤
│ HEADERS (Key: Value pairs)                    │
│ e.g., "Host: example.com"                     │
│       "User-Agent: Mozilla/5.0"               │
│       "Accept: text/html"                     │
├───────────────────────────────────────────────┤
│ EMPTY LINE (CRLF)                             │
├───────────────────────────────────────────────┤
│ BODY (Optional)                               │
│ Used primarily for POST/PUT methods           │
│ Contains data being sent to the server        │
└───────────────────────────────────────────────┘
```

#### 2. Response Structure

Every HTTP response from a server contains:

```
┌───────────────────────────────────────────────┐
│ STATUS LINE (HTTP-Version Status-Code Reason) │
│ e.g., "HTTP/1.1 200 OK"                       │
├───────────────────────────────────────────────┤
│ HEADERS (Key: Value pairs)                    │
│ e.g., "Content-Type: text/html"               │
│       "Content-Length: 1024"                  │
│       "Server: RustyServer/0.1.0"             │
├───────────────────────────────────────────────┤
│ EMPTY LINE (CRLF)                             │
├───────────────────────────────────────────────┤
│ BODY (Optional)                               │
│ Contains the resource or error information    │
└───────────────────────────────────────────────┘
```

#### 3. HTTP Methods

HTTP methods indicate the desired action to be performed on the resource:

| Method  | Purpose | Idempotent | Safe | Request Body | Response Body | Common Use Cases |
|---------|---------|------------|------|--------------|---------------|------------------|
| GET     | Retrieve resource | Yes | Yes | No | Yes | Fetch webpage, API data |
| POST    | Create resource | No | No | Yes | Optional | Submit form, upload data |
| PUT     | Update/replace resource | Yes | No | Yes | Optional | Update existing resource |
| DELETE  | Remove resource | Yes | No | Optional | Optional | Remove resource from server |
| HEAD    | Get headers only | Yes | Yes | No | No | Check resource metadata |
| OPTIONS | Get supported operations | Yes | Yes | No | Yes | CORS preflight, API discovery |
| PATCH   | Partial update | No | No | Yes | Optional | Partial resource updates |

> **Note:** 
> - **Idempotent**: Multiple identical requests have the same effect as a single request
> - **Safe**: Does not alter the server's state

#### 4. Status Codes

HTTP status codes indicate the result of the request:

| Category | Range | Meaning | Common Examples |
|----------|-------|---------|----------------|
| **1xx** | 100-199 | Informational | 100 Continue, 101 Switching Protocols |
| **2xx** | 200-299 | Success | 200 OK, 201 Created, 204 No Content |
| **3xx** | 300-399 | Redirection | 301 Moved Permanently, 302 Found, 304 Not Modified |
| **4xx** | 400-499 | Client Error | 400 Bad Request, 404 Not Found, 403 Forbidden |
| **5xx** | 500-599 | Server Error | 500 Internal Server Error, 503 Service Unavailable |

#### 5. Common Headers

Headers provide metadata about the request or response:

**Request Headers**:
- `Host`: Domain name of the server (required in HTTP/1.1)
- `User-Agent`: Client software information
- `Accept`: Media types the client can process
- `Content-Type`: Format of request body
- `Content-Length`: Size of request body
- `Authorization`: Authentication credentials

**Response Headers**:
- `Content-Type`: Format of response body (e.g., text/html, application/json)
- `Content-Length`: Size of response body in bytes
- `Server`: Information about the server software
- `Set-Cookie`: Sets a cookie for the client
- `Cache-Control`: Directives for caching mechanisms

## Implementation Plan

We'll implement our HTTP protocol support in a structured manner, focusing on modularity and clean design. Our implementation will follow these steps:

### Implementation Strategy

1. **Define Core HTTP Types**: 
   - Create enums for HTTP methods, status codes, and versions
   - Implement traits for conversion between strings and our type-safe representations
   - Design a module structure that separates concerns

2. **Implement Request Parsing**:
   - Build a `Request` struct to represent HTTP requests
   - Create a parser to extract information from raw TCP streams
   - Handle headers, request line, and body parsing
   - Implement robust error handling for malformed requests

3. **Develop Response Generation**:
   - Design a `Response` struct with appropriate fields
   - Implement the Builder pattern for fluent response creation
   - Create methods for writing responses back to TCP streams
   - Support standard headers and content types

4. **Integrate with the TCP Server**:
   - Update the connection handler to use our HTTP components
   - Implement basic routing based on request paths
   - Add appropriate error responses for invalid requests

5. **Add Testing and Validation**:
   - Create unit tests for request parsing and response generation
   - Implement integration tests for the complete HTTP flow
   - Validate compliance with HTTP/1.1 specifications

### Module Structure

```
src/
├── http/
│   ├── mod.rs       # Core HTTP types and re-exports
│   ├── request.rs   # HTTP request parsing
│   └── response.rs  # HTTP response generation
└── server/
    └── mod.rs       # Updated to use HTTP components
```

This approach ensures our code is maintainable and follows the separation of concerns principle.

## HTTP Enums and Common Types

First, let's define the core HTTP types in `src/http/mod.rs`:

```rust
// HTTP module
// This contains our HTTP protocol implementation
pub mod request;
pub mod response;

/// HTTP method enum representing the different HTTP verbs
#[derive(Debug, PartialEq, Clone)]
pub enum Method {
    GET,
    POST,
    PUT,
    DELETE,
    HEAD,
    OPTIONS,
    CONNECT,
    TRACE,
    PATCH,
    UNKNOWN,
}

impl From<&str> for Method {
    fn from(s: &str) -> Self {
        match s.to_uppercase().as_str() {
            "GET" => Method::GET,
            "POST" => Method::POST,
            "PUT" => Method::PUT,
            "DELETE" => Method::DELETE,
            "HEAD" => Method::HEAD,
            "OPTIONS" => Method::OPTIONS,
            "CONNECT" => Method::CONNECT,
            "TRACE" => Method::TRACE,
            "PATCH" => Method::PATCH,
            _ => Method::UNKNOWN,
        }
    }
}

/// HTTP version enum
#[derive(Debug, PartialEq, Clone)]
pub enum Version {
    HTTP1_0,
    HTTP1_1,
    HTTP2_0,
    UNKNOWN,
}

impl From<&str> for Version {
    fn from(s: &str) -> Self {
        match s {
            "HTTP/1.0" => Version::HTTP1_0,
            "HTTP/1.1" => Version::HTTP1_1,
            "HTTP/2.0" => Version::HTTP2_0,
            _ => Version::UNKNOWN,
        }
    }
}

/// HTTP status codes
#[derive(Debug, PartialEq, Clone, Copy)]
pub enum StatusCode {
    Ok = 200,
    Created = 201,
    Accepted = 202,
    NoContent = 204,
    BadRequest = 400,
    Unauthorized = 401,
    Forbidden = 403,
    NotFound = 404,
    MethodNotAllowed = 405,
    InternalServerError = 500,
    NotImplemented = 501,
    BadGateway = 502,
    ServiceUnavailable = 503,
}

impl StatusCode {
    /// Returns the status code number
    pub fn code(&self) -> u16 {
        *self as u16
    }

    /// Returns the reason phrase for the status code
    pub fn reason_phrase(&self) -> &str {
        match self {
            Self::Ok => "OK",
            Self::Created => "Created",
            Self::Accepted => "Accepted",
            Self::NoContent => "No Content",
            Self::BadRequest => "Bad Request",
            Self::Unauthorized => "Unauthorized",
            Self::Forbidden => "Forbidden",
            Self::NotFound => "Not Found",
            Self::MethodNotAllowed => "Method Not Allowed",
            Self::InternalServerError => "Internal Server Error",
            Self::NotImplemented => "Not Implemented",
            Self::BadGateway => "Bad Gateway",
            Self::ServiceUnavailable => "Service Unavailable",
        }
    }
}
```

### Rust Language Features in This Code

- **Enums**: Used to represent finite sets of values (HTTP methods, versions, status codes)
- **Traits**: Implementation of the `From` trait allows conversion from strings to our enum types
- **Match expressions**: Used for converting strings to enum variants
- **Deriving traits**: Using `#[derive(Debug, PartialEq, Clone)]` to automatically implement common traits
- **Documentation comments**: Using `///` for generating documentation
- **Method implementation**: Adding methods to enums like `code()` and `reason_phrase()`

## HTTP Request Implementation

Now, let's implement the HTTP request parsing in `src/http/request.rs`:

```rust
use std::collections::HashMap;
use super::{Method, Version};
use std::io::prelude::*;
use std::io::BufReader;
use std::net::TcpStream;

/// Represents an HTTP request received from a client
#[derive(Debug)]
pub struct Request {
    /// HTTP method (GET, POST, etc.)
    pub method: Method,
    /// Request URI path
    pub path: String,
    /// HTTP protocol version
    pub version: Version,
    /// Request headers
    pub headers: HashMap<String, String>,
    /// Request body (for POST, PUT requests)
    pub body: Vec<u8>,
}

impl Request {
    /// Parses an HTTP request from a TCP stream
    pub fn from_stream(stream: &mut TcpStream) -> Result<Self, std::io::Error> {
        let mut reader = BufReader::new(stream);
        let mut request_line = String::new();
        reader.read_line(&mut request_line)?;

        // Parse the request line (METHOD /path HTTP/x.x)
        let parts: Vec<&str> = request_line.trim().split_whitespace().collect();
        if parts.len() < 3 {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                "Invalid HTTP request line",
            ));
        }

        let method = Method::from(parts[0]);
        let path = String::from(parts[1]);
        let version = Version::from(parts[2]);

        // Parse headers
        let mut headers = HashMap::new();
        loop {
            let mut header_line = String::new();
            reader.read_line(&mut header_line)?;
            let header_line = header_line.trim();

            if header_line.is_empty() {
                break; // End of headers
            }

            if let Some(pos) = header_line.find(':') {
                let (name, value) = header_line.split_at(pos);
                let value = value[1..].trim(); // Skip the ':' and trim
                headers.insert(name.to_string(), value.to_string());
            }
        }

        // Read body if content length is specified
        let mut body = Vec::new();
        if let Some(content_length) = headers.get("Content-Length") {
            if let Ok(length) = content_length.parse::<usize>() {
                let mut buffer = vec![0; length];
                reader.read_exact(&mut buffer)?;
                body = buffer;
            }
        }

        Ok(Request {
            method,
            path,
            version,
            headers,
            body,
        })
    }

    /// Gets the value of a specified header
    pub fn get_header(&self, name: &str) -> Option<&String> {
        // Case-insensitive header lookup
        for (key, value) in &self.headers {
            if key.to_lowercase() == name.to_lowercase() {
                return Some(value);
            }
        }
        None
    }
}
```

### Request Parser Design Considerations

In designing the HTTP request parser, we made several important decisions:

1. **Using BufReader**
   - **Decision**: We used `BufReader` to read from the TCP stream.
   - **Alternative**: Direct reading from the stream without buffering.
   - **Tradeoff**: `BufReader` provides efficient line-based reading, which is perfect for HTTP headers, at the cost of a small memory overhead.

2. **Header Storage**
   - **Decision**: We used a `HashMap<String, String>` for headers.
   - **Alternative**: A specialized header struct with common headers as typed fields.
   - **Tradeoff**: The HashMap approach is simpler and handles any header, but doesn't provide type safety for well-known headers.

3. **Body Handling**
   - **Decision**: We read the entire body at once if Content-Length is specified.
   - **Alternative**: Streaming body parsing or lazy loading.
   - **Tradeoff**: Our approach is simple but has memory implications for large requests.

4. **Error Handling**
   - **Decision**: We use standard I/O errors.
   - **Alternative**: Custom error types for HTTP-specific errors.
   - **Tradeoff**: Standard errors are simpler, but less specific. We'll evolve to custom errors later.

## HTTP Response Implementation

Next, let's implement the HTTP response generation in `src/http/response.rs`:

```rust
use std::collections::HashMap;
use std::io::{Result, Write};
use super::{StatusCode, Version};

/// Represents an HTTP response to send back to a client
pub struct Response {
    /// HTTP protocol version
    pub version: Version,
    /// HTTP status code
    pub status: StatusCode,
    /// Response headers
    pub headers: HashMap<String, String>,
    /// Response body
    pub body: Vec<u8>,
}

impl Response {
    /// Creates a new HTTP response with default values
    pub fn new() -> Self {
        let mut headers = HashMap::new();
        headers.insert(String::from("Content-Type"), String::from("text/html"));
        headers.insert(String::from("Server"), String::from("RustyServer/0.1.0"));
        
        Self {
            version: Version::HTTP1_1,
            status: StatusCode::Ok,
            headers,
            body: Vec::new(),
        }
    }
    
    /// Sets the HTTP status code for the response
    pub fn with_status(mut self, status: StatusCode) -> Self {
        self.status = status;
        self
    }

    /// Sets a response header
    pub fn with_header(mut self, name: &str, value: &str) -> Self {
        self.headers.insert(name.to_string(), value.to_string());
        self
    }

    /// Sets the response body
    pub fn with_body(mut self, body: Vec<u8>) -> Self {
        self.headers.insert(
            String::from("Content-Length"), 
            body.len().to_string()
        );
        self.body = body;
        self
    }

    /// Sets the response body from a string
    pub fn with_text(self, text: &str) -> Self {
        self.with_body(text.as_bytes().to_vec())
    }

    /// Sets the response content type
    pub fn with_content_type(mut self, content_type: &str) -> Self {
        self.headers.insert(String::from("Content-Type"), content_type.to_string());
        self
    }

    /// Writes the complete response to the provided writer
    pub fn write_to<W: Write>(&self, writer: &mut W) -> Result<()> {
        // Write status line
        let version = match self.version {
            Version::HTTP1_0 => "HTTP/1.0",
            Version::HTTP1_1 => "HTTP/1.1",
            Version::HTTP2_0 => "HTTP/2.0",
            Version::UNKNOWN => "HTTP/1.1", // Default to HTTP/1.1 if unknown
        };
        
        writeln!(
            writer, 
            "{} {} {}", 
            version, 
            self.status.code(), 
            self.status.reason_phrase()
        )?;
        
        // Write headers
        for (name, value) in &self.headers {
            writeln!(writer, "{}: {}", name, value)?;
        }
        
        // End headers section
        writeln!(writer)?;
        
        // Write body
        writer.write_all(&self.body)?;
        
        Ok(())
    }
}
```

### Response Builder Design Considerations

Our HTTP response implementation uses the Builder Pattern, which offers several advantages:

1. **Builder Pattern**
   - **Decision**: We used method chaining for constructing responses.
   - **Alternative**: Traditional setter methods or direct struct initialization.
   - **Tradeoff**: Builder pattern provides a fluent, readable API, but adds some complexity.

2. **Generic Writer**
   - **Decision**: The `write_to` method works with any type that implements `Write`.
   - **Alternative**: Working directly with `TcpStream`.
   - **Tradeoff**: This makes the code more testable and flexible, at the cost of a slightly more complex interface.

3. **Default Values**
   - **Decision**: We provide sensible defaults for a new response.
   - **Alternative**: Requiring all fields to be specified.
   - **Tradeoff**: This makes the API more ergonomic for common cases, but might hide unexpected behavior.

## Updating the Server to Use HTTP Components

Now that we have our HTTP components, let's update the server to use them. Modify the `handle_connection` method in `src/server/mod.rs`:

```rust
fn handle_connection(&self, mut stream: TcpStream) -> io::Result<()> {
    // Get peer address for logging
    let peer_addr = stream.peer_addr()?;
    println!("Connection established from: {}", peer_addr);
    
    // Parse the HTTP request from the stream
    let request = match http::request::Request::from_stream(&mut stream) {
        Ok(req) => req,
        Err(e) => {
            eprintln!("Error parsing request: {}", e);
            
            // Send a 400 Bad Request response
            let response = http::response::Response::new()
                .with_status(http::StatusCode::BadRequest)
                .with_text("Bad Request");
                
            response.write_to(&mut stream)?;
            return Ok(());
        }
    };
    
    println!("Received {} request for {}", request.method, request.path);
    
    // Create a simple response
    let response = http::response::Response::new()
        .with_status(http::StatusCode::Ok)
        .with_text("Hello from Rusty Server!");
    
    // Send the response
    response.write_to(&mut stream)?;
    
    println!("Response sent to {}", peer_addr);
    
    Ok(())
}
```

### Testing Our HTTP Implementation

Let's update our test function in `src/server/tests.rs` to verify the HTTP parsing:

```rust
#[test]
fn test_http_request_response() {
    // Start server on a specific test port
    let address = "127.0.0.1:8082";
    let handle = start_test_server(address);
    
    // Give the server a moment to start
    thread::sleep(Duration::from_millis(100));
    
    // Connect to the server
    let mut stream = TcpStream::connect(address).expect("Failed to connect to test server");
    
    // Send a custom HTTP request with headers
    let request = "GET /test HTTP/1.1\r\nHost: localhost\r\nUser-Agent: Test\r\n\r\n";
    stream.write_all(request.as_bytes()).expect("Failed to send request");
    
    // Read the response
    let mut buffer = [0; 1024];
    let bytes_read = stream.read(&mut buffer).expect("Failed to read response");
    let response = String::from_utf8_lossy(&buffer[0..bytes_read]);
    
    // Check the response
    assert!(response.contains("HTTP/1.1 200 OK"));
    assert!(response.contains("Content-Type: text/html"));
    assert!(response.contains("Server: RustyServer/0.1.0"));
    assert!(response.contains("Hello from Rusty Server!"));
}
```

### Running the Enhanced Server

Rebuild and run the server:

```bash
cargo run
```

You can now test various HTTP requests:

```bash
# Basic GET request
curl http://localhost:8080

# Custom request with headers
curl -H "X-Custom-Header: Value" http://localhost:8080

# Different HTTP methods
curl -X POST http://localhost:8080
```

## Summary

In this module, we've built a robust HTTP implementation that transforms our basic TCP server into a fully-functional HTTP server. We've:

- Created type-safe representations of HTTP methods, versions, and status codes
- Implemented request parsing from TCP streams with proper error handling
- Built a flexible response generation system using the Builder pattern
- Integrated HTTP handling into our TCP server
- Added tests to validate our HTTP implementation

Our web server is now HTTP-compliant and capable of understanding and responding to standard web requests. This forms the foundation for more advanced features we'll implement in future modules.

## Knowledge Check

1. **Which part of the HTTP request contains the resource path and HTTP method?**
   - A) Request headers
   - B) Request line
   - C) Request body
   - D) Query string

2. **In our implementation, what data structure did we use to store HTTP headers?**
   - A) Vector of tuples
   - B) LinkedList
   - C) HashMap
   - D) TreeMap

3. **Which HTTP status code range indicates a client error?**
   - A) 100-199
   - B) 200-299
   - C) 300-399
   - D) 400-499

4. **What design pattern did we use to construct HTTP responses?**
   - A) Factory pattern
   - B) Singleton pattern
   - C) Observer pattern
   - D) Builder pattern

5. **Which of the following HTTP methods is NOT idempotent?**
   - A) GET
   - B) PUT
   - C) POST
   - D) DELETE

<details>
<summary>Click to see answers</summary>

1. B) Request line
2. C) HashMap
3. D) 400-499
4. D) Builder pattern
5. C) POST
</details>

## Additional Resources

### HTTP Specifications and Standards
- [RFC 7230: HTTP/1.1 Message Syntax and Routing](https://datatracker.ietf.org/doc/html/rfc7230)
- [RFC 7231: HTTP/1.1 Semantics and Content](https://datatracker.ietf.org/doc/html/rfc7231)
- [MDN Web Docs: HTTP](https://developer.mozilla.org/en-US/docs/Web/HTTP) - Comprehensive guide to HTTP concepts

### Rust-Specific Resources
- [Rust Cookbook: HTTP Client Examples](https://rust-lang-nursery.github.io/rust-cookbook/web/clients.html)
- [hyper crate documentation](https://docs.rs/hyper/latest/hyper/) - A fast and correct HTTP implementation for Rust
- [reqwest documentation](https://docs.rs/reqwest/latest/reqwest/) - High-level HTTP client

### Books and In-Depth Learning
- [HTTP: The Definitive Guide](https://www.oreilly.com/library/view/http-the-definitive/**********/) - Comprehensive coverage of HTTP
- [RESTful Web APIs](https://www.oreilly.com/library/view/restful-web-apis/*************/) - Understanding HTTP in the context of APIs

### Tools for HTTP Testing and Debugging
- [Postman](https://www.postman.com/) - API development and testing platform
- [curl documentation](https://curl.se/docs/) - Command-line tool for transferring data with URLs
- [Wireshark HTTP documentation](https://wiki.wireshark.org/HTTP) - Analyzing HTTP traffic

## Next Steps

While our web server is now HTTP-compliant, it still has several limitations:

1. It only serves a fixed response, regardless of the requested path
2. It doesn't handle static files or other resources
3. It processes only one request at a time

In the next module, we'll implement static file serving to address the first two limitations, allowing our server to deliver different content based on the requested URL path.

## Navigation
- [Previous: Basic TCP Server](02-basic-tcp-server.md)
- [Next: Static File Serving](04-static-file-serving.md)
