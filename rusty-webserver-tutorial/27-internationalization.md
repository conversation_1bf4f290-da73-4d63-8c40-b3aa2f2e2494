# Internationalization (i18n) Support

## Learning Objectives
- Understand internationalization (i18n) and localization (l10n) concepts
- Implement a robust i18n system in a Rust webserver
- Create and manage translation resources efficiently
- Detect and negotiate user language preferences
- Apply localization to various content types including templates, dates, and numbers

## Prerequisites
- Understanding of HTTP headers and request processing
- Familiarity with Rust string handling
- Basic knowledge of template rendering systems

## Introduction

Internationalization (i18n) allows your web application to support multiple languages and locales, making it accessible to a global audience. In this module, you'll learn how to implement a comprehensive internationalization system in a Rust webserver, from locale detection to translation management and content localization.

## i18n Fundamentals

### Key Concepts

- **Internationalization (i18n)**: The process of designing software so it can be adapted to various languages without engineering changes.
- **Localization (l10n)**: The process of adapting software for a specific language or region.
- **Locale**: A combination of language and regional settings (e.g., `en-US` for US English).
- **Translation Resources**: Structured files containing translated strings.
- **Message Formatting**: Converting strings with dynamic content for different languages.

### i18n Architecture Overview

A well-designed i18n system typically includes:

1. **Translation Resource Management**: Loading and caching translations
2. **Locale Detection**: Determining the user's preferred language
3. **Message Formatting**: Handling plurals, genders, dates, numbers, etc.
4. **Template Integration**: Incorporating translations into rendered content
5. **Fallback Mechanisms**: Handling missing translations gracefully

## Translation Resource Management

### Using Fluent for Natural-Sounding Translations

[Project Fluent](https://projectfluent.org/) is a modern localization system designed for natural-sounding translations. Let's implement a translation manager using Fluent:

```rust
use fluent::{FluentBundle, FluentResource, FluentValue};
use fluent_syntax::ast::Pattern;
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::RwLock;
use unic_langid::{langid, LanguageIdentifier};

// Supported locales in our application
pub const DEFAULT_LOCALE: LanguageIdentifier = langid!("en-US");
pub const SUPPORTED_LOCALES: &[LanguageIdentifier] = &[
    langid!("en-US"),
    langid!("es-ES"),
    langid!("fr-FR"),
    langid!("de-DE"),
    langid!("ja-JP"),
];

// Translation manager
pub struct TranslationManager {
    bundles: RwLock<HashMap<LanguageIdentifier, FluentBundle<FluentResource>>>,
    resources_path: PathBuf,
}

impl TranslationManager {
    pub fn new<P: AsRef<Path>>(resources_path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let resources_path = resources_path.as_ref().to_path_buf();
        
        // Create instance
        let mut manager = Self {
            bundles: RwLock::new(HashMap::new()),
            resources_path,
        };
        
        // Initialize bundles for all supported locales
        manager.init_bundles()?;
        
        Ok(manager)
    }
    
    // Initialize translation bundles for all supported locales
    fn init_bundles(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        for locale in SUPPORTED_LOCALES {
            self.load_bundle(locale)?;
        }
        
        Ok(())
    }
    
    // Load a translation bundle for a specific locale
    fn load_bundle(&self, locale: &LanguageIdentifier) -> Result<(), Box<dyn std::error::Error>> {
        // Build resource path for the locale
        let resource_path = self.resources_path
            .join(locale.to_string())
            .join("main.ftl");
        
        // Check if the resource exists
        if !resource_path.exists() {
            println!("Warning: No translation resource found for {}", locale);
            return Ok(());
        }
        
        // Read the resource file
        let source = fs::read_to_string(&resource_path)?;
        
        // Parse the resource
        let resource = match FluentResource::try_new(source) {
            Ok(res) => res,
            Err((res, errors)) => {
                for error in errors {
                    eprintln!("Error in {}: {}", resource_path.display(), error);
                }
                res
            }
        };
        
        // Create a bundle for the locale
        let mut bundle = FluentBundle::new(vec![locale.clone()]);
        
        // Add the resource to the bundle
        if let Err(errors) = bundle.add_resource(resource) {
            for error in errors {
                eprintln!("Error adding resource for {}: {}", locale, error);
            }
        }
        
        // Store the bundle
        let mut bundles = self.bundles.write().unwrap();
        bundles.insert(locale.clone(), bundle);
        
        // Try to load additional resources (e.g., domain-specific files)
        self.load_additional_resources(locale)?;
        
        println!("Loaded translations for {}", locale);
        Ok(())
    }
    
    // Load additional translation resources for specific domains
    fn load_additional_resources(&self, locale: &LanguageIdentifier) -> Result<(), Box<dyn std::error::Error>> {
        let domains = ["errors", "admin", "user"];
        let locale_dir = self.resources_path.join(locale.to_string());
        
        if !locale_dir.exists() {
            return Ok(());
        }
        
        let mut bundles = self.bundles.write().unwrap();
        let bundle = bundles.get_mut(locale).ok_or("Bundle not found")?;
        
        for domain in domains {
            let resource_path = locale_dir.join(format!("{}.ftl", domain));
            
            if resource_path.exists() {
                let source = fs::read_to_string(&resource_path)?;
                let resource = FluentResource::try_new(source).map_err(|(_, errors)| {
                    format!("Failed to parse {}: {:?}", resource_path.display(), errors)
                })?;
                
                if let Err(errors) = bundle.add_resource(resource) {
                    for error in errors {
                        eprintln!(
                            "Error adding resource {} for {}: {}",
                            domain, locale, error
                        );
                    }
                }
                
                println!("Loaded {} translations for {}", domain, locale);
            }
        }
        
        Ok(())
    }
    
    // Get a translated message with arguments
    pub fn get_message(
        &self,
        locale: &LanguageIdentifier,
        message_id: &str,
        args: Option<HashMap<&str, FluentValue<'_>>>,
    ) -> String {
        let bundles = self.bundles.read().unwrap();
        
        // Try requested locale
        if let Some(bundle) = bundles.get(locale) {
            if let Some(msg) = bundle.get_message(message_id) {
                if let Some(pattern) = msg.value() {
                    let mut errors = vec![];
                    let result = bundle.format_pattern(
                        pattern,
                        args.as_ref(),
                        &mut errors,
                    );
                    
                    if errors.is_empty() {
                        return result.to_string();
                    }
                    
                    for error in errors {
                        eprintln!("Error formatting message {}: {:?}", message_id, error);
                    }
                }
            }
        }
        
        // If not found or error, try default locale
        if locale != &DEFAULT_LOCALE {
            if let Some(bundle) = bundles.get(&DEFAULT_LOCALE) {
                if let Some(msg) = bundle.get_message(message_id) {
                    if let Some(pattern) = msg.value() {
                        let mut errors = vec![];
                        let result = bundle.format_pattern(
                            pattern,
                            args.as_ref(),
                            &mut errors,
                        );
                        
                        if errors.is_empty() {
                            return result.to_string();
                        }
                    }
                }
            }
        }
        
        // Return message ID as fallback
        format!("[{}]", message_id)
    }
    
    // Reload translations for a specific locale
    pub fn reload_locale(&self, locale: &LanguageIdentifier) -> Result<(), Box<dyn std::error::Error>> {
        {
            let mut bundles = self.bundles.write().unwrap();
            bundles.remove(locale);
        }
        
        self.load_bundle(locale)?;
        
        Ok(())
    }
    
    // Reload all translations
    pub fn reload_all(&self) -> Result<(), Box<dyn std::error::Error>> {
        let locales: Vec<LanguageIdentifier> = {
            let bundles = self.bundles.read().unwrap();
            bundles.keys().cloned().collect()
        };
        
        for locale in locales {
            self.reload_locale(&locale)?;
        }
        
        Ok(())
    }
}
```

### Simple Example of a Fluent Translation File

Create a file at `resources/locales/en-US/main.ftl`:

```ftl
# Main translations for English (US)

welcome = Welcome to our website!
greeting = Hello, { $name }!
items = { $count ->
    [one] You have one item
   *[other] You have { $count } items
}
today-is = Today is { DATETIME($date, month: "long", day: "numeric", year: "numeric") }
```

And a Spanish version at `resources/locales/es-ES/main.ftl`:

```ftl
# Main translations for Spanish (Spain)

welcome = ¡Bienvenido a nuestro sitio web!
greeting = ¡Hola, { $name }!
items = { $count ->
    [one] Tienes un elemento
   *[other] Tienes { $count } elementos
}
today-is = Hoy es { DATETIME($date, month: "long", day: "numeric", year: "numeric") }
```

## Locale Detection and Negotiation

### Implementing Locale Detection

The next step is to detect the user's preferred language:

```rust
use unic_langid::LanguageIdentifier;
use accept_language::{parse, intersection, Match};
use std::str::FromStr;
use actix_web::{HttpRequest, web};
use actix_web::http::header;
use std::sync::Arc;

// Language preferences stored in user session
#[derive(Debug, Clone)]
pub struct UserLanguagePreference {
    pub locale: LanguageIdentifier,
    pub source: LocaleSource,
}

#[derive(Debug, Clone, PartialEq)]
pub enum LocaleSource {
    Default,
    Cookie,
    Session,
    QueryParam,
    AcceptLanguage,
}

// Locale detector
pub struct LocaleDetector {
    default_locale: LanguageIdentifier,
    supported_locales: Vec<LanguageIdentifier>,
    translation_manager: Arc<TranslationManager>,
}

impl LocaleDetector {
    pub fn new(
        default_locale: LanguageIdentifier,
        supported_locales: Vec<LanguageIdentifier>,
        translation_manager: Arc<TranslationManager>,
    ) -> Self {
        Self {
            default_locale,
            supported_locales,
            translation_manager,
        }
    }
    
    // Detect locale from request
    pub fn detect_locale(&self, req: &HttpRequest) -> UserLanguagePreference {
        // 1. Check query parameter
        if let Some(lang) = self.get_locale_from_query(req) {
            return UserLanguagePreference {
                locale: lang,
                source: LocaleSource::QueryParam,
            };
        }
        
        // 2. Check cookie
        if let Some(lang) = self.get_locale_from_cookie(req) {
            return UserLanguagePreference {
                locale: lang,
                source: LocaleSource::Cookie,
            };
        }
        
        // 3. Check session
        if let Some(lang) = self.get_locale_from_session(req) {
            return UserLanguagePreference {
                locale: lang,
                source: LocaleSource::Session,
            };
        }
        
        // 4. Check Accept-Language header
        if let Some(lang) = self.get_locale_from_header(req) {
            return UserLanguagePreference {
                locale: lang,
                source: LocaleSource::AcceptLanguage,
            };
        }
        
        // 5. Use default
        UserLanguagePreference {
            locale: self.default_locale.clone(),
            source: LocaleSource::Default,
        }
    }
    
    // Get locale from query parameter
    fn get_locale_from_query(&self, req: &HttpRequest) -> Option<LanguageIdentifier> {
        req.query_string()
            .split('&')
            .find_map(|pair| {
                if let Some(lang) = pair.strip_prefix("lang=") {
                    self.parse_and_validate_locale(lang)
                } else {
                    None
                }
            })
    }
    
    // Get locale from cookie
    fn get_locale_from_cookie(&self, req: &HttpRequest) -> Option<LanguageIdentifier> {
        req.cookie("locale")
            .and_then(|cookie| self.parse_and_validate_locale(cookie.value()))
    }
    
    // Get locale from session
    fn get_locale_from_session(&self, req: &HttpRequest) -> Option<LanguageIdentifier> {
        req.extensions()
            .get::<web::Data<actix_session::Session>>()
            .and_then(|session| {
                session.get::<String>("locale").ok().flatten()
            })
            .and_then(|locale_str| self.parse_and_validate_locale(&locale_str))
    }
    
    // Get locale from Accept-Language header
    fn get_locale_from_header(&self, req: &HttpRequest) -> Option<LanguageIdentifier> {
        if let Some(accept_lang) = req.headers().get(header::ACCEPT_LANGUAGE) {
            if let Ok(header_value) = accept_lang.to_str() {
                // Parse the Accept-Language header
                let langs = parse(header_value).unwrap_or_default();
                
                // Convert our supported locales to compatible format
                let supported: Vec<&str> = self.supported_locales
                    .iter()
                    .map(|lang| lang.to_string().as_str())
                    .collect();
                
                // Find the best match
                if let Some(matched) = intersection(&langs, &supported) {
                    match matched {
                        Match::Exact(tag) | Match::Language(tag) => {
                            return LanguageIdentifier::from_str(tag).ok();
                        }
                        _ => {}
                    }
                }
            }
        }
        
        None
    }
    
    // Parse and validate a locale string
    fn parse_and_validate_locale(&self, locale_str: &str) -> Option<LanguageIdentifier> {
        if let Ok(lang_id) = LanguageIdentifier::from_str(locale_str) {
            if self.supported_locales.contains(&lang_id) {
                return Some(lang_id);
            }
            
            // If full locale not supported, try just the language part
            for supported in &self.supported_locales {
                if supported.language == lang_id.language {
                    return Some(supported.clone());
                }
            }
        }
        
        None
    }
    
    // Get the translation manager
    pub fn translation_manager(&self) -> Arc<TranslationManager> {
        self.translation_manager.clone()
    }
}
```

### Middleware for Automatic Locale Detection

Create middleware to detect locale for every request:

```rust
use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage,
};
use std::future::{ready, Ready, Future};
use std::pin::Pin;
use std::sync::Arc;

// Locale middleware
pub struct LocaleMiddleware {
    locale_detector: Arc<LocaleDetector>,
}

impl LocaleMiddleware {
    pub fn new(locale_detector: Arc<LocaleDetector>) -> Self {
        Self { locale_detector }
    }
}

impl<S, B> Transform<S, ServiceRequest> for LocaleMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = LocaleMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(LocaleMiddlewareService {
            service,
            locale_detector: self.locale_detector.clone(),
        }))
    }
}

pub struct LocaleMiddlewareService<S> {
    service: S,
    locale_detector: Arc<LocaleDetector>,
}

impl<S, B> Service<ServiceRequest> for LocaleMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let locale_detector = self.locale_detector.clone();
        let mut req = req;
        
        // Detect user's preferred locale
        let user_pref = locale_detector.detect_locale(&req);
        
        // Store locale in request extensions for later use
        req.extensions_mut().insert(user_pref.clone());
        
        let fut = self.service.call(req);

        Box::pin(async move {
            let mut res = fut.await?;
            
            // If locale was determined from query or header, set a cookie
            if matches!(user_pref.source, LocaleSource::QueryParam | LocaleSource::AcceptLanguage) {
                let cookie = cookie::Cookie::build("locale", user_pref.locale.to_string())
                    .path("/")
                    .max_age(cookie::time::Duration::days(365))
                    .http_only(true)
                    .finish();
                    
                res.response_mut().add_cookie(&cookie).ok();
            }
            
            Ok(res)
        })
    }
}
```

## Integrating i18n with Templates

### Tera Template Integration

Integrate translations with Tera templates:

```rust
use tera::{Tera, Context, Result as TeraResult, Value};
use std::collections::HashMap;
use std::sync::Arc;
use unic_langid::LanguageIdentifier;
use serde_json::json;

// Add translation functions to Tera
pub fn register_i18n_functions(
    tera: &mut Tera,
    translation_manager: Arc<TranslationManager>,
) -> TeraResult<()> {
    // Register the t (translate) function
    tera.register_function(
        "t",
        move |args: &HashMap<String, Value>| -> TeraResult<Value> {
            // Extract arguments
            let key = match args.get("key") {
                Some(k) => match k.as_str() {
                    Some(s) => s,
                    None => return Err("t: key must be a string".into()),
                },
                None => return Err("t: key is required".into()),
            };
            
            // Get locale from context or use default
            let locale_str = match args.get("locale") {
                Some(l) => match l.as_str() {
                    Some(s) => s,
                    None => "en-US",
                },
                None => "en-US",
            };
            
            let locale = locale_str.parse::<LanguageIdentifier>()
                .unwrap_or_else(|_| DEFAULT_LOCALE.clone());
                
            // Extract named arguments for interpolation
            let mut fluent_args = HashMap::new();
            for (arg_name, arg_value) in args {
                if arg_name != "key" && arg_name != "locale" {
                    let fluent_value = match arg_value {
                        Value::String(s) => FluentValue::String(s.clone().into()),
                        Value::Number(n) if n.is_i64() => {
                            FluentValue::Number(n.as_i64().unwrap().into())
                        }
                        Value::Number(n) if n.is_f64() => {
                            FluentValue::Number(n.as_f64().unwrap().into())
                        }
                        Value::Bool(b) => FluentValue::String(b.to_string().into()),
                        _ => FluentValue::String(arg_value.to_string().into()),
                    };
                    fluent_args.insert(arg_name.as_str(), fluent_value);
                }
            }
            
            // Get the translated message
            let message = translation_manager.get_message(
                &locale,
                key,
                Some(fluent_args),
            );
            
            Ok(Value::String(message))
        },
    );
    
    Ok(())
}

// Example of using translation in a template:
/*
<h1>{{ t(key="welcome") }}</h1>
<p>{{ t(key="greeting", name=user.name) }}</p>
<p>{{ t(key="items", count=items.len) }}</p>
*/

// Integration with request handler
use actix_web::{web, HttpResponse, HttpRequest, Responder};

async fn index(
    req: HttpRequest,
    tmpl: web::Data<Tera>,
    locale_detector: web::Data<Arc<LocaleDetector>>,
) -> impl Responder {
    // Get user language preference from request extensions
    let lang_pref = req.extensions()
        .get::<UserLanguagePreference>()
        .cloned()
        .unwrap_or_else(|| UserLanguagePreference {
            locale: DEFAULT_LOCALE.clone(),
            source: LocaleSource::Default,
        });
    
    // Prepare template context
    let mut context = Context::new();
    context.insert("locale", &lang_pref.locale.to_string());
    context.insert("user", &json!({
        "name": "John Doe",
    }));
    context.insert("items_count", &5);
    
    // Render template with translations
    let body = tmpl.render("index.html", &context)
        .unwrap_or_else(|e| format!("Template error: {}", e));
    
    HttpResponse::Ok().body(body)
}
```

### Example Template with Translations

```html
<!DOCTYPE html>
<html lang="{{ locale }}">
<head>
    <meta charset="UTF-8">
    <title>{{ t(key="site-title", locale=locale) }}</title>
</head>
<body>
    <header>
        <h1>{{ t(key="welcome", locale=locale) }}</h1>
    </header>
    
    <main>
        <p>{{ t(key="greeting", locale=locale, name=user.name) }}</p>
        <p>{{ t(key="items", locale=locale, count=items_count) }}</p>
    </main>
    
    <footer>
        <p>{{ t(key="copyright", locale=locale, year=2023) }}</p>
        
        <div class="language-selector">
            <label>{{ t(key="select-language", locale=locale) }}</label>
            <select onchange="changeLanguage(this.value)">
                <option value="en-US" {% if locale == "en-US" %}selected{% endif %}>English (US)</option>
                <option value="es-ES" {% if locale == "es-ES" %}selected{% endif %}>Español (España)</option>
                <option value="fr-FR" {% if locale == "fr-FR" %}selected{% endif %}>Français (France)</option>
                <option value="de-DE" {% if locale == "de-DE" %}selected{% endif %}>Deutsch (Deutschland)</option>
                <option value="ja-JP" {% if locale == "ja-JP" %}selected{% endif %}>日本語 (日本)</option>
            </select>
        </div>
    </footer>
    
    <script>
        function changeLanguage(lang) {
            window.location.search = 'lang=' + lang;
        }
    </script>
</body>
</html>
```

## Date, Number, and Currency Formatting

Implement proper formatting for dates, numbers, and currencies:

```rust
use icu::calendar::DateTime;
use icu::datetime::{DateTimeFormat, options::style};
use icu::locid::Locale;
use icu::numbers::{NumberFormatter, NumberFormatterOptions};
use std::str::FromStr;

// Date formatter
pub struct DateFormatter {
    formatters: HashMap<LanguageIdentifier, DateTimeFormat>,
}

impl DateFormatter {
    pub fn new(locales: &[LanguageIdentifier]) -> Result<Self, Box<dyn std::error::Error>> {
        let mut formatters = HashMap::new();
        
        for locale_id in locales {
            let locale = Locale::from_str(&locale_id.to_string())?;
            
            // Create formatter for the locale with default options
            let formatter = DateTimeFormat::try_new_with_locale(
                &locale,
                style::Date::Long,
                style::Time::Short,
            )?;
            
            formatters.insert(locale_id.clone(), formatter);
        }
        
        Ok(Self { formatters })
    }
    
    // Format a date with the given locale
    pub fn format_date(
        &self,
        date: &chrono::DateTime<chrono::Utc>,
        locale: &LanguageIdentifier,
    ) -> String {
        let formatter = match self.formatters.get(locale) {
            Some(f) => f,
            None => return date.format("%Y-%m-%d %H:%M").to_string(),
        };
        
        // Convert chrono DateTime to ICU DateTime
        let icu_date = DateTime::try_new_gregorian_datetime(
            date.year(),
            (date.month() as i32).try_into().unwrap(),
            date.day() as i32,
            date.hour() as i32,
            date.minute() as i32,
            date.second() as i32,
        ).unwrap();
        
        // Format the date
        formatter.format(&icu_date).to_string()
    }
}

// Number formatter
pub struct NumberFormatter {
    formatters: HashMap<LanguageIdentifier, icu::numbers::NumberFormatter>,
}

impl NumberFormatter {
    pub fn new(locales: &[LanguageIdentifier]) -> Result<Self, Box<dyn std::error::Error>> {
        let mut formatters = HashMap::new();
        
        for locale_id in locales {
            let locale = Locale::from_str(&locale_id.to_string())?;
            
            // Create formatter for the locale
            let formatter = icu::numbers::NumberFormatter::try_new(
                &locale,
                NumberFormatterOptions::default(),
            )?;
            
            formatters.insert(locale_id.clone(), formatter);
        }
        
        Ok(Self { formatters })
    }
    
    // Format a number with the given locale
    pub fn format_number(
        &self,
        number: f64,
        locale: &LanguageIdentifier,
    ) -> String {
        let formatter = match self.formatters.get(locale) {
            Some(f) => f,
            None => return number.to_string(),
        };
        
        // Format the number
        formatter.format(number).to_string()
    }
    
    // Format currency with the given locale and currency code
    pub fn format_currency(
        &self,
        amount: f64,
        currency: &str,
        locale: &LanguageIdentifier,
    ) -> String {
        // In a real implementation, you'd use ICU's CurrencyFormatter
        // This is a simplified version
        let formatted_number = self.format_number(amount, locale);
        
        match locale.language.as_str() {
            "en" => format!("{} {}", currency, formatted_number),
            "fr" => format!("{} {}", formatted_number, currency),
            "de" => format!("{} {}", formatted_number, currency),
            "es" => format!("{} {}", formatted_number, currency),
            "ja" => format!("{}{}", currency, formatted_number),
            _ => format!("{} {}", currency, formatted_number),
        }
    }
}
```

## Pluralization and Complex Message Formatting

Fluent handles pluralization well, but here's more detail about how it works:

```ftl
# English pluralization
items = { $count ->
    [0] You have no items
    [one] You have one item
   *[other] You have { $count } items
}

# With gender
greeting = { $gender ->
   *[male] Hello Mr. { $name }
    [female] Hello Ms. { $name }
    [other] Hello { $name }
}

# Nested selectors (gender and count)
items-owner = { $gender ->
   *[male] { $count ->
        [0] He has no items
        [one] He has one item
       *[other] He has { $count } items
    }
    [female] { $count ->
        [0] She has no items
        [one] She has one item
       *[other] She has { $count } items
    }
    [other] { $count ->
        [0] They have no items
        [one] They have one item
       *[other] They have { $count } items
    }
}
```

## API Internationalization

For API responses, implement i18n for error messages and content:

```rust
use actix_web::{web, HttpResponse, Responder};
use serde::Serialize;
use unic_langid::LanguageIdentifier;

#[derive(Serialize)]
struct ApiResponse {
    success: bool,
    message: String,
    data: Option<serde_json::Value>,
}

async fn api_handler(
    req: actix_web::HttpRequest,
    locale_detector: web::Data<Arc<LocaleDetector>>,
) -> impl Responder {
    // Get user language preference
    let lang_pref = req.extensions()
        .get::<UserLanguagePreference>()
        .cloned()
        .unwrap_or_else(|| UserLanguagePreference {
            locale: DEFAULT_LOCALE.clone(),
            source: LocaleSource::Default,
        });
    
    let translation_manager = locale_detector.translation_manager();
    
    // Generate API response with translated message
    let response = ApiResponse {
        success: true,
        message: translation_manager.get_message(
            &lang_pref.locale,
            "api-welcome",
            None,
        ),
        data: Some(serde_json::json!({
            "user": {
                "greeting": translation_manager.get_message(
                    &lang_pref.locale,
                    "greeting",
                    Some([("name", FluentValue::String("API User".into()))].iter()
                        .cloned().collect()),
                )
            }
        })),
    };
    
    // Set content language header in response
    HttpResponse::Ok()
        .append_header(("Content-Language", lang_pref.locale.to_string()))
        .json(response)
}
```

## Translation Management

### Translation File Organization

Organize translation files by locale and domain:

```
resources/
├── locales/
│   ├── en-US/
│   │   ├── main.ftl
│   │   ├── admin.ftl
│   │   ├── errors.ftl
│   │   └── user.ftl
│   ├── es-ES/
│   │   ├── main.ftl
│   │   ├── admin.ftl
│   │   ├── errors.ftl
│   │   └── user.ftl
│   ├── fr-FR/
│   │   ├── main.ftl
│   │   └── ...
```

### Creating a Translation Dashboard

Build an admin interface to manage translations:

```rust
use actix_web::{web, HttpResponse, Responder, Error};
use std::fs;
use std::path::Path;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

// Translation entry in dashboard
#[derive(Debug, Serialize, Deserialize)]
struct TranslationEntry {
    key: String,
    text: HashMap<String, String>,
    description: Option<String>,
}

// Add update translation handler
async fn update_translation(
    web::Json(entry): web::Json<TranslationEntry>,
    locale_detector: web::Data<Arc<LocaleDetector>>,
    config: web::Data<AppConfig>,
) -> Result<impl Responder, Error> {
    // Security check (in real app, check admin permissions)
    
    // For each locale, update the translation file
    for (locale, text) in &entry.text {
        let locale_id = LanguageIdentifier::from_str(locale)
            .map_err(|e| actix_web::error::ErrorBadRequest(format!("Invalid locale: {}", e)))?;
        
        // Find appropriate domain file
        let (domain, key) = if entry.key.contains('.') {
            let parts: Vec<&str> = entry.key.splitn(2, '.').collect();
            (parts[0], parts[1])
        } else {
            ("main", &entry.key)
        };
        
        // Construct file path
        let file_path = Path::new(&config.locales_dir)
            .join(locale)
            .join(format!("{}.ftl", domain));
            
        // Check if file exists
        if !file_path.exists() {
            // Create directory if needed
            if let Some(parent) = file_path.parent() {
                fs::create_dir_all(parent)?;
            }
            
            // Create empty file
            fs::write(&file_path, "")?;
        }
        
        // Read existing content
        let mut content = fs::read_to_string(&file_path)?;
        
        // Check if key exists in file
        let key_pattern = format!("\n{} =", key);
        if content.contains(&key_pattern) {
            // Update existing entry
            let mut lines: Vec<String> = content.lines().map(String::from).collect();
            let mut in_key = false;
            let mut start_idx = 0;
            let mut end_idx = 0;
            
            // Find the key and its value
            for (i, line) in lines.iter().enumerate() {
                if line.trim().starts_with(&format!("{} =", key)) {
                    in_key = true;
                    start_idx = i;
                } else if in_key && !line.trim().is_empty() && !line.trim().starts_with(" ") {
                    // New key found, end of our entry
                    in_key = false;
                    end_idx = i;
                    break;
                }
            }
            
            if in_key {
                // If we're still in the key at the end, end_idx is the lines length
                end_idx = lines.len();
            }
            
            // Replace the lines for this key
            lines.splice(
                start_idx..end_idx,
                vec![format!("{} = {}", key, text)],
            );
            
            // Write back to file
            content = lines.join("\n");
        } else {
            // Add new entry
            if !content.is_empty() && !content.ends_with("\n") {
                content.push('\n');
            }
            
            // Add description if provided
            if let Some(desc) = &entry.description {
                content.push_str(&format!("# {}\n", desc));
            }
            
            // Add translation
            content.push_str(&format!("{} = {}\n", key, text));
        }
        
        // Write updated content
        fs::write(&file_path, content)?;
    }
    
    // Reload translations
    locale_detector.translation_manager().reload_all()?;
    
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": "Translation updated successfully"
    })))
}
```

## Putting It All Together

Here's how to integrate everything into a complete web application:

```rust
use actix_web::{web, App, HttpServer, middleware};
use std::sync::Arc;

// Application configuration
struct AppConfig {
    locales_dir: String,
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Initialize logger
    env_logger::init();
    
    // Initialize translation manager
    let translation_manager = Arc::new(
        TranslationManager::new("resources/locales")
            .expect("Failed to initialize translations")
    );
    
    // Initialize locale detector
    let locale_detector = Arc::new(
        LocaleDetector::new(
            DEFAULT_LOCALE.clone(),
            SUPPORTED_LOCALES.to_vec(),
            translation_manager.clone()
        )
    );
    
    // Initialize Tera templates
    let mut tera = Tera::new("templates/**/*").expect("Failed to initialize templates");
    register_i18n_functions(&mut tera, translation_manager.clone())
        .expect("Failed to register i18n functions");
        
    // Initialize date formatter
    let date_formatter = DateFormatter::new(SUPPORTED_LOCALES)
        .expect("Failed to initialize date formatter");
        
    // Initialize number formatter
    let number_formatter = NumberFormatter::new(SUPPORTED_LOCALES)
        .expect("Failed to initialize number formatter");
        
    // Start the server
    HttpServer::new(move || {
        App::new()
            // Middleware
            .wrap(middleware::Logger::default())
            .wrap(LocaleMiddleware::new(locale_detector.clone()))
            
            // Application data
            .app_data(web::Data::new(tera.clone()))
            .app_data(web::Data::new(locale_detector.clone()))
            .app_data(web::Data::new(date_formatter.clone()))
            .app_data(web::Data::new(number_formatter.clone()))
            .app_data(web::Data::new(AppConfig {
                locales_dir: "resources/locales".to_string(),
            }))
            
            // Routes
            .service(web::resource("/").to(index))
            .service(web::resource("/api/hello").to(api_handler))
            .service(
                web::scope("/admin")
                    .service(
                        web::resource("/translations")
                            .route(web::post().to(update_translation))
                    )
            )
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}
```

## Best Practices for i18n

### 1. Use Proper Message Keys

Organize keys hierarchically and descriptively:

```
# Good
user.greeting = Hello, { $name }!
error.not_found = Resource not found

# Bad
greeting = Hello, { $name }!
error1 = Resource not found
```

### 2. Provide Context for Translators

Add comments to help translators understand the context:

```ftl
# This appears on the homepage welcome banner
welcome = Welcome to our site!

# Displayed when a search returns no results
search-no-results = No results found for "{ $query }".
```

### 3. Handle RTL Languages

Support right-to-left languages with proper CSS and HTML:

```html
<!DOCTYPE html>
<html lang="{{ locale }}" dir="{{ locale_dir }}">
<head>
    <meta charset="UTF-8">
    <style>
        /* RTL-aware CSS */
        [dir="rtl"] .sidebar {
            float: right;
        }
        
        [dir="ltr"] .sidebar {
            float: left;
        }
    </style>
</head>
<body>
    <!-- Content -->
</body>
</html>
```

```rust
// Add direction information to template context
let locale_dir = match lang_pref.locale.language.as_str() {
    "ar" | "he" | "fa" | "ur" => "rtl",
    _ => "ltr",
};
context.insert("locale_dir", &locale_dir);
```

### 4. Use Unicode Correctly

Ensure proper Unicode support throughout your application:

```rust
// Ensure strings are valid UTF-8
fn validate_input(input: &str) -> bool {
    // All Rust strings are valid UTF-8, but you might need to check
    // if they contain valid characters for your target languages
    
    // For example, check if CJK characters are rendered properly
    true
}

// Use Unicode-aware operations
fn truncate_text(text: &str, max_length: usize) -> String {
    // Use Unicode character boundaries
    text.chars().take(max_length).collect::<String>()
}

// Handle text with combining marks
fn text_length(text: &str) -> usize {
    // Use grapheme clusters instead of chars for visual length
    use unicode_segmentation::UnicodeSegmentation;
    text.graphemes(true).count()
}
```

### 5. Avoid String Concatenation

Never concatenate translated strings:

```
# Wrong approach
greeting-start = Hello
greeting-end = , welcome to our site!

# Correct approach
greeting = Hello, welcome to our site!
greeting-name = Hello { $name }, welcome to our site!
```

## Knowledge Check

1. What is the difference between internationalization (i18n) and localization (l10n)?
   - Internationalization (i18n) is designing software to support multiple languages.
   - Localization (l10n) is adapting software for a specific language or region.

2. What is a locale and how is it identified?
   - A locale represents language and regional settings, usually identified by a language code (like "en") and optional region code (like "US"), combined as "en-US".

3. How does locale negotiation work?
   - Locale negotiation matches the user's preferred languages (from browser settings, cookies, etc.) with the languages supported by the application.

4. What challenges arise when handling pluralization in different languages?
   - Different languages have different pluralization rules (some have one form, others have two, six, or even more forms).
   - The grammatical structure may change completely depending on the number.

5. Why is it important to provide context for translators?
   - Context helps translators understand where and how the text is used, leading to more accurate and natural translations.
   - The same word can translate differently depending on context.

## Additional Resources

- [Project Fluent Documentation](https://projectfluent.org/)
- [ICU (International Components for Unicode)](http://site.icu-project.org/)
- [W3C Internationalization](https://www.w3.org/International/)
- [Unicode CLDR (Common Locale Data Repository)](http://cldr.unicode.org/)
- [Mozilla L10n Guidelines](https://mozilla-l10n.github.io/localizer-documentation/)
