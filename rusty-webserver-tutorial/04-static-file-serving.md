<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\04-static-file-serving.md -->
# Static File Serving

## Learning Objectives
- Implement a robust static file serving system for web content delivery
- Design secure path handling to prevent directory traversal attacks
- Create a content type detection mechanism based on file extensions
- Develop integration between HTTP request handling and filesystem access
- Implement proper error handling for file operations
- Write effective tests for static file serving functionality

## Prerequisites
- Completion of Module 03: HTTP Protocol
- Understanding of HTTP methods and responses
- Familiarity with Rust's file system APIs
- Knowledge of path manipulation in Rust
- Basic understanding of MIME types and content types

## Navigation
- [Previous: HTTP Protocol](03-http-protocol.md)
- [Next: Configuration System](05-configuration-system.md)

## Introduction
In this module, we'll enhance our web server to serve static files from the filesystem. Static file serving is one of the fundamental features of any web server, enabling the delivery of HTML, CSS, JavaScript, images, and other content to clients. This capability forms the core of what makes our project a true "web" server, as it can now deliver actual web content rather than just fixed responses.

## Static File Serving Overview

Static file serving is a core web server functionality that bridges the gap between the filesystem and HTTP. When a client requests a resource like `/images/logo.png`, the web server needs to locate this file on disk and deliver it through HTTP.

### The Static File Serving Process

```
┌───────────────┐         ┌───────────────┐         ┌───────────────┐
│  HTTP Request │         │  File System  │         │ HTTP Response │
│  GET /img.jpg │ ───────>│ /public/img.jpg│───────> │ 200 OK       │
│               │         │ (read file)   │         │ image/jpeg    │
└───────────────┘         └───────────────┘         └───────────────┘
```

The process involves several key steps:

1. **URL-to-Filesystem Mapping**: Translate the request URL path to a filesystem path
   - Example: `/images/logo.png` → `/var/www/public/images/logo.png`
   - Must handle URL encoding, path normalization, and security concerns

2. **File Existence & Access Check**: Verify the file exists and is accessible
   - Check read permissions and file existence
   - Handle errors gracefully with appropriate HTTP status codes (404, 403)

3. **Content Type Determination**: Set the correct MIME type in HTTP headers
   - Based on file extension (`.html` → `text/html`)
   - Affects how browsers interpret and render the content

4. **File Reading**: Access the file content from the filesystem
   - Handle I/O errors appropriately
   - Consider memory usage for large files

5. **Response Construction**: Send the file with appropriate HTTP headers
   - Set `Content-Type`, `Content-Length`, caching headers
   - Ensure proper encoding (binary vs. text)

### Security Considerations

Static file serving introduces several security concerns:

- **Directory Traversal Attacks**: Using `../` sequences to access files outside the web root
  ```
  GET /../../../../etc/passwd HTTP/1.1
  ```

- **Path Normalization Issues**: Differences in how paths are normalized can create vulnerabilities
  ```
  GET /public/..%2f..%2f..%2fetc/passwd HTTP/1.1
  ```

- **Symbolic Link Risks**: Symlinks might point to sensitive files outside the web directory

- **File Extension Hiding**: Attempts to disguise executable content as benign files
  ```
  GET /uploads/malicious.php.jpg HTTP/1.1
  ```

Our implementation will need to address these security concerns while providing efficient file serving capabilities.

## Implementing a Static File Handler

To implement static file serving, we'll create a dedicated handler that encapsulates all file-related operations. This approach follows the Single Responsibility Principle, making our code more maintainable and testable.

### Design Considerations

Before we write code, let's consider the key design aspects of our static file handler:

| Aspect | Options | Our Approach | Reasoning |
|--------|---------|--------------|-----------|
| **Modularity** | Inline code vs. Dedicated module | Dedicated module | Keeps server code clean and allows for easy testing |
| **Path Security** | Trust paths vs. Strict validation | Strict validation | Essential for preventing path traversal attacks |
| **Error Handling** | Generic errors vs. Specific errors | HTTP-mapped errors | Provides better diagnostics and client feedback |
| **File Reading** | Complete read vs. Streaming | Complete read (for now) | Simpler implementation; streaming will come later |
| **Content Types** | Hard-coded vs. External library | Hard-coded common types | Simpler implementation without dependencies |

### Implementation Steps

1. Create a dedicated StaticFileHandler struct
2. Implement path normalization for security
3. Add file reading functionality
4. Determine content types from file extensions
5. Generate appropriate HTTP responses

### Creating the File Handler

Let's create a new module for handling static files. Create the file `src/server/static_handler.rs`:

```rust
use std::fs::File;
use std::io::{self, Read};
use std::path::{Path, PathBuf};
use crate::http::{StatusCode, response::Response};

/// Handles serving static files from the filesystem
pub struct StaticFileHandler {
    /// Base directory from which to serve files
    root_dir: PathBuf,
}

impl StaticFileHandler {
    /// Creates a new static file handler
    pub fn new<P: AsRef<Path>>(root_dir: P) -> Self {
        StaticFileHandler {
            root_dir: PathBuf::from(root_dir.as_ref()),
        }
    }
    
    /// Serves a file based on the request path
    pub fn serve(&self, path: &str) -> Response {
        // Normalize the path to prevent directory traversal attacks
        let normalized_path = self.normalize_path(path);
        
        // Combine with the root directory
        let file_path = self.root_dir.join(normalized_path);
        
        // Try to open and serve the file
        match self.read_file(&file_path) {
            Ok((content, content_type)) => {
                Response::new()
                    .with_status(StatusCode::Ok)
                    .with_content_type(&content_type)
                    .with_body(content)
            },
            Err(e) => {
                println!("Error serving file: {}", e);
                Response::new()
                    .with_status(StatusCode::NotFound)
                    .with_text("404 Not Found")
            }
        }
    }
    
    /// Normalizes a path to prevent directory traversal
    fn normalize_path(&self, path: &str) -> String {
        // Remove the leading slash if present
        let path = path.trim_start_matches('/');
        
        // Handle empty path or root request
        if path.is_empty() {
            return String::from("index.html");
        }
        
        // For security, ensure we don't allow path traversal
        let path = Path::new(path);
        let mut normalized = PathBuf::new();
        
        for component in path.components() {
            match component {
                std::path::Component::Normal(c) => normalized.push(c),
                _ => {} // Ignore other components like ParentDir for security
            }
        }
        
        // If the path is a directory, append index.html
        if normalized.to_string_lossy().ends_with('/') || normalized.to_string_lossy().is_empty() {
            normalized.push("index.html");
        }
        
        normalized.to_string_lossy().to_string()
    }
    
    /// Reads a file and determines its content type
    fn read_file(&self, path: &Path) -> io::Result<(Vec<u8>, String)> {
        let mut file = File::open(path)?;
        let mut content = Vec::new();
        file.read_to_end(&mut content)?;
        
        // Determine content type based on file extension
        let content_type = self.get_content_type(path);
        
        Ok((content, content_type))
    }
    
    /// Determines content type based on file extension
    fn get_content_type(&self, path: &Path) -> String {
        let extension = path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");
            
        match extension.to_lowercase().as_str() {
            "html" | "htm" => String::from("text/html"),
            "css" => String::from("text/css"),
            "js" => String::from("application/javascript"),
            "jpg" | "jpeg" => String::from("image/jpeg"),
            "png" => String::from("image/png"),
            "gif" => String::from("image/gif"),
            "svg" => String::from("image/svg+xml"),
            "json" => String::from("application/json"),
            "txt" => String::from("text/plain"),
            _ => String::from("application/octet-stream"),
        }
    }
}
```

### Key Design Decisions Explained

Our static file handling implementation involves several important design decisions that balance security, performance, and maintainability.

#### Path Normalization and Security

Security is paramount when serving files from the filesystem. Improper path handling can lead to directory traversal vulnerabilities, one of the most common web server security issues.

| Approach | Implementation | Advantages | Disadvantages | Security Impact |
|----------|---------------|------------|--------------|----------------|
| **Custom Path Normalization** (our choice) | Filter path components | Full control over security rules | Complex to implement correctly | High if done correctly |
| **Path Cleaning Library** | Use `path_clean` or similar | Proven implementation | Additional dependency | Depends on library quality |
| **Canonical Paths** | Use `std::fs::canonicalize` | Built-in Rust function | Can fail on nonexistent paths | Good but needs additional checks |
| **Explicit Whitelisting** | Only serve predefined files | Maximum security | Very inflexible | Very high |

**Our Implementation Highlights:**
```rust
// Remove the leading slash if present
let path = path.trim_start_matches('/');

// For security, ensure we don't allow path traversal
let path = Path::new(path);
let mut normalized = PathBuf::new();

for component in path.components() {
    match component {
        std::path::Component::Normal(c) => normalized.push(c),
        _ => {} // Ignore other components like ParentDir for security
    }
}
```

This approach carefully handles each path component, rejecting anything that could lead to directory traversal.

#### Content Type Detection

The content type (MIME type) tells the browser how to interpret the file, affecting rendering, security context, and behavior.

| Approach | Implementation | Advantages | Disadvantages | Best Use Case |
|----------|---------------|------------|--------------|--------------|
| **Extension Mapping** (our choice) | Match on file extension | Simple, no dependencies | Limited file type support | Basic web server |
| **MIME Type Library** | Use `mime_guess` or similar | Comprehensive coverage | Additional dependency | Production server |
| **OS Integration** | Use OS file type detection | System-consistent behavior | Platform-dependent | Desktop applications |
| **Content Analysis** | Examine file content | Works without extensions | CPU intensive | Specialized applications |

**Our Implementation:**
```rust
match extension.to_lowercase().as_str() {
    "html" | "htm" => String::from("text/html"),
    "css" => String::from("text/css"),
    "js" => String::from("application/javascript"),
    // Other common types...
    _ => String::from("application/octet-stream"), // Default fallback
}
```

We cover the most common web file types while providing a safe default for unknown types.

#### File Reading Strategy

How we read files affects memory usage, performance, and scalability of our server.

| Strategy | Implementation | Memory Usage | Performance | Scalability |
|----------|---------------|-------------|------------|-------------|
| **Complete Read** (our choice) | Read entire file at once | High for large files | Fast for small files | Poor for many large files |
| **Streaming Chunks** | Read and write in chunks | Low, constant | Consistent | Good |
| **Memory Mapping** | Memory map the file | Virtual memory only | Very fast for large files | Good with proper limits |
| **Sendfile** | Use OS sendfile syscall | Minimal | Optimal | Excellent |

**Our Implementation:**
```rust
let mut file = File::open(path)?;
let mut content = Vec::new();
file.read_to_end(&mut content)?;
```

We've chosen the simplest approach for this module, reading entire files into memory. This works well for small files but will be enhanced in future modules for better handling of large files.

## Updating the Server Module

Now, let's update the server module to use our static file handler. First, modify `src/server/mod.rs` to include the new module:

```rust
mod static_handler;
```

Next, let's update the `Server` struct and its implementation:

```rust
use std::net::{TcpListener, TcpStream};
use std::io;
use crate::http::{self, StatusCode};
use crate::config::ServerConfig;
use self::static_handler::StaticFileHandler;

/// A basic HTTP server
pub struct Server {
    /// The address to listen on (ip:port)
    address: String,
    /// Handler for static files
    static_handler: StaticFileHandler,
}

impl Server {
    /// Create a new server instance with the given config
    pub fn new(config: &ServerConfig) -> Self {
        Server {
            address: config.address(),
            static_handler: StaticFileHandler::new(&config.doc_root),
        }
    }

    // ... existing code ...
}
```

Finally, let's modify the `handle_connection` method to use the static file handler:

```rust
fn handle_connection(&self, mut stream: TcpStream) -> io::Result<()> {
    // Get peer address for logging
    let peer_addr = stream.peer_addr()?;
    println!("Connection established from: {}", peer_addr);
    
    // Parse the HTTP request from the stream
    let request = match http::request::Request::from_stream(&mut stream) {
        Ok(req) => req,
        Err(e) => {
            eprintln!("Error parsing request: {}", e);
            
            // Send a 400 Bad Request response
            let response = http::response::Response::new()
                .with_status(StatusCode::BadRequest)
                .with_text("400 Bad Request");
                
            response.write_to(&mut stream)?;
            return Ok(());
        }
    };
    
    println!("Received {} request for {}", request.method, request.path);
    
    // Handle the request based on the method
    let response = match request.method {
        http::Method::GET | http::Method::HEAD => {
            // Serve static file for GET and HEAD requests
            self.static_handler.serve(&request.path)
        },
        _ => {
            // Return 405 Method Not Allowed for other methods
            http::response::Response::new()
                .with_status(StatusCode::MethodNotAllowed)
                .with_header("Allow", "GET, HEAD")
                .with_text("405 Method Not Allowed")
        }
    };
    
    // Send the response
    response.write_to(&mut stream)?;
    
    println!("Response sent to {}", peer_addr);
    
    Ok(())
}
```

## Creating Sample Static Content

Let's create some sample static content in the `static` directory:

### index.html

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rusty Server</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Welcome to Rusty Server</h1>
        <p>This is a simple web server built with Rust.</p>
        <p>It can serve static files like HTML, CSS, and images.</p>
    </div>
</body>
</html>
```

### styles.css

```css
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
}

.container {
    width: 80%;
    margin: 30px auto;
    padding: 20px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1 {
    color: #0066cc;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}
```

## Updating the Main Function

Now, let's update the `src/main.rs` file to use our configuration:

```rust
mod server;
mod http;
mod config;

use config::ServerConfig;

fn main() {
    println!("Starting Rusty Server...");
    
    // Create server configuration
    let config = ServerConfig::new();
    println!("Server configured to listen on {}", config.address());
    println!("Serving files from {}", config.doc_root);
    
    // Create and run the server
    let server = server::Server::new(&config);
    
    if let Err(e) = server.run() {
        eprintln!("Server error: {}", e);
    }
}
```

## Testing Static File Serving

Testing is crucial for ensuring our static file serving implementation works correctly and securely. We'll implement both unit tests for the file handler and integration tests to validate the complete HTTP flow.

### Test Strategy

Our tests should verify several key aspects:

1. **Basic Functionality**: Serving existing files with correct content and headers
2. **Error Handling**: Appropriate 404 responses for missing files
3. **Security**: Prevention of directory traversal attacks
4. **Content Types**: Correct MIME type detection for various file extensions
5. **Edge Cases**: Handling empty files, large files, and unusual filenames

### Unit Testing the Static File Handler

Let's add comprehensive tests for our static file handler in `src/server/static_handler.rs`:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::io::Write;
    use tempfile::TempDir;

    #[test]
    fn test_serve_existing_file() {
        // Create a temporary directory for testing
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();
        
        // Create a test HTML file
        let file_path = temp_path.join("test.html");
        let mut file = File::create(&file_path).unwrap();
        write!(file, "<html><body>Test Content</body></html>").unwrap();
        
        // Create the handler with our temp directory as root
        let handler = StaticFileHandler::new(temp_path);
        
        // Serve the file
        let response = handler.serve("/test.html");
        
        // Check the response
        assert_eq!(response.status, StatusCode::Ok);
        assert_eq!(
            String::from_utf8_lossy(&response.body),
            "<html><body>Test Content</body></html>"
        );
        assert_eq!(
            response.headers.get("Content-Type").unwrap(),
            "text/html"
        );
    }
    
    #[test]
    fn test_serve_nonexistent_file() {
        // Create a temporary directory for testing
        let temp_dir = TempDir::new().unwrap();
        
        // Create the handler with our temp directory as root
        let handler = StaticFileHandler::new(temp_dir.path());
        
        // Try to serve a file that doesn't exist
        let response = handler.serve("/nonexistent.html");
        
        // Check that we get a 404 response
        assert_eq!(response.status, StatusCode::NotFound);
    }
    
    #[test]
    fn test_prevent_directory_traversal() {
        // Create a temporary directory structure for testing
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();
        
        // Create a test file outside the root directory
        let outside_dir = temp_path.join("outside");
        fs::create_dir(&outside_dir).unwrap();
        let outside_file = outside_dir.join("secret.txt");
        let mut file = File::create(outside_file).unwrap();
        write!(file, "Secret content").unwrap();
        
        // Create the handler with our temp directory as root
        let handler = StaticFileHandler::new(temp_path);
        
        // Try to access the file with a path traversal attack
        let response = handler.serve("/../outside/secret.txt");
        
        // Should get a 404 because the path traversal is prevented
        assert_eq!(response.status, StatusCode::NotFound);
    }
}
```

## Running and Testing the Enhanced Server

### Adding Dependencies

Update `Cargo.toml` to include the new testing dependency:

```toml
[dev-dependencies]
tempfile = "3.4"
```

### Building and Running

Build and run the server with:

```bash
cargo run
```

The server should start with output similar to:
```
Starting Rusty Server...
Server configured to listen on 127.0.0.1:8080
Serving files from ./static
Server listening on 127.0.0.1:8080
```

### Manual Testing

To verify that static file serving is working correctly:

1. **Browser Testing**:
   - Open your web browser and navigate to `http://localhost:8080/`
   - You should see the index page with proper styling
   - Try accessing various files like `http://localhost:8080/styles.css`

2. **Command Line Testing**:
   ```bash
   # Test basic HTML file
   curl -v http://localhost:8080/
   
   # Test CSS file and check content type header
   curl -I http://localhost:8080/styles.css
   
   # Test 404 for nonexistent file
   curl -v http://localhost:8080/not-found.html
   
   # Test security (should return 404, not expose files outside root)
   curl -v http://localhost:8080/../../../etc/passwd
   ```

### Troubleshooting Common Issues

If you encounter problems:

1. **404 Not Found for all files**:
   - Check that your document root configuration is correct
   - Verify file permissions allow the server to read the files

2. **Incorrect Content Types**:
   - Verify file extensions match the expected values in `get_content_type`

3. **Path Traversal Vulnerabilities**:
   - Run security tests to verify the path normalization is working

## Summary

In this module, we've implemented static file serving in our web server, a foundational capability that transforms it from a basic TCP/HTTP server into a functional web server. We've:

1. Created a dedicated `StaticFileHandler` to manage file operations
2. Implemented secure path normalization to prevent directory traversal attacks
3. Added content type detection based on file extensions
4. Integrated file serving with our HTTP response system
5. Written comprehensive tests to validate functionality and security
6. Created sample static content for demonstration

Our server can now serve websites comprising HTML, CSS, JavaScript, images, and other static assets, making it genuinely useful for web development and hosting.

## Knowledge Check

1. **Which of the following is the primary security concern when implementing static file serving?**
   - A) Cross-site scripting (XSS)
   - B) SQL injection
   - C) Directory traversal attacks
   - D) Cross-site request forgery (CSRF)

2. **What's the purpose of content type detection in static file serving?**
   - A) To optimize file storage on disk
   - B) To tell browsers how to interpret and render content
   - C) To encrypt sensitive files
   - D) To compress files for faster transmission

3. **In our implementation, how do we prevent directory traversal attacks?**
   - A) By using regex to filter out dangerous characters
   - B) By filtering path components and only accepting normal components
   - C) By checking file permissions
   - D) By restricting file access to a specific user

4. **What does the HTTP status code 404 indicate when serving static files?**
   - A) The file exists but cannot be accessed due to permissions
   - B) The file doesn't exist at the requested path
   - C) The server encountered an error while reading the file
   - D) The file is too large to serve

5. **Which design decision affects memory usage most significantly in our current implementation?**
   - A) Using match statements for content type detection
   - B) Normalizing paths for each request
   - C) Reading entire files into memory
   - D) Using a dedicated struct for the file handler

<details>
<summary>Click to see answers</summary>

1. C) Directory traversal attacks
2. B) To tell browsers how to interpret and render content
3. B) By filtering path components and only accepting normal components
4. B) The file doesn't exist at the requested path
5. C) Reading entire files into memory
</details>

## Additional Resources

### Web Server File Handling
- [MDN Web Docs: MIME types](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types) - Comprehensive guide to content types
- [OWASP Path Traversal](https://owasp.org/www-community/attacks/Path_Traversal) - Security guide for preventing directory traversal attacks
- [HTTP 1.1 Range Requests (RFC 7233)](https://datatracker.ietf.org/doc/html/rfc7233) - Advanced topic for serving partial content

### Rust File System APIs
- [Rust std::fs Documentation](https://doc.rust-lang.org/std/fs/index.html) - Official documentation for filesystem operations
- [Rust Path Manipulation](https://doc.rust-lang.org/std/path/index.html) - Working with file paths in Rust
- [mime_guess crate](https://docs.rs/mime_guess/latest/mime_guess/) - More comprehensive MIME type detection

### Performance Optimization
- [Zero-copy Data Transfer](https://docs.rs/tokio/latest/tokio/io/trait.AsyncReadExt.html#method.copy_to) - Advanced technique for efficient file serving (future module)
- [Sendfile syscall](https://man7.org/linux/man-pages/man2/sendfile.2.html) - Low-level optimization for file transmission

### Tools for Testing and Debugging
- [curl tutorial](https://curl.se/docs/manual.html) - Detailed guide for testing HTTP servers
- [Wireshark HTTP Analysis](https://wiki.wireshark.org/HTTP) - For examining HTTP traffic
- [OWASP ZAP](https://www.zaproxy.org/) - Security testing tool that can identify directory traversal vulnerabilities

## Next Steps

While our server can now serve static files, there are several limitations to address in upcoming modules:

1. **Performance**: The server processes only one request at a time and reads entire files into memory
2. **Configuration**: There's no advanced configuration system for customizing behavior
3. **Error Handling**: The current approach is basic and lacks comprehensive error reporting
4. **Logging**: Output is limited to console with minimal structure

In the next module, we'll build a robust configuration system that allows customizing the server's behavior without recompiling the code.

## Navigation
- [Previous: HTTP Protocol](03-http-protocol.md)
- [Next: Configuration System](05-configuration-system.md)
