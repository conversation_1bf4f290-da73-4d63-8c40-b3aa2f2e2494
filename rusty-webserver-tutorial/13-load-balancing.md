<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\13-load-balancing.md -->
# Advanced Load Balancing

## Navigation
- [Previous: Security Features](12-security-features.md)
- [Next: Benchmarking & Performance](14-benchmarking-performance.md)

This tutorial covers implementing sophisticated load balancing strategies while introducing advanced Rust concepts like trait objects, generic associated types, and async traits.

## Web Server Concepts

- **Load Balancing Strategies**
  - Round-robin
  - Least connections
  - Weighted distribution
  - IP hash-based routing
  - Dynamic health checks

## Rust Concepts Introduced

### 1. Generic Associated Types (GATs)
```rust
trait HealthCheck {
    type Future<'a>: Future<Output = bool> + 'a;
    fn check_health(&self) -> Self::Future<'_>;
}
```

### 2. Async Traits
```rust
#[async_trait]
trait LoadBalancer {
    async fn select_backend(&self, request: &Request) -> Result<Backend>;
    async fn update_stats(&self, backend: &Backend, response_time: Duration);
}
```

### 3. Type-<PERSON> Pattern
```rust
struct LoadBalancer<S: State> {
    backends: Vec<Backend>,
    state: PhantomData<S>,
}

// States
struct Uninitialized;
struct Initialized;
struct Running;
```

## Implementation Overview

```mermaid
flowchart TD
    subgraph LoadBalancer[Load Balancer]
        S[Strategy]
        H[Health Checker]
        M[Metrics]
    end
    
    subgraph Backends[Backend Servers]
        B1[Backend 1]
        B2[Backend 2]
        B3[Backend 3]
    end

    Client((Client)) --> LoadBalancer
    S --> B1
    S --> B2
    S --> B3
    H --> B1
    H --> B2
    H --> B3
    B1 --> M
    B2 --> M
    B3 --> M
```

## Step-by-Step Implementation

### 1. Define Core Types

```rust
/// A backend server in the load balancer pool
pub struct Backend {
    uri: Uri,
    health: AtomicBool,
    active_connections: AtomicUsize,
    total_requests: AtomicUsize,
    average_response_time: AtomicU64,
    weight: u32,
}

/// Statistics for load balancing decisions
pub struct BackendStats {
    active_connections: usize,
    total_requests: usize,
    average_response_time: Duration,
    last_health_check: DateTime<Utc>,
    healthy: bool,
}
```

### 2. Health Checking System

```rust
#[async_trait]
pub trait HealthCheck {
    async fn check_health(&self, backend: &Backend) -> bool;
    async fn schedule_checks(&self, interval: Duration);
}

pub struct HttpHealthCheck {
    client: Client<HttpsConnector<HttpConnector>>,
    path: String,
    expected_status: StatusCode,
}
```

### 3. Load Balancing Strategies

```rust
pub trait Strategy: Send + Sync {
    fn select_backend(&self, context: &RequestContext) -> Option<&Backend>;
    fn update_stats(&self, backend: &Backend, stats: BackendStats);
}

// Round Robin Implementation
pub struct RoundRobin {
    backends: Arc<Vec<Backend>>,
    next: AtomicUsize,
}

// Least Connections Implementation
pub struct LeastConnections {
    backends: Arc<Vec<Backend>>,
}

// IP Hash Implementation
pub struct IpHash {
    backends: Arc<Vec<Backend>>,
    hasher: XxHash64,
}
```

### 4. Dynamic Strategy Selection

```rust
pub enum BalancingStrategy {
    RoundRobin(RoundRobin),
    LeastConnections(LeastConnections),
    IpHash(IpHash),
}

impl BalancingStrategy {
    pub fn from_config(config: &Config) -> Self {
        match config.strategy.as_str() {
            "round-robin" => BalancingStrategy::RoundRobin(RoundRobin::new()),
            "least-conn" => BalancingStrategy::LeastConnections(LeastConnections::new()),
            "ip-hash" => BalancingStrategy::IpHash(IpHash::new()),
            _ => BalancingStrategy::RoundRobin(RoundRobin::new()),
        }
    }
}
```

## Advanced Rust Concepts Explained

### 1. Generic Associated Types (GATs)
GATs are a powerful feature that allows you to define associated types that can be generic over lifetimes. This is particularly useful for async traits and futures:

```rust
trait AsyncProcessor {
    type Output<'a>: Future<Output = Result<(), Error>> + 'a
    where
        Self: 'a;
    
    fn process<'a>(&'a self, input: &'a str) -> Self::Output<'a>;
}
```

Key points:
- GATs enable generic lifetimes in associated types
- Essential for async trait definitions
- Allows for more flexible API design

### 2. Type State Pattern
Using Rust's type system to enforce state transitions at compile time:

```rust
impl LoadBalancer<Uninitialized> {
    pub fn new() -> Self { /* ... */ }
    pub fn initialize(self, config: Config) -> LoadBalancer<Initialized> { /* ... */ }
}

impl LoadBalancer<Initialized> {
    pub fn start(self) -> LoadBalancer<Running> { /* ... */ }
}

impl LoadBalancer<Running> {
    pub async fn process_request(&self, request: Request) -> Result<Response> { /* ... */ }
}
```

Benefits:
- Compile-time state validation
- Impossible states become unrepresentable
- Self-documenting API

### 3. Atomic Operations
Using atomic types for thread-safe counters without locks:

```rust
impl Backend {
    pub fn increment_connections(&self) -> usize {
        self.active_connections.fetch_add(1, Ordering::SeqCst)
    }

    pub fn update_response_time(&self, duration: Duration) {
        let nanos = duration.as_nanos() as u64;
        self.average_response_time.fetch_update(
            Ordering::SeqCst,
            Ordering::SeqCst,
            |current| {
                Some((current + nanos) / 2)
            }
        ).unwrap_or_default();
    }
}
```

Key concepts:
- Lock-free synchronization
- Memory ordering guarantees
- Atomic operations vs. mutexes

## Testing Load Balancer Implementations

### 1. Unit Tests with Advanced Rust Features

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_round_robin_distribution() {
        let lb = RoundRobin::new(vec![
            Backend::new("http://backend1"),
            Backend::new("http://backend2"),
            Backend::new("http://backend3"),
        ]);
        
        let selections: Vec<_> = (0..6)
            .map(|_| lb.select_backend(&RequestContext::default()))
            .collect();
            
        // Verify round-robin pattern
        assert_eq!(selections[0].uri(), "http://backend1");
        assert_eq!(selections[1].uri(), "http://backend2");
        assert_eq!(selections[2].uri(), "http://backend3");
        assert_eq!(selections[3].uri(), "http://backend1");
    }
}
```

### 2. Integration Tests with Async Runtime

```rust
#[tokio::test]
async fn test_load_balancer_failover() {
    // Start mock backends
    let backend1 = MockBackend::new().with_delay(Duration::from_millis(100));
    let backend2 = MockBackend::new().with_delay(Duration::from_millis(50));
    
    let lb = LoadBalancer::new()
        .with_strategy(LeastConnections::new())
        .with_backends(vec![backend1, backend2]);
        
    // Simulate requests and verify distribution
    let results = execute_test_requests(lb, 100).await;
    assert_distribution(&results);
}
```

## Performance Considerations

1. **Lock-Free Operations**
   - Use atomic operations for counters
   - Minimize contention points
   - Efficient concurrent data structures

2. **Health Check Optimization**
   - Asynchronous health checks
   - Configurable check intervals
   - Circuit breaker pattern

3. **Memory Efficiency**
   - Zero-copy request forwarding
   - Pooled connections
   - Efficient statistics tracking

## Security Considerations

When implementing load balancing in your web server, several security considerations must be addressed to prevent attackers from exploiting your load balancing system.

```mermaid
flowchart TD
    A[Load Balancer Security Risks] --> B[Backend Impersonation]
    A --> C[Backend Poisoning]
    A --> D[Information Disclosure]
    A --> E[Session Hijacking]
    A --> F[DoS Amplification]
    A --> G[Health Check Abuse]
    A --> H[Backend Enumeration]
    A --> I[Traffic Manipulation]
    
    B --> B1[TLS Verification Bypass]
    
    C --> C1[Malicious Backend Insertion]
    
    D --> D1[Backend Information Leaks]
    D --> D2[Header Leakage]
    
    E --> E1[Session Affinity Attacks]
    
    F --> F1[Request Multiplication]
    
    G --> G1[Health Check Forgery]
    
    H --> H1[Backend Discovery]
    
    I --> I1[Traffic Hijacking]
```

### 1. Secure Backend Authentication

Implement mutual TLS authentication between the load balancer and backends:

```rust
/// TLS configuration for secure backend connections
pub struct BackendTlsConfig {
    /// Root certificate authority
    ca_cert: Vec<Certificate>,
    /// Client certificates for mutual TLS
    client_certs: Vec<Certificate>,
    /// Client private key
    client_key: PrivateKey,
    /// Whether to verify backend certificates
    verify_certs: bool,
    /// Allowed cipher suites
    cipher_suites: Vec<CipherSuite>,
    /// Minimum TLS version
    min_tls_version: TlsVersion,
}

/// Configure secure client for backend connections
fn create_secure_backend_client(config: &BackendTlsConfig) -> Result<Client> {
    let mut client_config = ClientConfig::builder()
        .with_safe_defaults()
        .with_root_certificates(RootCertStore::from_certificates(config.ca_cert.clone()))
        .with_client_auth_cert(config.client_certs.clone(), config.client_key.clone())?;

    // Set minimum TLS version
    client_config.versions = match config.min_tls_version {
        TlsVersion::Tls12 => vec![&TLS12],
        TlsVersion::Tls13 => vec![&TLS13],
        _ => return Err(Error::new("Unsupported TLS version")),
    };
    
    // Set allowed cipher suites
    if !config.cipher_suites.is_empty() {
        client_config.ciphersuites = config.cipher_suites.clone();
    }
    
    // Create HTTPS connector with our custom configuration
    let https = HttpsConnector::from((client_config.into(), HttpConnector::new()));
    
    // Build the client
    Ok(Client::builder().build(https))
}
```

### 2. Backend Validation and Verification

Implement secure backend verification mechanisms:

```rust
/// Backend verification configuration
pub struct BackendVerificationConfig {
    /// Expected backend certificates (fingerprints)
    expected_cert_fingerprints: HashMap<String, String>,
    /// Expected backend public keys
    expected_public_keys: HashMap<String, String>,
    /// Backend IP allowlist
    allowed_backend_ips: Vec<IpNetwork>,
    /// Backend hostname verification
    verify_hostname: bool,
}

/// Verify backend identity
pub fn verify_backend(
    backend: &Backend,
    response: &Response<Body>, 
    config: &BackendVerificationConfig
) -> Result<bool> {
    // Extract TLS connection information
    if let Some(tls_info) = response.extensions().get::<TlsInfo>() {
        // Verify certificate fingerprint
        if !config.expected_cert_fingerprints.is_empty() {
            let cert_fingerprint = calculate_cert_fingerprint(&tls_info.peer_certificate()?);
            
            if let Some(expected) = config.expected_cert_fingerprints.get(&backend.id) {
                if cert_fingerprint != *expected {
                    log::warn!(
                        "Backend {} certificate fingerprint mismatch: expected {}, got {}",
                        backend.id, expected, cert_fingerprint
                    );
                    return Ok(false);
                }
            }
        }
        
        // Extract and verify backend IP
        if let Some(remote_addr) = response.extensions().get::<ConnectInfo<SocketAddr>>() {
            let ip = remote_addr.ip();
            
            if !config.allowed_backend_ips.iter().any(|net| net.contains(ip)) {
                log::warn!("Backend IP {} not in allowlist", ip);
                return Ok(false);
            }
        }
    }
    
    // Additional verification checks
    // Verify response headers match expected backend signature
    if let Some(server_id) = response.headers().get("X-Server-ID") {
        if let Some(expected) = backend.expected_headers.get("X-Server-ID") {
            if server_id != expected {
                log::warn!("Server ID mismatch: expected {}, got {}", expected, server_id);
                return Ok(false);
            }
        }
    }
    
    Ok(true)
}

/// Calculate fingerprint from certificate
fn calculate_cert_fingerprint(cert_der: &[u8]) -> String {
    let mut hasher = Sha256::new();
    hasher.update(cert_der);
    let result = hasher.finalize();
    
    // Convert to colon-separated hex string
    result.iter()
        .map(|b| format!("{:02x}", b))
        .collect::<Vec<_>>()
        .join(":")
}
```

### 3. Secure Health Checks

Implement secure health check mechanisms:

```rust
/// Health check security configuration
pub struct HealthCheckSecurityConfig {
    /// Authentication token for health checks
    auth_token: Option<String>,
    /// Use HTTPS for health checks
    use_https: bool,
    /// Custom headers for health checks
    headers: HashMap<String, String>,
    /// Allowed response patterns (regex)
    allowed_response_patterns: Vec<Regex>,
    /// Disallowed response patterns (regex)
    disallowed_response_patterns: Vec<Regex>,
}

/// Perform secure health check
pub async fn secure_health_check(
    backend: &Backend,
    config: &HealthCheckSecurityConfig
) -> Result<bool> {
    // Build health check URL
    let scheme = if config.use_https { "https" } else { "http" };
    let url = format!("{}://{}:{}{}", 
        scheme, 
        backend.host, 
        backend.port, 
        backend.health_check_path.as_deref().unwrap_or("/health")
    );
    
    // Create request with security headers
    let mut request_builder = Request::builder()
        .method(Method::GET)
        .uri(url.clone());
    
    // Add custom headers including authentication
    for (key, value) in &config.headers {
        request_builder = request_builder.header(key, value);
    }
    
    // Add auth token if configured
    if let Some(token) = &config.auth_token {
        request_builder = request_builder.header("Authorization", format!("Bearer {}", token));
    }
    
    // Add unique nonce to prevent caching
    let nonce = format!("{:x}", rand::random::<u64>());
    request_builder = request_builder.header("X-Health-Check-Nonce", &nonce);
    
    let request = request_builder.body(Body::empty())?;
    
    // Send request with timeout
    let client = hyper_util::client::legacy::Client::new();
    let response = client.request(request).await?;
    
    // Validate response
    let status = response.status();
    let is_success = status.is_success();
    
    // Read and validate response body if needed
    if is_success && (!config.allowed_response_patterns.is_empty() || !config.disallowed_response_patterns.is_empty()) {
        let body_bytes = hyper::body::to_bytes(response.into_body()).await?;
        let body_text = String::from_utf8_lossy(&body_bytes);
        
        // Check for required patterns
        for pattern in &config.allowed_response_patterns {
            if !pattern.is_match(&body_text) {
                log::warn!("Health check for {} failed: required pattern not found", url);
                return Ok(false);
            }
        }
        
        // Check for disallowed patterns
        for pattern in &config.disallowed_response_patterns {
            if pattern.is_match(&body_text) {
                log::warn!("Health check for {} failed: disallowed pattern found", url);
                return Ok(false);
            }
        }
    }
    
    // Log health check result
    let result_str = if is_success { "passed" } else { "failed" };
    log::debug!("Health check for {} {}: status {}", url, result_str, status);
    
    Ok(is_success)
}
```

### 4. Load Balancer Hardening

Implement secure load balancer configuration:

```rust
/// Load balancer security configuration
pub struct LoadBalancerSecurityConfig {
    /// Validate backend response headers
    validate_response_headers: bool,
    /// Strip sensitive headers from backend requests
    strip_sensitive_headers: bool,
    /// Add security headers to responses
    add_security_headers: bool,
    /// Hide backend information
    hide_backend_info: bool,
    /// Backend connection timeout
    connection_timeout: Duration,
    /// Validate backend hostnames
    validate_hostnames: bool,
}

/// Process incoming request securely before load balancing
fn secure_request_processing(
    request: &mut Request,
    config: &LoadBalancerSecurityConfig
) -> Result<()> {
    // Remove sensitive headers if configured
    if config.strip_sensitive_headers {
        let sensitive_headers = [
            "X-Forwarded-For", "X-Real-IP", "X-Forwarded-Host",
            "X-Forwarded-Proto", "Forwarded", "Via"
        ];
        
        for header in sensitive_headers {
            request.headers_mut().remove(header);
        }
    }
    
    // Add secure headers
    request.headers_mut().insert(
        "X-Forwarded-Proto",
        HeaderValue::from_static("https")
    );
    
    // Add request ID for traceability
    let request_id = format!("{:x}", rand::random::<u128>());
    request.headers_mut().insert(
        "X-Request-ID",
        HeaderValue::from_str(&request_id)?
    );
    
    Ok(())
}

/// Process backend response securely
fn secure_response_processing(
    response: &mut Response,
    backend: &Backend,
    config: &LoadBalancerSecurityConfig
) -> Result<()> {
    // Remove backend-identifying headers if configured
    if config.hide_backend_info {
        response.headers_mut().remove("Server");
        response.headers_mut().remove("X-Powered-By");
    }
    
    // Add security headers if configured
    if config.add_security_headers {
        add_default_security_headers(response.headers_mut());
    }
    
    // Validate response headers if configured
    if config.validate_response_headers {
        // Check for suspicious headers
        let suspicious_headers = ["X-Debug-Info", "X-Runtime", "X-Backend-Server"];
        
        for header in suspicious_headers {
            if response.headers().contains_key(header) {
                log::warn!(
                    "Backend {} returned suspicious header: {}",
                    backend.id, header
                );
                response.headers_mut().remove(header);
            }
        }
    }
    
    Ok(())
}

/// Add default security headers
fn add_default_security_headers(headers: &mut HeaderMap) {
    headers.insert("X-Content-Type-Options", HeaderValue::from_static("nosniff"));
    headers.insert("X-Frame-Options", HeaderValue::from_static("DENY"));
    headers.insert("X-XSS-Protection", HeaderValue::from_static("1; mode=block"));
    headers.insert("Referrer-Policy", HeaderValue::from_static("strict-origin-when-cross-origin"));
}
```

### 5. Secure Session Affinity

Implement secure session affinity:

```rust
/// Session affinity security configuration
pub struct SessionAffinitySecurityConfig {
    /// Session cookie name
    cookie_name: String,
    /// Use secure cookies only
    secure_cookie: bool,
    /// Cookie encryption key
    encryption_key: [u8; 32],
    /// Cookie max age in seconds
    cookie_max_age: u64,
    /// SameSite cookie policy
    same_site: SameSite,
}

/// Session affinity methods
#[derive(Debug, Clone, PartialEq)]
pub enum AffinityMethod {
    /// Cookie-based affinity
    Cookie,
    /// IP-based affinity
    IpHash,
    /// Header-based affinity
    Header(String),
    /// None (no affinity)
    None,
}

/// Implement secure session affinity
pub fn create_secure_affinity_key(
    request: &Request,
    method: &AffinityMethod,
    config: &SessionAffinitySecurityConfig
) -> Result<Option<String>> {
    match method {
        AffinityMethod::Cookie => {
            // Extract existing cookie if present
            if let Some(cookie_header) = request.headers().get(header::COOKIE) {
                let cookie_str = cookie_header.to_str()?;
                
                // Parse cookies
                for cookie_pair in cookie_str.split(';') {
                    let trimmed = cookie_pair.trim();
                    if let Some((name, value)) = trimmed.split_once('=') {
                        if name == config.cookie_name {
                            // Decrypt and validate cookie
                            return match decrypt_and_validate_cookie(value, &config.encryption_key) {
                                Ok(backend_id) => Ok(Some(backend_id)),
                                Err(e) => {
                                    log::warn!("Invalid affinity cookie: {}", e);
                                    Ok(None)
                                }
                            };
                        }
                    }
                }
            }
            
            // No valid cookie found
            Ok(None)
        },
        AffinityMethod::IpHash => {
            // Extract client IP
            if let Some(addr) = get_client_ip(request) {
                // Hash the IP to create a stable affinity key
                let mut hasher = Sha256::new();
                hasher.update(addr.to_string().as_bytes());
                let hash = hasher.finalize();
                
                // Use first 8 bytes of hash as hex string
                let affinity_key = hex::encode(&hash[0..8]);
                Ok(Some(affinity_key))
            } else {
                Ok(None)
            }
        },
        AffinityMethod::Header(header_name) => {
            // Extract header value
            if let Some(header_val) = request.headers().get(header_name) {
                if let Ok(val_str) = header_val.to_str() {
                    // Hash the header value
                    let mut hasher = Sha256::new();
                    hasher.update(val_str.as_bytes());
                    let hash = hasher.finalize();
                    
                    // Use first 8 bytes of hash as hex string
                    let affinity_key = hex::encode(&hash[0..8]);
                    Ok(Some(affinity_key))
                } else {
                    Ok(None)
                }
            } else {
                Ok(None)
            }
        },
        AffinityMethod::None => Ok(None),
    }
}

/// Create secure affinity cookie
pub fn create_secure_affinity_cookie(
    backend_id: &str,
    config: &SessionAffinitySecurityConfig
) -> Result<String> {
    // Create cookie value with timestamp for expiration validation
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    
    let cookie_data = format!("{}:{}", backend_id, now);
    
    // Encrypt cookie value
    let encrypted = encrypt_cookie_value(&cookie_data, &config.encryption_key)?;
    
    // Build cookie with security attributes
    let mut cookie = format!(
        "{}={}; Path=/; Max-Age={}",
        config.cookie_name, encrypted, config.cookie_max_age
    );
    
    // Add security attributes
    if config.secure_cookie {
        cookie.push_str("; Secure");
    }
    
    cookie.push_str("; HttpOnly");
    
    // Add SameSite attribute
    match config.same_site {
        SameSite::Strict => cookie.push_str("; SameSite=Strict"),
        SameSite::Lax => cookie.push_str("; SameSite=Lax"),
        SameSite::None => cookie.push_str("; SameSite=None"),
    }
    
    Ok(cookie)
}
```

### 6. DDoS Protection with Smart Load Balancing

Implement DDoS protection in the load balancer:

```rust
/// DDoS protection configuration
pub struct DdosProtectionConfig {
    /// Enable DDoS protection
    enabled: bool,
    /// Rate limit thresholds
    rate_limits: HashMap<String, RateLimit>,
    /// Burst handling configuration
    burst_handling: BurstHandling,
    /// Dynamic backend protection
    dynamic_protection: bool,
}

/// Rate limit configuration
pub struct RateLimit {
    /// Maximum requests per second
    max_requests_per_second: u32,
    /// Maximum requests per minute
    max_requests_per_minute: u32,
    /// Maximum connections per IP
    max_connections_per_ip: u32,
    /// Block time after violation (seconds)
    block_time_seconds: u64,
}

/// Burst handling configuration
pub enum BurstHandling {
    /// Reject excess requests
    Reject,
    /// Queue excess requests
    Queue(usize),  // Queue size
    /// Route to dedicated backends
    RouteToDedicated(Vec<Backend>),
}

/// DDoS detection manager
pub struct DdosDetectionManager {
    /// Request counters by IP
    request_counters: DashMap<IpAddr, RateTracker>,
    /// Global request counter
    global_counter: Arc<GlobalRateTracker>,
    /// Configuration
    config: DdosProtectionConfig,
    /// Blocklist
    blocklist: DashMap<IpAddr, Instant>,
}

impl DdosDetectionManager {
    /// Process request for DDoS detection
    pub fn process_request(&self, request: &Request) -> Result<DdosAction> {
        if !self.config.enabled {
            return Ok(DdosAction::Allow);
        }
        
        // Extract client IP
        let client_ip = match get_client_ip(request) {
            Some(ip) => ip,
            None => return Ok(DdosAction::Allow),  // Can't apply IP-based protection
        };
        
        // Check if IP is blocklisted
        if let Some(entry) = self.blocklist.get(&client_ip) {
            let block_time = entry.value();
            if block_time.elapsed() < Duration::from_secs(self.config.rate_limits["default"].block_time_seconds) {
                return Ok(DdosAction::Block);
            } else {
                // Block expired, remove from blocklist
                self.blocklist.remove(&client_ip);
            }
        }
        
        // Get or create rate tracker for this IP
        let mut tracker = self.request_counters
            .entry(client_ip)
            .or_insert_with(|| RateTracker::new());
        
        // Update tracker with this request
        tracker.add_request();
        
        // Check rate limits
        let rate_limit = self.config.rate_limits.get("default").unwrap();
        
        if tracker.requests_per_second() > rate_limit.max_requests_per_second ||
           tracker.requests_per_minute() > rate_limit.max_requests_per_minute ||
           tracker.active_connections > rate_limit.max_connections_per_ip {
            
            // Add to blocklist if configured
            if rate_limit.block_time_seconds > 0 {
                self.blocklist.insert(client_ip, Instant::now());
            }
            
            // Determine action based on burst handling configuration
            match &self.config.burst_handling {
                BurstHandling::Reject => {
                    return Ok(DdosAction::Block);
                },
                BurstHandling::Queue(max_size) => {
                    if self.global_counter.queue_size() < *max_size {
                        return Ok(DdosAction::Queue);
                    } else {
                        return Ok(DdosAction::Block);
                    }
                },
                BurstHandling::RouteToDedicated(_) => {
                    return Ok(DdosAction::RouteToDedicated);
                },
            }
        }
        
        // Update global counter
        self.global_counter.add_request();
        
        // No issues detected
        Ok(DdosAction::Allow)
    }
    
    /// Get dedicated backends for burst traffic
    pub fn get_dedicated_backends(&self) -> Option<Vec<Backend>> {
        match &self.config.burst_handling {
            BurstHandling::RouteToDedicated(backends) => Some(backends.clone()),
            _ => None,
        }
    }
}

/// DDoS action to take
pub enum DdosAction {
    /// Allow the request
    Allow,
    /// Block the request
    Block,
    /// Queue the request
    Queue,
    /// Route to dedicated backend
    RouteToDedicated,
}
```

### 7. Backend Request Forgery Protection

Implement protections against backend request forgery:

```rust
/// Backend request forgery protection
pub struct RequestForgeryProtection {
    /// Allowed backend hosts
    allowed_hosts: Vec<String>,
    /// Allowed backend paths (prefix)
    allowed_paths: Vec<String>,
    /// Blocked request headers
    blocked_headers: Vec<String>,
    /// Validate Host header
    validate_host: bool,
}

/// Validate request for backend forgery protection
pub fn validate_backend_request(
    request: &Request,
    backend: &Backend,
    config: &RequestForgeryProtection
) -> Result<bool> {
    // Validate host is allowed
    if config.validate_host {
        if let Some(host) = request.headers().get(header::HOST) {
            let host_str = host.to_str()?;
            
            // Ensure host matches the expected backend host
            if !host_str.eq_ignore_ascii_case(&backend.host) && 
               !config.allowed_hosts.iter().any(|h| h.eq_ignore_ascii_case(host_str)) {
                log::warn!("Host header mismatch: {} vs {}", host_str, backend.host);
                return Ok(false);
            }
        }
    }
    
    // Validate path is allowed
    let path = request.uri().path();
    if !config.allowed_paths.iter().any(|allowed| path.starts_with(allowed)) {
        log::warn!("Path not allowed: {}", path);
        return Ok(false);
    }
    
    // Check for blocked headers
    for blocked in &config.blocked_headers {
        if request.headers().contains_key(blocked) {
            log::warn!("Request contains blocked header: {}", blocked);
            return Ok(false);
        }
    }
    
    // Additional security checks
    
    // Check for HTTP version downgrade attacks
    if request.version() < Version::HTTP_11 {
        log::warn!("HTTP version downgrade attempt detected");
        return Ok(false);
    }
    
    // Check for suspicious query parameters
    if let Some(query) = request.uri().query() {
        let suspicious_patterns = ["exec=", "select+", "union+", "insert+", "drop+", "script="];
        
        for pattern in suspicious_patterns {
            if query.contains(pattern) {
                log::warn!("Suspicious query parameter detected: {}", pattern);
                return Ok(false);
            }
        }
    }
    
    Ok(true)
}
```

### 8. Load Balancer Logging and Auditing

Implement secure logging and auditing:

```rust
/// Security audit log event
pub struct SecurityAuditEvent {
    /// Timestamp
    timestamp: SystemTime,
    /// Event type
    event_type: SecurityEventType,
    /// Client IP
    client_ip: Option<IpAddr>,
    /// Request ID
    request_id: String,
    /// Backend ID
    backend_id: Option<String>,
    /// Event details
    details: HashMap<String, String>,
    /// Severity
    severity: SecurityEventSeverity,
}

/// Security event types
pub enum SecurityEventType {
    /// Backend connection attempt
    BackendConnection,
    /// Backend verification
    BackendVerification,
    /// Backend selection
    BackendSelection,
    /// Rate limiting
    RateLimiting,
    /// Request forgery attempt
    RequestForgery,
    /// Backend failure
    BackendFailure,
}

/// Security event severity
pub enum SecurityEventSeverity {
    /// Informational event
    Info,
    /// Warning event
    Warning,
    /// Error event
    Error,
    /// Critical event
    Critical,
}

/// Log security audit event
pub fn log_security_audit(event: SecurityAuditEvent, config: &AuditConfig) -> Result<()> {
    // Convert to structured log format
    let mut log_entry = HashMap::new();
    
    // Add basic fields
    log_entry.insert("timestamp", format!("{:?}", event.timestamp));
    log_entry.insert("event_type", format!("{:?}", event.event_type));
    log_entry.insert("severity", format!("{:?}", event.severity));
    log_entry.insert("request_id", event.request_id.clone());
    
    // Add optional fields
    if let Some(ip) = event.client_ip {
        log_entry.insert("client_ip", ip.to_string());
    }
    
    if let Some(backend) = &event.backend_id {
        log_entry.insert("backend_id", backend.clone());
    }
    
    // Add all details
    for (key, value) in event.details {
        log_entry.insert(key, value);
    }
    
    // Serialize to JSON
    let json = serde_json::to_string(&log_entry)?;
    
    // Log based on configuration
    match config.log_destination {
        LogDestination::File(path) => {
            // Append to file
            let mut file = OpenOptions::new()
                .create(true)
                .append(true)
                .open(path)?;
                
            file.write_all(json.as_bytes())?;
            file.write_all(b"\n")?;
        },
        LogDestination::Syslog => {
            // Send to syslog
            let severity = match event.severity {
                SecurityEventSeverity::Info => syslog::Severity::LOG_INFO,
                SecurityEventSeverity::Warning => syslog::Severity::LOG_WARNING,
                SecurityEventSeverity::Error => syslog::Severity::LOG_ERR,
                SecurityEventSeverity::Critical => syslog::Severity::LOG_CRIT,
            };
            
            let logger = syslog::unix(syslog::Facility::LOG_DAEMON)?;
            logger.send(severity, json)?;
        },
        LogDestination::Console => {
            // Print to console with appropriate color/level
            match event.severity {
                SecurityEventSeverity::Info => log::info!("{}", json),
                SecurityEventSeverity::Warning => log::warn!("{}", json),
                SecurityEventSeverity::Error => log::error!("{}", json),
                SecurityEventSeverity::Critical => log::error!("CRITICAL: {}", json),
            }
        },
    }
    
    Ok(())
}
```

### 9. Network Segmentation and Isolation

Implement network segmentation for load balancing:

```rust
/// Network segmentation configuration
pub struct NetworkSegmentConfig {
    /// Backend network segments
    segments: HashMap<String, NetworkSegment>,
    /// Default segment
    default_segment: String,
}

/// Network segment definition
pub struct NetworkSegment {
    /// Segment name
    name: String,
    /// Backends in this segment
    backends: Vec<Backend>,
    /// Allowed client subnets
    allowed_clients: Vec<IpNetwork>,
    /// Traffic rules
    traffic_rules: Vec<TrafficRule>,
}

/// Traffic routing rule
pub struct TrafficRule {
    /// Rule name
    name: String,
    /// Path pattern
    path_pattern: Option<String>,
    /// Header matches
    header_matches: HashMap<String, String>,
    /// Client IP matches
    client_ip_matches: Vec<IpNetwork>,
    /// Target backend IDs
    target_backends: Vec<String>,
}

/// Route request to appropriate network segment
pub fn route_to_segment(
    request: &Request,
    config: &NetworkSegmentConfig
) -> Result<String> {
    // Extract client IP
    let client_ip = match get_client_ip(request) {
        Some(ip) => ip,
        None => return Ok(config.default_segment.clone()),
    };
    
    // Find matching segment based on client IP
    for (segment_name, segment) in &config.segments {
        // Check if client is allowed in this segment
        if segment.allowed_clients.iter().any(|net| net.contains(client_ip)) {
            // Check traffic rules for more specific routing
            for rule in &segment.traffic_rules {
                if matches_traffic_rule(request, client_ip, rule) {
                    return Ok(segment_name.clone());
                }
            }
        }
    }
    
    // Default fallback
    Ok(config.default_segment.clone())
}

/// Check if request matches traffic rule
fn matches_traffic_rule(
    request: &Request, 
    client_ip: IpAddr,
    rule: &TrafficRule
) -> bool {
    // Check path pattern if present
    if let Some(pattern) = &rule.path_pattern {
        if !request.uri().path().starts_with(pattern) {
            return false;
        }
    }
    
    // Check header matches
    for (header_name, header_value) in &rule.header_matches {
        match request.headers().get(header_name) {
            Some(value) => {
                if let Ok(value_str) = value.to_str() {
                    if value_str != header_value {
                        return false;
                    }
                } else {
                    return false;
                }
            },
            None => return false,
        }
    }
    
    // Check client IP matches
    if !rule.client_ip_matches.is_empty() {
        if !rule.client_ip_matches.iter().any(|net| net.contains(client_ip)) {
            return false;
        }
    }
    
    // All conditions matched
    true
}
```

### 10. Control Plane Security

Implement secure load balancer control plane:

```rust
/// Load balancer control plane security
pub struct ControlPlaneSecurityConfig {
    /// Access tokens
    access_tokens: HashMap<String, Vec<Permission>>,
    /// Allowed management IPs
    allowed_management_ips: Vec<IpNetwork>,
    /// Management interface binding
    management_bind_address: SocketAddr,
    /// TLS configuration for management interface
    tls_config: Option<TlsConfig>,
    /// Audit logging
    audit_logging: bool,
}

/// Permission for control plane actions
pub enum Permission {
    /// View configuration
    ViewConfig,
    /// Modify configuration
    ModifyConfig,
    /// Add/remove backends
    ManageBackends,
    /// View statistics
    ViewStats,
    /// Restart load balancer
    Restart,
}

/// Authenticate control plane request
pub fn authenticate_control_plane_request(
    request: &Request,
    config: &ControlPlaneSecurityConfig
) -> Result<Vec<Permission>> {
    // Check client IP
    let client_ip = match get_client_ip(request) {
        Some(ip) => ip,
        None => return Err(Error::new("Unknown client IP")),
    };
    
    // Check if client IP is allowed
    if !config.allowed_management_ips.iter().any(|net| net.contains(client_ip)) {
        return Err(Error::new("Client IP not authorized for management"));
    }
    
    // Check authorization header
    let token = match request.headers().get(header::AUTHORIZATION) {
        Some(value) => {
            let auth_str = value.to_str()?;
            if auth_str.starts_with("Bearer ") {
                &auth_str[7..]
            } else {
                return Err(Error::new("Invalid authorization format"));
            }
        },
        None => return Err(Error::new("Missing authorization header")),
    };
    
    // Look up permissions for token
    match config.access_tokens.get(token) {
        Some(permissions) => Ok(permissions.clone()),
        None => Err(Error::new("Invalid access token")),
    }
}

/// Validate control plane action
pub fn authorize_control_plane_action(
    action: &ControlPlaneAction,
    permissions: &[Permission]
) -> Result<()> {
    let required_permission = match action {
        ControlPlaneAction::GetConfig => Permission::ViewConfig,
        ControlPlaneAction::UpdateConfig(_) => Permission::ModifyConfig,
        ControlPlaneAction::AddBackend(_) => Permission::ManageBackends,
        ControlPlaneAction::RemoveBackend(_) => Permission::ManageBackends,
        ControlPlaneAction::GetStats => Permission::ViewStats,
        ControlPlaneAction::Restart => Permission::Restart,
    };
    
    if permissions.contains(&required_permission) {
        Ok(())
    } else {
        Err(Error::new("Insufficient permissions for this action"))
    }
}
```

## Configuration Example

```yaml
load_balancer:
  strategy: least-connections
  health_check:
    interval: 10s
    timeout: 5s
    path: /health
    expected_status: 200
  backends:
    - uri: http://backend1:8080
      weight: 100
    - uri: http://backend2:8080
      weight: 100
    - uri: http://backend3:8080
      weight: 50
```

## Rust Learning Summary

In this tutorial, you've learned:

1. **Advanced Type System Features**
   - Generic Associated Types
   - Type-state programming
   - Trait bounds and associated types

2. **Concurrency Patterns**
   - Atomic operations
   - Lock-free algorithms
   - Async trait implementation

3. **Zero-Cost Abstractions**
   - Compile-time guarantees
   - Type-level state machines
   - Efficient runtime behavior

## Next Steps

In the next tutorial, we'll focus on benchmarking and performance optimization, where we'll learn about:
- Rust's built-in benchmarking tools
- Flamegraphs and profiling
- Memory analysis and optimization

## Navigation
- [Previous: Security Features](12-security-features.md)
- [Next: Benchmarking & Performance](14-benchmarking-performance.md)