# Advanced Plugin System

## Learning Objectives
- Understand different plugin architecture patterns for Rust web applications
- Implement dynamic loading and unloading of plugin modules
- Design robust API contracts between core application and plugins
- Create a secure sandboxing system for third-party plugins
- Build a versioned plugin registry with dependency resolution

## Prerequisites
- Understanding of Rust traits and dynamic dispatch
- Knowledge of Rust's FFI (Foreign Function Interface)
- Familiarity with dependency injection patterns
- Basic understanding of module systems and linking

## Introduction

A well-designed plugin system allows a web application to be extended with new features without modifying the core codebase. This module covers implementing sophisticated plugin architectures in Rust webservers, from basic trait-based extensions to dynamic library loading and sandboxed plugin execution.

## Plugin System Fundamentals

### Key Concepts

- **Plugin Architecture**: The overall design pattern for extending applications
- **Plugin Interface**: Contracts defining how plugins interact with the core
- **Dynamic Loading**: Loading plugins at runtime vs. compile time
- **Registration**: How plugins announce their capabilities to the core
- **Lifecycle Management**: Initialization, activation, deactivation, and cleanup
- **Dependency Resolution**: Managing relationships between plugins

### Plugin Architecture Patterns

1. **Trait-based plugins**: Compile-time extensions using Rust's trait system
2. **Dynamic library plugins**: Runtime-loaded modules using `libloading`
3. **WASM plugins**: WebAssembly modules for sandboxed execution
4. **Script-based plugins**: Using embedded scripting languages
5. **HTTP-based plugins**: External services communicating via API

## Trait-Based Plugin System

The simplest approach uses Rust's trait system for compile-time plugins:

```rust
use std::collections::HashMap;
use std::sync::Arc;

// Plugin trait defining the interface
pub trait WebServerPlugin: Send + Sync {
    fn name(&self) -> &str;
    fn version(&self) -> &str;
    fn description(&self) -> &str;
    
    fn on_initialize(&self, context: &PluginContext) -> Result<(), PluginError>;
    fn on_request(&self, request: &mut HttpRequest) -> Result<(), PluginError>;
    fn on_response(&self, response: &mut HttpResponse) -> Result<(), PluginError>;
    fn on_shutdown(&self) -> Result<(), PluginError>;
}

// Plugin context provides access to core functionality
pub struct PluginContext {
    config: Arc<HashMap<String, String>>,
    logger: Arc<dyn Logger>,
}

// Plugin error type
#[derive(Debug, thiserror::Error)]
pub enum PluginError {
    #[error("Initialization failed: {0}")]
    InitError(String),
    #[error("Runtime error: {0}")]
    RuntimeError(String),
}

// Plugin registry manages all loaded plugins
pub struct PluginRegistry {
    plugins: Vec<Arc<dyn WebServerPlugin>>,
    context: PluginContext,
}

impl PluginRegistry {
    pub fn new(config: Arc<HashMap<String, String>>, logger: Arc<dyn Logger>) -> Self {
        Self {
            plugins: Vec::new(),
            context: PluginContext { config, logger },
        }
    }
    
    pub fn register<P: WebServerPlugin + 'static>(&mut self, plugin: P) {
        let plugin = Arc::new(plugin);
        
        // Initialize plugin
        if let Err(e) = plugin.on_initialize(&self.context) {
            eprintln!("Failed to initialize plugin {}: {}", plugin.name(), e);
            return;
        }
        
        println!("Registered plugin: {} v{}", plugin.name(), plugin.version());
        self.plugins.push(plugin);
    }
    
    pub fn handle_request(&self, request: &mut HttpRequest) {
        for plugin in &self.plugins {
            if let Err(e) = plugin.on_request(request) {
                eprintln!("Plugin {} error: {}", plugin.name(), e);
            }
        }
    }
    
    pub fn handle_response(&self, response: &mut HttpResponse) {
        for plugin in &self.plugins {
            if let Err(e) = plugin.on_response(response) {
                eprintln!("Plugin {} error: {}", plugin.name(), e);
            }
        }
    }
    
    pub fn shutdown(&self) {
        for plugin in &self.plugins {
            if let Err(e) = plugin.on_shutdown() {
                eprintln!("Error shutting down plugin {}: {}", plugin.name(), e);
            }
        }
    }
}

// Example authentication plugin
pub struct AuthPlugin {
    name: String,
    version: String,
}

impl AuthPlugin {
    pub fn new() -> Self {
        Self {
            name: "Auth Plugin".to_string(),
            version: "1.0.0".to_string(),
        }
    }
}

impl WebServerPlugin for AuthPlugin {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn version(&self) -> &str {
        &self.version
    }
    
    fn description(&self) -> &str {
        "Authentication and authorization plugin"
    }
    
    fn on_initialize(&self, context: &PluginContext) -> Result<(), PluginError> {
        println!("Auth plugin initialized");
        Ok(())
    }
    
    fn on_request(&self, request: &mut HttpRequest) -> Result<(), PluginError> {
        // Check authentication
        if let Some(auth_header) = request.headers.get("Authorization") {
            // Process auth header...
        }
        Ok(())
    }
    
    fn on_response(&self, _: &mut HttpResponse) -> Result<(), PluginError> {
        // No-op for this plugin
        Ok(())
    }
    
    fn on_shutdown(&self) -> Result<(), PluginError> {
        println!("Auth plugin shut down");
        Ok(())
    }
}

// Server startup with plugin registration
fn main() {
    let config = Arc::new(HashMap::new());
    let logger = Arc::new(ConsoleLogger::new());
    
    let mut registry = PluginRegistry::new(config, logger);
    
    // Register plugins
    registry.register(AuthPlugin::new());
    registry.register(CachePlugin::new());
    
    // Start server with plugins
    let server = WebServer::new(registry);
    server.start();
}
```

## Dynamic Library Plugin System

For runtime-loaded plugins, use the `libloading` crate:

```rust
use libloading::{Library, Symbol};
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use std::sync::Arc;
use std::any::Any;
use std::ffi::CStr;

// Type definitions for FFI
pub type InitializeFn = unsafe extern "C" fn(*mut PluginContext) -> *mut PluginInstance;
pub type ShutdownFn = unsafe extern "C" fn(*mut PluginInstance);
pub type HandleRequestFn = unsafe extern "C" fn(*mut PluginInstance, *mut HttpRequest) -> bool;

// Plugin metadata structure - shared between core and plugins
#[repr(C)]
pub struct PluginInfo {
    name: *const libc::c_char,
    version: *const libc::c_char,
    description: *const libc::c_char,
}

// Opaque plugin instance for FFI
pub struct PluginInstance {
    // Plugin-specific data
    _private: Box<dyn Any>,
}

// Plugin context accessible to plugins
#[repr(C)]
pub struct PluginContext {
    // Function pointers for callbacks
    log_fn: Option<unsafe extern "C" fn(level: i32, message: *const libc::c_char)>,
    get_config_fn: Option<unsafe extern "C" fn(key: *const libc::c_char) -> *const libc::c_char>,
}

// Dynamic plugin wrapper
struct DynamicPlugin {
    name: String,
    version: String,
    description: String,
    library: Library,
    instance: *mut PluginInstance,
    handle_request_fn: Symbol<HandleRequestFn>,
    shutdown_fn: Symbol<ShutdownFn>,
}

impl Drop for DynamicPlugin {
    fn drop(&mut self) {
        unsafe {
            (self.shutdown_fn)(self.instance);
            // Library is dropped automatically, which unloads the dynamic library
        }
    }
}

// Dynamic plugin manager
struct DynamicPluginManager {
    plugins: Vec<DynamicPlugin>,
    plugin_dir: PathBuf,
    context: PluginContext,
}

impl DynamicPluginManager {
    fn new(plugin_dir: PathBuf) -> Self {
        // Create plugin context with callback functions
        let context = PluginContext {
            log_fn: Some(Self::log_callback),
            get_config_fn: Some(Self::config_callback),
        };
        
        Self {
            plugins: Vec::new(),
            plugin_dir,
            context,
        }
    }
    
    // Callback used by plugins to log messages
    unsafe extern "C" fn log_callback(level: i32, message: *const libc::c_char) {
        let message = CStr::from_ptr(message).to_string_lossy();
        match level {
            0 => println!("[DEBUG] {}", message),
            1 => println!("[INFO] {}", message),
            2 => eprintln!("[ERROR] {}", message),
            _ => println!("[UNKNOWN] {}", message),
        }
    }
    
    // Callback used by plugins to access configuration
    unsafe extern "C" fn config_callback(key: *const libc::c_char) -> *const libc::c_char {
        let key = CStr::from_ptr(key).to_string_lossy();
        
        // In a real implementation, look up in config store
        static EMPTY: &[u8] = b"\0";
        EMPTY.as_ptr() as *const libc::c_char
    }
    
    // Load all plugins from the plugin directory
    fn load_all_plugins(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let entries = std::fs::read_dir(&self.plugin_dir)?;
        
        for entry in entries {
            let entry = entry?;
            let path = entry.path();
            
            // Only look at dynamic libraries
            if !Self::is_dynamic_library(&path) {
                continue;
            }
            
            if let Err(e) = self.load_plugin(&path) {
                eprintln!("Failed to load plugin {}: {}", 
                          path.display(), e);
            }
        }
        
        Ok(())
    }
    
    // Check if path is a dynamic library
    fn is_dynamic_library(path: &Path) -> bool {
        if let Some(ext) = path.extension() {
            #[cfg(target_os = "windows")]
            return ext == "dll";
            
            #[cfg(target_os = "linux")]
            return ext == "so";
            
            #[cfg(target_os = "macos")]
            return ext == "dylib";
        }
        
        false
    }
    
    // Load a single plugin from path
    fn load_plugin(&mut self, path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            // Load the dynamic library
            let library = Library::new(path)?;
            
            // Get plugin info
            let get_info: Symbol<extern "C" fn() -> *const PluginInfo> = 
                library.get(b"get_plugin_info")?;
            let info = get_info();
            
            // Extract plugin metadata
            let name = CStr::from_ptr((*info).name).to_string_lossy().to_string();
            let version = CStr::from_ptr((*info).version).to_string_lossy().to_string();
            let description = CStr::from_ptr((*info).description).to_string_lossy().to_string();
            
            println!("Loading plugin: {} v{}", name, version);
            
            // Get plugin functions
            let initialize: Symbol<InitializeFn> = library.get(b"initialize_plugin")?;
            let handle_request: Symbol<HandleRequestFn> = library.get(b"handle_request")?;
            let shutdown: Symbol<ShutdownFn> = library.get(b"shutdown_plugin")?;
            
            // Create context pointer
            let ctx_ptr = &mut self.context as *mut PluginContext;
            
            // Initialize plugin
            let instance = initialize(ctx_ptr);
            
            // Add to loaded plugins
            self.plugins.push(DynamicPlugin {
                name,
                version,
                description,
                library,
                instance,
                handle_request_fn: handle_request,
                shutdown_fn: shutdown,
            });
            
            Ok(())
        }
    }
    
    // Process request through all plugins
    fn handle_request(&self, request: &mut HttpRequest) {
        for plugin in &self.plugins {
            unsafe {
                let request_ptr = request as *mut HttpRequest;
                if !(plugin.handle_request_fn)(plugin.instance, request_ptr) {
                    // Plugin returned false, stop processing
                    break;
                }
            }
        }
    }
    
    // Unload all plugins
    fn unload_all(&mut self) {
        // Plugins are dropped, which calls their shutdown function
        self.plugins.clear();
    }
}
```

### Example Plugin Implementation

```rust
// plugin.rs - Compiled as a dynamic library
use std::ffi::{CStr, CString};
use std::os::raw::c_char;
use std::ptr;

// Import shared plugin API definitions
use webserver_plugin_api::{PluginInfo, PluginInstance, PluginContext, HttpRequest};

// Plugin-specific data
struct MyPlugin {
    name: String,
    config: Option<String>,
}

// Static plugin info
static PLUGIN_INFO: PluginInfo = PluginInfo {
    name: b"MyPlugin\0" as *const u8 as *const c_char,
    version: b"1.0.0\0" as *const u8 as *const c_char,
    description: b"Example plugin implementation\0" as *const u8 as *const c_char,
};

// Export plugin info function
#[no_mangle]
pub extern "C" fn get_plugin_info() -> *const PluginInfo {
    &PLUGIN_INFO
}

// Plugin initialization
#[no_mangle]
pub unsafe extern "C" fn initialize_plugin(ctx: *mut PluginContext) -> *mut PluginInstance {
    // Log initialization
    if let Some(log_fn) = (*ctx).log_fn {
        let msg = CString::new("Plugin initializing").unwrap();
        log_fn(1, msg.as_ptr());
    }
    
    // Get configuration
    let mut config = None;
    if let Some(get_config_fn) = (*ctx).get_config_fn {
        let key = CString::new("plugin.myconfig").unwrap();
        let value = get_config_fn(key.as_ptr());
        if !value.is_null() {
            let value_str = CStr::from_ptr(value).to_string_lossy().to_string();
            config = Some(value_str);
        }
    }
    
    // Create plugin instance
    let plugin = MyPlugin {
        name: "MyPlugin".to_string(),
        config,
    };
    
    // Box plugin and return as opaque pointer
    let instance = Box::new(PluginInstance {
        _private: Box::new(plugin),
    });
    
    Box::into_raw(instance)
}

// Handle HTTP request
#[no_mangle]
pub unsafe extern "C" fn handle_request(
    instance: *mut PluginInstance,
    request: *mut HttpRequest
) -> bool {
    // In a real plugin, modify the request
    // ...
    
    // Return true to continue processing
    true
}

// Plugin shutdown
#[no_mangle]
pub unsafe extern "C" fn shutdown_plugin(instance: *mut PluginInstance) {
    // Convert opaque pointer back to Box and drop
    let _ = Box::from_raw(instance);
}
```

## WebAssembly Plugin System

Using WebAssembly for sandboxed plugin execution:

```rust
use wasmtime::{Engine, Module, Store, Instance, Caller, Func, Memory, Extern};
use anyhow::{Result, anyhow};
use std::path::Path;
use std::sync::Arc;

// Plugin API for WASM modules
struct WasmPluginApi {
    memory: Option<Memory>,
}

// WASM Plugin manager
struct WasmPluginManager {
    engine: Engine,
    plugins: Vec<WasmPlugin>,
}

// WASM Plugin instance
struct WasmPlugin {
    name: String,
    instance: Instance,
    handle_request: Func,
    store: Store<WasmPluginApi>,
}

impl WasmPluginManager {
    fn new() -> Self {
        let engine = Engine::default();
        
        Self {
            engine,
            plugins: Vec::new(),
        }
    }
    
    fn load_plugin(&mut self, path: &Path) -> Result<()> {
        let module = Module::from_file(&self.engine, path)?;
        
        let mut store = Store::new(
            &self.engine, 
            WasmPluginApi { 
                memory: None,
            }
        );
        
        // Define host functions available to the WASM module
        let log_func = Func::wrap(&mut store, |caller: Caller<'_, WasmPluginApi>, ptr: i32, len: i32| {
            let memory = match caller.data().memory.as_ref() {
                Some(mem) => mem,
                None => return Err(anyhow!("No memory exported from WASM module")),
            };
            
            // Read log message from WASM memory
            let mut buffer = vec![0; len as usize];
            memory.read(caller, ptr as usize, &mut buffer)?;
            
            // Convert to string
            let message = String::from_utf8_lossy(&buffer);
            println!("[WASM Plugin] {}", message);
            
            Ok(())
        });
        
        // Instantiate the module with imports
        let instance = Instance::new(
            &mut store, 
            &module, 
            &[Extern::Func(log_func)]
        )?;
        
        // Get exported memory from the module
        let memory = instance.get_memory(&mut store, "memory")
            .ok_or_else(|| anyhow!("WASM module didn't export memory"))?;
        
        // Store the memory in our context
        store.data_mut().memory = Some(memory);
        
        // Get exported functions
        let get_name = instance.get_func(&mut store, "get_name")
            .ok_or_else(|| anyhow!("WASM module didn't export get_name function"))?
            .typed::<(i32,), i32>(&store)?;
        
        let handle_request = instance.get_func(&mut store, "handle_request")
            .ok_or_else(|| anyhow!("WASM module didn't export handle_request function"))?;
        
        // Call get_name to retrieve plugin name
        let ptr_len = get_name.call(&mut store, (0,))?;
        let ptr = ptr_len >> 32;
        let len = ptr_len & 0xFFFFFFFF;
        
        let mut name_buffer = vec![0; len as usize];
        memory.read(&store, ptr as usize, &mut name_buffer)?;
        
        let name = String::from_utf8_lossy(&name_buffer).to_string();
        println!("Loaded WASM plugin: {}", name);
        
        self.plugins.push(WasmPlugin {
            name,
            instance,
            handle_request,
            store,
        });
        
        Ok(())
    }
    
    fn handle_request(&mut self, request_data: &[u8]) -> Result<()> {
        for plugin in &mut self.plugins {
            // For each plugin:
            // 1. Copy request data to WASM memory
            let memory = plugin.store.data().memory.as_ref()
                .ok_or_else(|| anyhow!("No memory in plugin"))?;
            
            // Allocate memory in WASM module
            let alloc = plugin.instance.get_func(&mut plugin.store, "alloc")
                .ok_or_else(|| anyhow!("WASM module didn't export alloc function"))?
                .typed::<(i32,), i32>(&plugin.store)?;
            
            let ptr = alloc.call(&mut plugin.store, (request_data.len() as i32,))?;
            
            // Copy request data to WASM memory
            memory.write(&mut plugin.store, ptr as usize, request_data)?;
            
            // Call handle_request
            let result = plugin.handle_request.typed::<(i32, i32), i32>(&plugin.store)?
                .call(&mut plugin.store, (ptr, request_data.len() as i32))?;
            
            // Process result
            if result == 0 {
                // Plugin wants to stop processing
                break;
            }
        }
        
        Ok(())
    }
}

// Example usage
fn main() -> Result<()> {
    let mut manager = WasmPluginManager::new();
    
    // Load plugins
    manager.load_plugin(Path::new("plugins/auth.wasm"))?;
    manager.load_plugin(Path::new("plugins/logger.wasm"))?;
    
    // Handle request
    let request_data = b"GET /api/users HTTP/1.1\r\nHost: example.com\r\n\r\n";
    manager.handle_request(request_data)?;
    
    Ok(())
}
```

### Example WASM Plugin Source

```rust
// Compile with wasm32-unknown-unknown target
// auth_plugin.rs

// Memory management
#[no_mangle]
pub extern "C" fn alloc(size: i32) -> i32 {
    // Allocate memory and return pointer
    let mut buffer = Vec::<u8>::with_capacity(size as usize);
    let ptr = buffer.as_mut_ptr() as i32;
    
    // Leak the vector so it's not freed
    std::mem::forget(buffer);
    ptr
}

// Export plugin name
#[no_mangle]
pub extern "C" fn get_name(buffer_ptr: i32) -> i64 {
    let name = "AuthPlugin";
    let name_bytes = name.as_bytes();
    
    unsafe {
        std::ptr::copy_nonoverlapping(
            name_bytes.as_ptr(),
            buffer_ptr as *mut u8,
            name_bytes.len()
        );
    }
    
    ((buffer_ptr as i64) << 32) | (name_bytes.len() as i64)
}

// External function provided by host
extern "C" {
    fn log(ptr: i32, len: i32);
}

// Handle HTTP request
#[no_mangle]
pub extern "C" fn handle_request(ptr: i32, len: i32) -> i32 {
    // Log that we received a request
    let message = "Auth plugin processing request";
    let message_bytes = message.as_bytes();
    
    unsafe {
        std::ptr::copy_nonoverlapping(
            message_bytes.as_ptr(),
            ptr as *mut u8,
            message_bytes.len()
        );
        
        log(ptr, message_bytes.len() as i32);
    }
    
    // Return 1 to continue processing
    1
}
```

## Script-Based Plugin System

Using an embedded scripting language like Rhai:

```rust
use rhai::{Engine, Scope, AST};
use std::path::{Path, PathBuf};
use std::fs;
use std::collections::HashMap;

// Plugin script manager
struct ScriptPluginManager {
    engine: Engine,
    scripts: HashMap<String, AST>,
    script_dir: PathBuf,
}

impl ScriptPluginManager {
    fn new(script_dir: PathBuf) -> Self {
        let mut engine = Engine::new();
        
        // Register host functions
        engine.register_fn("log", |message: &str| {
            println!("[Script] {}", message);
        });
        
        Self {
            engine,
            scripts: HashMap::new(),
            script_dir,
        }
    }
    
    fn load_all_scripts(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let entries = fs::read_dir(&self.script_dir)?;
        
        for entry in entries {
            let entry = entry?;
            let path = entry.path();
            
            // Only look at .rhai scripts
            if path.extension().and_then(|ext| ext.to_str()) == Some("rhai") {
                if let Err(e) = self.load_script(&path) {
                    eprintln!("Failed to load script {}: {}", 
                              path.display(), e);
                }
            }
        }
        
        Ok(())
    }
    
    fn load_script(&mut self, path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let script_name = path.file_stem()
            .and_then(|name| name.to_str())
            .ok_or("Invalid script filename")?;
        
        let script_source = fs::read_to_string(path)?;
        
        // Compile the script
        let ast = self.engine.compile(&script_source)?;
        
        // Verify the script has required functions
        if !self.engine.eval_ast::<bool>(&ast, "has_on_request()")? {
            return Err("Script must have on_request function".into());
        }
        
        println!("Loaded script plugin: {}", script_name);
        self.scripts.insert(script_name.to_string(), ast);
        
        Ok(())
    }
    
    fn handle_request(&mut self, request: &HashMap<String, String>) -> Result<(), Box<dyn std::error::Error>> {
        let mut scope = Scope::new();
        
        // Add request data to scope
        scope.push("request", request.clone());
        
        // Call each script's on_request function
        for (name, ast) in &self.scripts {
            println!("Running script: {}", name);
            let result: bool = self.engine.eval_ast_with_scope(&mut scope, ast, "on_request(request)")?;
            
            if !result {
                println!("Script {} halted request processing", name);
                break;
            }
        }
        
        Ok(())
    }
}

// Example usage
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = ScriptPluginManager::new(PathBuf::from("scripts"));
    
    // Load all scripts
    manager.load_all_scripts()?;
    
    // Process a request
    let mut request = HashMap::new();
    request.insert("method".to_string(), "GET".to_string());
    request.insert("path".to_string(), "/api/users".to_string());
    
    manager.handle_request(&request)?;
    
    Ok(())
}
```

### Example Rhai Script Plugin

```rhai
// auth.rhai

// Initialization function
fn init() {
    log("Auth script initialized");
    return true;
}

// Request handler
fn on_request(request) {
    log("Processing request to: " + request["path"]);
    
    // Check if path requires authentication
    if request["path"].starts_with("/api/") {
        if !request.contains("auth_token") {
            log("Unauthorized request");
            return false;  // Stop processing
        }
        
        // Validate token (in a real plugin)
        log("Token validated");
    }
    
    return true;  // Continue processing
}

// Export whether we have required functions
fn has_on_request() {
    return true;
}
```

## Plugin Security and Sandboxing

Implementing security measures for plugins:

```rust
use std::sync::Arc;
use std::time::Duration;
use tokio::time::timeout;

// Resource limits for plugins
struct ResourceLimits {
    max_memory: usize,
    execution_timeout: Duration,
}

// Execute plugin with resource limitations
async fn execute_plugin_with_limits<F, T>(
    plugin_fn: F,
    limits: &ResourceLimits
) -> Result<T, PluginExecutionError>
where
    F: FnOnce() -> T + Send + 'static,
    T: Send + 'static,
{
    // Execute with timeout
    let task = tokio::spawn(async move {
        // Execute plugin function
        plugin_fn()
    });
    
    match timeout(limits.execution_timeout, task).await {
        Ok(result) => match result {
            Ok(output) => Ok(output),
            Err(_) => Err(PluginExecutionError::InternalError),
        },
        Err(_) => Err(PluginExecutionError::Timeout),
    }
}

// Sandbox plugin execution
struct PluginSandbox {
    limits: ResourceLimits,
}

impl PluginSandbox {
    fn new() -> Self {
        Self {
            limits: ResourceLimits {
                max_memory: 10 * 1024 * 1024, // 10MB
                execution_timeout: Duration::from_secs(1),
            },
        }
    }
    
    async fn execute<F, T>(&self, plugin_fn: F) -> Result<T, PluginExecutionError>
    where
        F: FnOnce() -> T + Send + 'static,
        T: Send + 'static,
    {
        execute_plugin_with_limits(plugin_fn, &self.limits).await
    }
}

#[derive(Debug)]
enum PluginExecutionError {
    Timeout,
    ResourceExceeded,
    InternalError,
}

// Usage example
async fn main() {
    let sandbox = PluginSandbox::new();
    
    let result = sandbox.execute(|| {
        // Plugin code here
        println!("Plugin executing...");
        
        // Simulate work
        std::thread::sleep(Duration::from_millis(500));
        
        "Plugin result"
    }).await;
    
    match result {
        Ok(output) => println!("Plugin output: {}", output),
        Err(e) => eprintln!("Plugin execution error: {:?}", e),
    }
}
```

## Plugin Dependency Resolution

Managing dependencies between plugins:

```rust
use std::collections::{HashMap, HashSet};
use petgraph::graph::{DiGraph, NodeIndex};
use petgraph::algo::toposort;

// Plugin metadata with dependencies
struct PluginMetadata {
    name: String,
    version: String,
    dependencies: Vec<String>,
}

// Plugin dependency resolver
struct PluginResolver {
    plugins: HashMap<String, PluginMetadata>,
}

impl PluginResolver {
    fn new() -> Self {
        Self {
            plugins: HashMap::new(),
        }
    }
    
    fn register_plugin(&mut self, metadata: PluginMetadata) {
        self.plugins.insert(metadata.name.clone(), metadata);
    }
    
    fn resolve_load_order(&self) -> Result<Vec<String>, String> {
        // Build dependency graph
        let mut graph = DiGraph::<&str, ()>::new();
        let mut node_indices = HashMap::new();
        
        // Add nodes for all plugins
        for (name, _) in &self.plugins {
            let idx = graph.add_node(name);
            node_indices.insert(name, idx);
        }
        
        // Add edges for dependencies
        for (name, metadata) in &self.plugins {
            let source = *node_indices.get(name).unwrap();
            
            for dep in &metadata.dependencies {
                if let Some(&target) = node_indices.get(dep) {
                    graph.add_edge(source, target, ());
                } else {
                    return Err(format!("Plugin {} depends on unknown plugin {}", name, dep));
                }
            }
        }
        
        // Perform topological sort to get load order
        match toposort(&graph, None) {
            Ok(order) => {
                // Convert node indices to plugin names
                let mut result = Vec::new();
                for idx in order {
                    let name = *graph[idx];
                    result.push(name.to_string());
                }
                
                // Reverse to get correct load order
                result.reverse();
                Ok(result)
            },
            Err(_) => Err("Circular dependency detected".to_string()),
        }
    }
}

// Example usage
fn main() {
    let mut resolver = PluginResolver::new();
    
    // Register plugins with their dependencies
    resolver.register_plugin(PluginMetadata {
        name: "core".to_string(),
        version: "1.0".to_string(),
        dependencies: vec![],
    });
    
    resolver.register_plugin(PluginMetadata {
        name: "auth".to_string(),
        version: "1.0".to_string(),
        dependencies: vec!["core".to_string()],
    });
    
    resolver.register_plugin(PluginMetadata {
        name: "dashboard".to_string(),
        version: "1.0".to_string(),
        dependencies: vec!["auth".to_string(), "core".to_string()],
    });
    
    // Get load order
    match resolver.resolve_load_order() {
        Ok(order) => {
            println!("Plugin load order:");
            for plugin in order {
                println!("- {}", plugin);
            }
        },
        Err(e) => eprintln!("Failed to resolve dependencies: {}", e),
    }
}
```

## Best Practices

### Plugin API Design

```rust
// A well-designed plugin API provides:
// 1. Stability guarantees (semver)
// 2. Versioned interfaces
// 3. Clear extension points

// Versioned plugin trait
pub trait WebServerPluginV1: Send + Sync {
    // Core functionality (never changes)
    fn name(&self) -> &str;
    fn version(&self) -> &str;
    
    // Lifecycle hooks
    fn on_initialize(&self) -> Result<(), PluginError>;
    fn on_shutdown(&self) -> Result<(), PluginError>;
    
    // Extension points
    fn on_request(&self, request: &mut HttpRequest) -> Result<(), PluginError>;
}

// Extended plugin trait for V2
pub trait WebServerPluginV2: WebServerPluginV1 {
    // Added in V2
    fn on_response(&self, response: &mut HttpResponse) -> Result<(), PluginError>;
    fn on_error(&self, error: &HttpError) -> Result<(), PluginError>;
}

// Plugin registry with version support
pub struct VersionedPluginRegistry {
    plugins_v1: Vec<Arc<dyn WebServerPluginV1>>,
    plugins_v2: Vec<Arc<dyn WebServerPluginV2>>,
}

impl VersionedPluginRegistry {
    pub fn register_plugin<P>(&mut self, plugin: P)
    where
        P: 'static + WebServerPluginV2,
    {
        let plugin_arc = Arc::new(plugin);
        
        // Register for both APIs
        self.plugins_v2.push(plugin_arc.clone());
        self.plugins_v1.push(plugin_arc);
        
        println!("Registered V2 plugin: {}", plugin_arc.name());
    }
    
    pub fn register_plugin_v1<P>(&mut self, plugin: P)
    where
        P: 'static + WebServerPluginV1,
    {
        let plugin_arc = Arc::new(plugin);
        self.plugins_v1.push(plugin_arc);
        
        println!("Registered V1 plugin: {}", plugin_arc.name());
    }
}
```

### Plugin Documentation

Providing clear documentation for plugin developers:

```rust
/// # Plugin API Documentation
/// 
/// This module defines the interface for webserver plugins.
/// 
/// ## Plugin Lifecycle
/// 
/// 1. Plugin is loaded and registered
/// 2. `on_initialize` is called during server startup
/// 3. `on_request` is called for each incoming request
/// 4. `on_response` is called before sending each response
/// 5. `on_shutdown` is called during server shutdown
/// 
/// ## Example Plugin
/// 
/// ```rust
/// use webserver_plugin_api::{WebServerPluginV1, HttpRequest, PluginError};
/// 
/// struct MyPlugin {
///     name: String,
/// }
/// 
/// impl WebServerPluginV1 for MyPlugin {
///     fn name(&self) -> &str {
///         &self.name
///     }
///     
///     fn version(&self) -> &str {
///         "1.0.0"
///     }
///     
///     fn on_initialize(&self) -> Result<(), PluginError> {
///         println!("Plugin initialized");
///         Ok(())
///     }
///     
///     fn on_shutdown(&self) -> Result<(), PluginError> {
///         println!("Plugin shutting down");
///         Ok(())
///     }
///     
///     fn on_request(&self, request: &mut HttpRequest) -> Result<(), PluginError> {
///         // Process request
///         Ok(())
///     }
/// }
/// ```

// Plugin configuration schema
#[derive(serde::Deserialize, serde::Serialize)]
pub struct PluginConfig {
    /// Plugin name
    pub name: String,
    
    /// Plugin version
    pub version: String,
    
    /// Whether the plugin is enabled
    pub enabled: bool,
    
    /// Plugin-specific configuration
    pub settings: HashMap<String, serde_json::Value>,
}

// Plugin capabilities declaration
#[derive(serde::Deserialize, serde::Serialize)]
pub struct PluginCapabilities {
    /// Features supported by this plugin
    pub features: HashSet<String>,
    
    /// Extension points this plugin implements
    pub extensions: HashSet<String>,
    
    /// Permissions required by this plugin
    pub permissions: HashSet<String>,
}
```

## Knowledge Check

1. What are the key differences between compile-time and runtime plugin systems in Rust?
2. How would you implement proper sandboxing for dynamically loaded plugins?
3. What mechanisms can you use for version compatibility in a plugin system?
4. How would you design a plugin system that supports dependency resolution between plugins?
5. What are the security considerations when loading external plugins?
6. Compare the advantages and disadvantages of WASM-based plugins versus dynamic library plugins.

## Additional Resources

- [Rust libloading Crate Documentation](https://docs.rs/libloading/)
- [WebAssembly with Wasmtime](https://docs.wasmtime.dev/)
- [Plugin Design Patterns](https://gameprogrammingpatterns.com/service-locator.html)
- [Rust FFI Guide](https://doc.rust-lang.org/nomicon/ffi.html)
- [Rhai Scripting Engine](https://rhai.rs/book/)
- [Semver for API Stability](https://semver.org/)

## Diagram: Plugin Architecture

```mermaid
graph TD
    A[Webserver Core] -- "1. Register" --> B[Plugin Registry]
    B -- "2. Initialize" --> C[Plugin 1]
    B -- "2. Initialize" --> D[Plugin 2]
    B -- "2. Initialize" --> E[Plugin 3]
    
    F[HTTP Request] --> A
    A -- "3. Process" --> B
    B -- "4. Filter Request" --> C
    C -- "5. Continue" --> D
    D -- "6. Continue" --> E
    E -- "7. Complete" --> B
    B -- "8. Return" --> A
    A --> G[HTTP Response]
    
    C -. "Dynamic Load" .-> H[Plugin Library]
    D -. "Script Eval" .-> I[Plugin Script]
    E -. "WASM Sandbox" .-> J[WASM Module]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style H fill:#fbb,stroke:#333,stroke-width:2px
    style I fill:#fbb,stroke:#333,stroke-width:2px
    style J fill:#fbb,stroke:#333,stroke-width:2px
```
