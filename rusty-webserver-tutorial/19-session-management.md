# Session Management and Cookies

## Navigation
- [Previous: TLS & HTTPS](18-tls-https.md)
- [Next: Authentication & Authorization](20-authentication-authorization.md)

## Table of Contents
- [Introduction](#introduction)
- [Theory](#theory)
- [Session Implementation](#rust-example-using-cookie-crate)
- [Best Practices](#best-practices)
- [Security Considerations](#security-considerations)
- [Integration](#integration)
- [Quiz](#quiz)

## Introduction

Sessions and cookies allow you to track user state across requests.

## Theory
- Cookies are small pieces of data stored in the browser.
- Sessions associate a unique ID with server-side data.

## Rust Example (using `cookie` crate)
```rust
use cookie::{<PERSON><PERSON>, <PERSON>ieJar};
let mut jar = CookieJar::new();
jar.add(Cookie::new("session_id", "abc123"));
```

## Best Practices
- Use secure, HttpOnly, and SameSite cookie flags.
- Store minimal sensitive data in cookies.
- Implement session expiration and rotation.
- Use a secure session store with encryption.

## Security Considerations

Session management is a critical security component of web applications. Improper implementation can lead to session hijacking, fixation attacks, and unauthorized access to user data.

### Session Management Threat Model

Understanding the threats against session management helps guide your implementation:

```mermaid
flowchart TD
    A[Attacker] -->|1. Session fixation| SM[Session Management]
    A -->|2. Session hijacking| SM
    A -->|3. Cross-Site Request Forgery| SM
    A -->|4. Cookie theft| SM
    A -->|5. Session sidejacking| SM
    A -->|6. Brute force attacks| SM
    
    SM --> SS[Session Store]
    SM --> C[Client Cookies]
    
    class A fill:#f96,stroke:#333
    class SM fill:#69f,stroke:#333
    class SS,C fill:#6d9,stroke:#333
```

### Secure Session ID Generation

Implementing secure session ID generation is fundamental to session security:

```rust
/// Session ID generator
pub struct SessionIdGenerator {
    // Random number generator
    rng: ThreadRng,
    // Length of generated IDs in bytes
    id_length: usize,
}

impl SessionIdGenerator {
    /// Create a new session ID generator
    pub fn new(id_length: usize) -> Self {
        Self {
            rng: ThreadRng::default(),
            id_length: id_length.max(16), // Minimum 16 bytes (128 bits)
        }
    }
    
    /// Generate a cryptographically secure random session ID
    pub fn generate(&mut self) -> String {
        // Generate random bytes
        let mut bytes = vec![0u8; self.id_length];
        self.rng.fill_bytes(&mut bytes);
        
        // Convert to URL-safe base64
        BASE64_URL_SAFE_NO_PAD.encode(&bytes)
    }
    
    /// Check if a session ID meets security requirements
    pub fn validate_id(&self, id: &str) -> bool {
        // Check minimum length (128 bits / 16 bytes in base64 is about 22 chars)
        if id.len() < 22 {
            return false;
        }
        
        // Check character set (base64url)
        if !id.chars().all(|c| {
            c.is_ascii_alphanumeric() || c == '-' || c == '_'
        }) {
            return false;
        }
        
        // Check if it's decodable as base64
        if BASE64_URL_SAFE_NO_PAD.decode(id).is_err() {
            return false;
        }
        
        true
    }
}
```

### Secure Cookie Configuration

Properly configuring cookies is essential for session security:

```rust
/// Create a secure session cookie
pub fn create_secure_session_cookie(session_id: &str, domain: &str) -> Cookie<'static> {
    let mut cookie = Cookie::build("session_id", session_id.to_owned())
        .domain(domain.to_owned())
        .path("/")
        .secure(true)     // Only send over HTTPS
        .http_only(true)  // Not accessible via JavaScript
        .max_age(Duration::hours(2))  // 2 hour expiration
        .same_site(SameSite::Lax)     // Protect against CSRF
        .finish();
        
    // Add optional __Host- prefix for additional security
    // This ensures the cookie is only sent to the exact domain it was set for
    if !domain.contains('.') {  // Only for non-subdomain use
        cookie.set_name("__Host-session_id");
    }
    
    cookie
}

/// Configure secure cookie jar settings
pub fn configure_secure_cookie_jar() -> CookieJar {
    let mut jar = CookieJar::new();
    
    // Add secure defaults
    jar.add_parsing_filter(|cookie, _jar| {
        // Set Secure flag unless in development
        if !cfg!(debug_assertions) && !cookie.secure() {
            cookie.set_secure(true);
        }
        
        // Always set HttpOnly
        if !cookie.http_only() {
            cookie.set_http_only(true);
        }
        
        // Set SameSite if not specified
        if cookie.same_site().is_none() {
            cookie.set_same_site(SameSite::Lax);
        }
    });
    
    jar
}
```

### Session Store Security

Implement a secure session storage system to protect session data:

```rust
/// Session data encryption
struct SessionEncryptor {
    // Encryption key
    key: [u8; 32],
    // Nonce size
    nonce_size: usize,
}

impl SessionEncryptor {
    /// Create a new session encryptor
    pub fn new(key: [u8; 32]) -> Self {
        Self {
            key,
            nonce_size: 12, // 12 bytes for XChaCha20-Poly1305
        }
    }
    
    /// Encrypt session data
    pub fn encrypt(&self, data: &[u8]) -> Result<Vec<u8>> {
        // Generate random nonce
        let mut nonce = vec![0u8; self.nonce_size];
        ThreadRng::default().fill_bytes(&mut nonce);
        
        // Create cipher
        let cipher = XChaCha20Poly1305::new(&self.key.into());
        
        // Encrypt data
        let ciphertext = cipher
            .encrypt(&nonce.into(), data)
            .map_err(|_| ServerError::Session("Encryption failed".to_string()))?;
            
        // Combine nonce and ciphertext
        let mut result = nonce;
        result.extend_from_slice(&ciphertext);
        
        Ok(result)
    }
    
    /// Decrypt session data
    pub fn decrypt(&self, data: &[u8]) -> Result<Vec<u8>> {
        // Check if data is long enough
        if data.len() <= self.nonce_size {
            return Err(ServerError::Session("Invalid encrypted data".to_string()));
        }
        
        // Split nonce and ciphertext
        let nonce = &data[..self.nonce_size];
        let ciphertext = &data[self.nonce_size..];
        
        // Create cipher
        let cipher = XChaCha20Poly1305::new(&self.key.into());
        
        // Decrypt data
        cipher
            .decrypt(nonce.into(), ciphertext)
            .map_err(|_| ServerError::Session("Decryption failed".to_string()))
    }
}

/// Secure session store implementation
pub struct SecureSessionStore {
    // Session data store
    store: RwLock<HashMap<String, EncryptedSession>>,
    // Session encryptor
    encryptor: SessionEncryptor,
    // Session expiry time
    session_expiry: Duration,
    // When to run garbage collection
    gc_threshold: usize,
    // Current session count
    session_count: AtomicUsize,
    // Last GC run timestamp
    last_gc: AtomicU64,
}

/// Encrypted session data
struct EncryptedSession {
    // Encrypted data
    data: Vec<u8>,
    // Expiry time
    expires_at: DateTime<Utc>,
    // Last access time
    last_accessed: DateTime<Utc>,
}

impl SecureSessionStore {
    /// Create a new secure session store
    pub fn new(encryption_key: [u8; 32], expiry_hours: u64) -> Self {
        Self {
            store: RwLock::new(HashMap::new()),
            encryptor: SessionEncryptor::new(encryption_key),
            session_expiry: Duration::hours(expiry_hours as i64),
            gc_threshold: 1000, // Run GC after 1000 operations
            session_count: AtomicUsize::new(0),
            last_gc: AtomicU64::new(0),
        }
    }
    
    /// Store session data securely
    pub fn store_session(&self, session_id: &str, data: &[u8]) -> Result<()> {
        // Encrypt the data
        let encrypted = self.encryptor.encrypt(data)?;
        
        // Create expiry time
        let expires_at = Utc::now() + self.session_expiry;
        
        // Store the session
        let mut store = self.store.write().unwrap();
        store.insert(session_id.to_owned(), EncryptedSession {
            data: encrypted,
            expires_at,
            last_accessed: Utc::now(),
        });
        
        // Update session count
        self.session_count.fetch_add(1, Ordering::Relaxed);
        
        // Run garbage collection if needed
        self.run_gc_if_needed();
        
        Ok(())
    }
    
    /// Retrieve session data
    pub fn get_session(&self, session_id: &str) -> Result<Option<Vec<u8>>> {
        let mut store = self.store.write().unwrap();
        
        if let Some(session) = store.get_mut(session_id) {
            // Check if session has expired
            if Utc::now() > session.expires_at {
                // Remove expired session
                store.remove(session_id);
                self.session_count.fetch_sub(1, Ordering::Relaxed);
                return Ok(None);
            }
            
            // Update last access time
            session.last_accessed = Utc::now();
            
            // Decrypt the session data
            let decrypted = self.encryptor.decrypt(&session.data)?;
            return Ok(Some(decrypted));
        }
        
        Ok(None)
    }
    
    /// Delete a session
    pub fn delete_session(&self, session_id: &str) -> Result<bool> {
        let mut store = self.store.write().unwrap();
        if store.remove(session_id).is_some() {
            self.session_count.fetch_sub(1, Ordering::Relaxed);
            return Ok(true);
        }
        Ok(false)
    }
    
    /// Run garbage collection if needed
    fn run_gc_if_needed(&self) {
        let count = self.session_count.load(Ordering::Relaxed);
        let now = Utc::now().timestamp() as u64;
        let last_gc = self.last_gc.load(Ordering::Relaxed);
        
        // Run GC if session count exceeds threshold and at least 1 hour since last GC
        if count > self.gc_threshold && now - last_gc > 3600 {
            if let Ok(mut store) = self.store.try_write() {
                // Find expired sessions
                let now = Utc::now();
                let expired: Vec<String> = store.iter()
                    .filter_map(|(id, session)| {
                        if now > session.expires_at {
                            Some(id.clone())
                        } else {
                            None
                        }
                    })
                    .collect();
                
                // Remove expired sessions
                for id in &expired {
                    store.remove(id);
                }
                
                // Update session count
                if !expired.is_empty() {
                    self.session_count.fetch_sub(expired.len(), Ordering::Relaxed);
                }
                
                // Update last GC time
                self.last_gc.store(now.timestamp() as u64, Ordering::Relaxed);
                
                log::info!("Session GC completed: {} expired sessions removed", expired.len());
            }
        }
    }
}
```

### Session Management Implementation

Implement secure session management that defends against common attacks:

```rust
/// Session manager for handling all session operations
pub struct SessionManager {
    // Session store
    store: Arc<SecureSessionStore>,
    // Session ID generator
    id_generator: Mutex<SessionIdGenerator>,
    // Configuration
    config: SessionConfig,
}

/// Session configuration
pub struct SessionConfig {
    // Cookie domain
    cookie_domain: String,
    // Cookie path
    cookie_path: String,
    // Session expiry in hours
    expiry_hours: u64,
    // Whether to regenerate session IDs on login
    regenerate_on_login: bool,
    // Whether to use cookie prefixing
    use_cookie_prefix: bool,
    // Whether to mark cookies secure
    secure_cookies: bool,
    // Session cookie name
    cookie_name: String,
}

impl SessionManager {
    /// Create a new session manager
    pub fn new(encryption_key: [u8; 32], config: SessionConfig) -> Self {
        Self {
            store: Arc::new(SecureSessionStore::new(encryption_key, config.expiry_hours)),
            id_generator: Mutex::new(SessionIdGenerator::new(24)), // 24 bytes = 192 bits
            config,
        }
    }
    
    /// Create a new session
    pub fn create_session(&self, request: &Request, response: &mut Response) -> Result<String> {
        // Generate a secure session ID
        let session_id = self.id_generator.lock().unwrap().generate();
        
        // Create an empty session
        self.store.store_session(&session_id, b"{}")?;
        
        // Set the session cookie
        let cookie = self.create_session_cookie(&session_id);
        response.headers.insert(
            "set-cookie".to_string(),
            cookie.to_string()
        );
        
        Ok(session_id)
    }
    
    /// Get session from request
    pub fn get_session(&self, request: &Request) -> Result<Option<Session>> {
        // Extract session ID from cookie
        let session_id = match self.extract_session_id(request) {
            Some(id) => id,
            None => return Ok(None),
        };
        
        // Validate session ID format
        if !self.id_generator.lock().unwrap().validate_id(&session_id) {
            return Ok(None);
        }
        
        // Get session data
        match self.store.get_session(&session_id)? {
            Some(data) => {
                // Parse JSON data
                let data: Value = serde_json::from_slice(&data)
                    .map_err(|_| ServerError::Session("Invalid session data".to_string()))?;
                
                Ok(Some(Session {
                    id: session_id,
                    data,
                }))
            }
            None => Ok(None),
        }
    }
    
    /// Save session data
    pub fn save_session(&self, session: &Session) -> Result<()> {
        // Serialize session data
        let data = serde_json::to_vec(&session.data)
            .map_err(|_| ServerError::Session("Failed to serialize session".to_string()))?;
        
        // Store session data
        self.store.store_session(&session.id, &data)
    }
    
    /// Destroy a session (logout)
    pub fn destroy_session(&self, session_id: &str, response: &mut Response) -> Result<()> {
        // Delete session from store
        self.store.delete_session(session_id)?;
        
        // Add cookie to delete the session cookie in the browser
        let removal_cookie = Cookie::build(self.get_cookie_name(), "")
            .domain(self.config.cookie_domain.clone())
            .path(self.config.cookie_path.clone())
            .max_age(Duration::seconds(-1))
            .http_only(true)
            .secure(self.config.secure_cookies)
            .finish();
        
        response.headers.insert(
            "set-cookie".to_string(),
            removal_cookie.to_string()
        );
        
        Ok(())
    }
    
    /// Regenerate session ID (for mitigating session fixation)
    pub fn regenerate_id(&self, old_session: &Session, response: &mut Response) -> Result<Session> {
        // Generate a new session ID
        let new_id = self.id_generator.lock().unwrap().generate();
        
        // Copy the old session data
        let data = old_session.data.clone();
        
        // Store the session with the new ID
        let serialized = serde_json::to_vec(&data)
            .map_err(|_| ServerError::Session("Failed to serialize session".to_string()))?;
        self.store.store_session(&new_id, &serialized)?;
        
        // Delete the old session
        self.store.delete_session(&old_session.id)?;
        
        // Set the new session cookie
        let cookie = self.create_session_cookie(&new_id);
        response.headers.insert(
            "set-cookie".to_string(),
            cookie.to_string()
        );
        
        Ok(Session {
            id: new_id,
            data,
        })
    }
    
    /// Create a session cookie with secure settings
    fn create_session_cookie(&self, session_id: &str) -> Cookie<'static> {
        let cookie_name = self.get_cookie_name();
        
        Cookie::build(cookie_name, session_id.to_owned())
            .domain(self.config.cookie_domain.clone())
            .path(self.config.cookie_path.clone())
            .http_only(true)
            .secure(self.config.secure_cookies)
            .same_site(SameSite::Lax)
            .max_age(Duration::hours(self.config.expiry_hours as i64))
            .finish()
    }
    
    /// Get cookie name with optional __Host- prefix
    fn get_cookie_name(&self) -> String {
        if self.config.use_cookie_prefix && !self.config.cookie_domain.contains('.') {
            format!("__Host-{}", self.config.cookie_name)
        } else {
            self.config.cookie_name.clone()
        }
    }
    
    /// Extract session ID from request cookies
    fn extract_session_id(&self, request: &Request) -> Option<String> {
        let cookie_header = request.headers.get("cookie")?;
        
        // Parse the cookie header
        for cookie_str in cookie_header.split(';') {
            let cookie_str = cookie_str.trim();
            if let Some((name, value)) = cookie_str.split_once('=') {
                let name = name.trim();
                let cookie_name = self.get_cookie_name();
                
                if name == cookie_name {
                    return Some(value.trim().to_string());
                }
                
                // Also check without prefix in case configuration changed
                if self.config.use_cookie_prefix && name == self.config.cookie_name {
                    return Some(value.trim().to_string());
                }
            }
        }
        
        None
    }
}
```

### CSRF Protection Integration

Integrate Cross-Site Request Forgery (CSRF) protection with session management:

```rust
/// CSRF protection integrated with session management
pub struct CsrfProtection {
    // Token expiry in seconds
    token_expiry: u64,
    // Session manager for storing tokens
    session_manager: Arc<SessionManager>,
}

impl CsrfProtection {
    /// Create new CSRF protection
    pub fn new(session_manager: Arc<SessionManager>) -> Self {
        Self {
            token_expiry: 3600, // 1 hour
            session_manager,
        }
    }
    
    /// Generate a new CSRF token for a session
    pub fn generate_token(&self, session: &mut Session) -> Result<String> {
        // Generate a secure random token
        let mut rng = ThreadRng::default();
        let mut bytes = [0u8; 32];
        rng.fill_bytes(&mut bytes);
        
        let token = BASE64_URL_SAFE_NO_PAD.encode(&bytes);
        let expires = Utc::now().timestamp() as u64 + self.token_expiry;
        
        // Store token in session with expiry
        let tokens = session.data.get_mut("csrf_tokens")
            .and_then(|v| v.as_object_mut())
            .unwrap_or_else(|| {
                session.data["csrf_tokens"] = json!({});
                session.data["csrf_tokens"].as_object_mut().unwrap()
            });
        
        tokens[&token] = json!(expires);
        
        // Save session with new token
        self.session_manager.save_session(session)?;
        
        Ok(token)
    }
    
    /// Validate a CSRF token from a request
    pub fn validate_token(&self, request: &Request, session: &Session) -> Result<bool> {
        // Get token from request
        let token = self.extract_token_from_request(request)?;
        
        // Get tokens from session
        let tokens = match session.data.get("csrf_tokens").and_then(|v| v.as_object()) {
            Some(tokens) => tokens,
            None => return Ok(false),
        };
        
        // Check if token exists and is valid
        match tokens.get(&token).and_then(|v| v.as_u64()) {
            Some(expires) => {
                // Check if token has expired
                let now = Utc::now().timestamp() as u64;
                if now > expires {
                    return Ok(false);
                }
                
                Ok(true)
            }
            None => Ok(false),
        }
    }
    
    /// Extract CSRF token from various places in the request
    fn extract_token_from_request(&self, request: &Request) -> Result<String> {
        // Try from header first (preferred method)
        if let Some(token) = request.headers.get("x-csrf-token") {
            return Ok(token.clone());
        }
        
        // If this is a form POST request, try from form data
        if request.method == Method::POST {
            if let Some(content_type) = request.headers.get("content-type") {
                if content_type.starts_with("application/x-www-form-urlencoded") {
                    // Parse form data
                    if let Ok(form_data) = form_urlencoded::parse(request.body.as_slice())
                        .into_owned()
                        .collect::<HashMap<String, String>>() {
                        
                        if let Some(token) = form_data.get("csrf_token") {
                            return Ok(token.clone());
                        }
                    }
                }
            }
        }
        
        // Finally, try from query string
        if let Some(query) = request.path.split('?').nth(1) {
            for pair in query.split('&') {
                if let Some((key, value)) = pair.split_once('=') {
                    if key == "csrf_token" {
                        return Ok(value.to_string());
                    }
                }
            }
        }
        
        Err(ServerError::Csrf("CSRF token not found in request".to_string()))
    }
    
    /// Clean up expired tokens to prevent session growth
    pub fn clean_expired_tokens(&self, session: &mut Session) -> Result<()> {
        let tokens = match session.data.get_mut("csrf_tokens").and_then(|v| v.as_object_mut()) {
            Some(tokens) => tokens,
            None => return Ok(()),
        };
        
        let now = Utc::now().timestamp() as u64;
        let expired_keys: Vec<String> = tokens.iter()
            .filter_map(|(key, value)| {
                if let Some(expires) = value.as_u64() {
                    if now > expires {
                        Some(key.clone())
                    } else {
                        None
                    }
                } else {
                    Some(key.clone()) // Remove invalid values
                }
            })
            .collect();
        
        for key in expired_keys {
            tokens.remove(&key);
        }
        
        if !tokens.is_empty() {
            self.session_manager.save_session(session)?;
        }
        
        Ok(())
    }
}
```

### Session Replay Protection

Implement protection against session replay attacks:

```rust
/// Session replay protection
pub struct ReplayProtection {
    // Nonce window size
    window_size: usize,
    // Session manager
    session_manager: Arc<SessionManager>,
}

impl ReplayProtection {
    /// Create new replay protection
    pub fn new(session_manager: Arc<SessionManager>) -> Self {
        Self {
            window_size: 100, // Track last 100 nonces
            session_manager,
        }
    }
    
    /// Generate a nonce for AJAX requests
    pub fn generate_nonce(&self, session: &mut Session) -> Result<String> {
        // Generate a secure random nonce
        let mut rng = ThreadRng::default();
        let mut bytes = [0u8; 16];
        rng.fill_bytes(&mut bytes);
        
        let nonce = BASE64_URL_SAFE_NO_PAD.encode(&bytes);
        
        // Store nonce in session
        let used_nonces = session.data.get_mut("used_nonces")
            .and_then(|v| v.as_array_mut())
            .unwrap_or_else(|| {
                session.data["used_nonces"] = json!([]);
                session.data["used_nonces"].as_array_mut().unwrap()
            });
        
        // Ensure window size is maintained
        while used_nonces.len() >= self.window_size {
            used_nonces.remove(0);
        }
        
        // Add new nonce
        used_nonces.push(json!(nonce.clone()));
        
        // Save session
        self.session_manager.save_session(session)?;
        
        Ok(nonce)
    }
    
    /// Validate a nonce from a request
    pub fn validate_nonce(&self, session: &mut Session, nonce: &str) -> Result<bool> {
        // Get used nonces from session
        let used_nonces = match session.data.get_mut("used_nonces").and_then(|v| v.as_array_mut()) {
            Some(nonces) => nonces,
            None => return Ok(false),
        };
        
        // Check if nonce exists in array
        for (i, n) in used_nonces.iter().enumerate() {
            if let Some(n) = n.as_str() {
                if n == nonce {
                    // Remove the nonce to prevent reuse
                    used_nonces.remove(i);
                    // Save session
                    self.session_manager.save_session(session)?;
                    return Ok(true);
                }
            }
        }
        
        Ok(false)
    }
}
```

### Session Monitoring for Attack Detection

Implement session monitoring to detect attack patterns:

```rust
/// Session activity monitor for detecting suspicious behavior
pub struct SessionMonitor {
    // Suspicious activity thresholds
    thresholds: SessionThresholds,
    // Tracking data by IP address
    ip_tracking: RwLock<HashMap<String, IpTrackingData>>,
    // Tracking data by session ID
    session_tracking: RwLock<HashMap<String, SessionTrackingData>>,
    // Blocked IPs
    blocked_ips: RwLock<HashMap<String, u64>>, // IP -> block expiry timestamp
}

/// Session activity thresholds
struct SessionThresholds {
    // Maximum failed login attempts before temporary block
    max_failed_logins: usize,
    // Maximum number of concurrent sessions per IP
    max_sessions_per_ip: usize,
    // Maximum session requests per minute
    max_requests_per_minute: usize,
    // Maximum session creation attempts per minute
    max_session_creations_per_minute: usize,
    // Maximum session switches per minute (potential session hijacking)
    max_session_switches_per_minute: usize,
}

impl SessionMonitor {
    /// Create a new session monitor
    pub fn new() -> Self {
        Self {
            thresholds: SessionThresholds {
                max_failed_logins: 5,
                max_sessions_per_ip: 10,
                max_requests_per_minute: 60,
                max_session_creations_per_minute: 10,
                max_session_switches_per_minute: 5,
            },
            ip_tracking: RwLock::new(HashMap::new()),
            session_tracking: RwLock::new(HashMap::new()),
            blocked_ips: RwLock::new(HashMap::new()),
        }
    }
    
    /// Record a request for monitoring
    pub fn record_request(
        &self,
        ip: &str,
        session_id: Option<&str>,
        user_agent: &str,
        path: &str,
        method: &Method
    ) -> Result<bool> {
        // Check if IP is blocked
        if self.is_ip_blocked(ip) {
            return Ok(false);
        }
        
        let now = Utc::now().timestamp() as u64;
        
        // Update IP tracking
        {
            let mut ip_data = self.ip_tracking.write().unwrap();
            let tracking = ip_data.entry(ip.to_owned()).or_insert_with(|| IpTrackingData {
                first_seen: now,
                last_seen: now,
                request_count: 0,
                session_count: 0,
                failed_logins: 0,
                session_creations: 0,
                session_ids: HashSet::new(),
                request_times: VecDeque::new(),
                session_creation_times: VecDeque::new(),
                session_switch_times: VecDeque::new(),
            });
            
            // Update tracking data
            tracking.last_seen = now;
            tracking.request_count += 1;
            tracking.request_times.push_back(now);
            
            // Keep only last minute of requests
            while let Some(&time) = tracking.request_times.front() {
                if now - time > 60 {
                    tracking.request_times.pop_front();
                } else {
                    break;
                }
            }
            
            // Record session ID if present
            if let Some(session_id) = session_id {
                let is_new_session = tracking.session_ids.insert(session_id.to_owned());
                
                if is_new_session {
                    tracking.session_count += 1;
                    tracking.session_switch_times.push_back(now);
                    
                    // Keep only last minute of session switches
                    while let Some(&time) = tracking.session_switch_times.front() {
                        if now - time > 60 {
                            tracking.session_switch_times.pop_front();
                        } else {
                            break;
                        }
                    }
                }
            }
            
            // Check for request rate limiting
            if tracking.request_times.len() > self.thresholds.max_requests_per_minute {
                log::warn!("Excessive request rate from IP: {}", ip);
                self.block_ip(ip, 300); // Block for 5 minutes
                return Ok(false);
            }
            
            // Check for session switching (potential hijacking)
            if tracking.session_switch_times.len() > self.thresholds.max_session_switches_per_minute {
                log::warn!("Excessive session switching from IP: {}", ip);
                self.block_ip(ip, 600); // Block for 10 minutes
                return Ok(false);
            }
            
            // Check for excessive concurrent sessions
            if tracking.session_count > self.thresholds.max_sessions_per_ip {
                log::warn!("Too many concurrent sessions from IP: {}", ip);
                self.block_ip(ip, 600); // Block for 10 minutes
                return Ok(false);
            }
        }
        
        // Update session tracking if session ID is present
        if let Some(session_id) = session_id {
            let mut session_data = self.session_tracking.write().unwrap();
            let tracking = session_data.entry(session_id.to_owned()).or_insert_with(|| SessionTrackingData {
                first_seen: now,
                last_seen: now,
                ips: HashSet::new(),
                user_agents: HashSet::new(),
                request_count: 0,
            });
            
            // Update tracking data
            tracking.last_seen = now;
            tracking.request_count += 1;
            tracking.ips.insert(ip.to_owned());
            tracking.user_agents.insert(user_agent.to_owned());
            
            // Check for session hijacking indicators (IP/UA changes)
            if tracking.ips.len() > 2 || tracking.user_agents.len() > 2 {
                log::warn!("Possible session hijacking detected for session ID: {}", session_id);
                // In a real system, you might invalidate the session here
                // We'll just log it for this example
            }
        }
        
        Ok(true)
    }
    
    /// Record a failed login attempt
    pub fn record_failed_login(&self, ip: &str) -> Result<bool> {
        let now = Utc::now().timestamp() as u64;
        
        // Update IP tracking
        {
            let mut ip_data = self.ip_tracking.write().unwrap();
            let tracking = ip_data.entry(ip.to_owned()).or_insert_with(|| IpTrackingData {
                first_seen: now,
                last_seen: now,
                request_count: 0,
                session_count: 0,
                failed_logins: 0,
                session_creations: 0,
                session_ids: HashSet::new(),
                request_times: VecDeque::new(),
                session_creation_times: VecDeque::new(),
                session_switch_times: VecDeque::new(),
            });
            
            tracking.failed_logins += 1;
            tracking.last_seen = now;
            
            // Check for brute force attempts
            if tracking.failed_logins >= self.thresholds.max_failed_logins {
                log::warn!("Too many failed logins from IP: {}", ip);
                self.block_ip(ip, 1800); // Block for 30 minutes
                return Ok(false);
            }
        }
        
        Ok(true)
    }
    
    /// Record a session creation
    pub fn record_session_creation(&self, ip: &str) -> Result<bool> {
        let now = Utc::now().timestamp() as u64;
        
        // Check if IP is blocked
        if self.is_ip_blocked(ip) {
            return Ok(false);
        }
        
        // Update IP tracking
        {
            let mut ip_data = self.ip_tracking.write().unwrap();
            let tracking = ip_data.entry(ip.to_owned()).or_insert_with(|| IpTrackingData {
                first_seen: now,
                last_seen: now,
                request_count: 0,
                session_count: 0,
                failed_logins: 0,
                session_creations: 0,
                session_ids: HashSet::new(),
                request_times: VecDeque::new(),
                session_creation_times: VecDeque::new(),
                session_switch_times: VecDeque::new(),
            });
            
            tracking.last_seen = now;
            tracking.session_creations += 1;
            tracking.session_creation_times.push_back(now);
            
            // Keep only last minute of session creations
            while let Some(&time) = tracking.session_creation_times.front() {
                if now - time > 60 {
                    tracking.session_creation_times.pop_front();
                } else {
                    break;
                }
            }
            
            // Check for session creation rate
            if tracking.session_creation_times.len() > self.thresholds.max_session_creations_per_minute {
                log::warn!("Excessive session creation rate from IP: {}", ip);
                self.block_ip(ip, 300); // Block for 5 minutes
                return Ok(false);
            }
        }
        
        Ok(true)
    }
    
    /// Block an IP address for a specified duration in seconds
    fn block_ip(&self, ip: &str, duration: u64) {
        let now = Utc::now().timestamp() as u64;
        let expiry = now + duration;
        
        let mut blocked = self.blocked_ips.write().unwrap();
        blocked.insert(ip.to_owned(), expiry);
        
        log::warn!("IP {} blocked until timestamp {}", ip, expiry);
    }
    
    /// Check if an IP is blocked
    fn is_ip_blocked(&self, ip: &str) -> bool {
        let now = Utc::now().timestamp() as u64;
        let mut blocked = self.blocked_ips.write().unwrap();
        
        if let Some(&expiry) = blocked.get(ip) {
            if now < expiry {
                return true;
            } else {
                // Remove expired block
                blocked.remove(ip);
            }
        }
        
        false
    }
    
    /// Clean up old tracking data
    pub fn cleanup(&self) {
        let now = Utc::now().timestamp() as u64;
        const CLEANUP_THRESHOLD: u64 = 3600; // 1 hour
        
        // Clean up IP tracking
        {
            let mut ip_data = self.ip_tracking.write().unwrap();
            ip_data.retain(|_, data| now - data.last_seen < CLEANUP_THRESHOLD);
        }
        
        // Clean up session tracking
        {
            let mut session_data = self.session_tracking.write().unwrap();
            session_data.retain(|_, data| now - data.last_seen < CLEANUP_THRESHOLD);
        }
        
        // Clean up blocked IPs
        {
            let mut blocked = self.blocked_ips.write().unwrap();
            blocked.retain(|_, &mut expiry| now < expiry);
        }
    }
}
```

### Best Practices for Session Security

1. **Use Secure Session IDs**:
   - Generate cryptographically secure random session IDs
   - Ensure sufficient ID length (at least 128 bits)
   - Validate session ID format before use

2. **Implement Proper Cookie Settings**:
   - Always use HttpOnly, Secure, and SameSite flags
   - Consider cookie prefixes (__Host- or __Secure-)
   - Set appropriate expiration times

3. **Encrypt Session Data**:
   - Encrypt all session data at rest
   - Use authenticated encryption (e.g., XChaCha20-Poly1305, AES-GCM)
   - Securely manage encryption keys

4. **Protect Against Session Fixation**:
   - Regenerate session IDs after authentication
   - Create new session IDs for privilege level changes
   - Validate session IDs against a whitelist

5. **Implement CSRF Protection**:
   - Generate and validate anti-CSRF tokens
   - Use double-submit cookie pattern
   - Validate the Origin/Referer headers

6. **Apply Session Lifecycle Management**:
   - Set appropriate session timeouts (idle and absolute)
   - Allow users to view and terminate active sessions
   - Implement secure logout procedures

7. **Monitor for Suspicious Activity**:
   - Track login attempts and failures
   - Monitor multiple concurrent sessions
   - Watch for rapid session switching
   - Detect anomalous behavior patterns

8. **Implement Session Compartmentalization**:
   - Use different sessions for different security levels
   - Implement step-up authentication for sensitive operations
   - Reset sensitive session data after use

## Integration
- Integrate with authentication and plugin systems.

## Quiz
1. What is the difference between a session and a cookie?
2. How can you secure cookies?

## Diagram
```mermaid
graph TD
    A[Client] -- Cookie --> B[Webserver]
    B -- Session Lookup --> C[Session Store]
```
