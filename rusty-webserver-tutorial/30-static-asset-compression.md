# Static Asset Compression

## Learning Objectives
- Understand the benefits of static asset compression in web applications
- Implement on-the-fly compression for HTTP responses in Rust
- Create pre-compression workflows for static assets
- Configure compression middleware with optimal settings
- Apply content negotiation for compression algorithms

## Prerequisites
- Understanding of HTTP headers and content negotiation
- Knowledge of static file serving in web applications
- Familiarity with basic performance optimization concepts

## Introduction

Static asset compression is a key technique for improving web application performance. By reducing the size of transmitted files, compression decreases page load times, reduces bandwidth consumption, and improves user experience. This module covers implementing efficient compression techniques for static assets in a Rust webserver, both for on-demand compression and pre-compressed assets.

## Compression Fundamentals

### Key Concepts

- **Compression Algorithms**: Methods for reducing file size (e.g., gzip, Brotli, Deflate, Zstandard)
- **Content Encoding**: HTTP headers that indicate compression type
- **Compression Ratio**: The reduction in file size achieved by compression
- **Compression Level**: Trade-off between compression ratio and CPU usage
- **Content Negotiation**: Selecting the best compression algorithm based on client support

### Compression Algorithm Comparison

| Algorithm | Compression Ratio | CPU Usage | Browser Support | Best For |
|-----------|------------------|-----------|----------------|----------|
| Gzip      | Medium           | Low       | Excellent      | General purpose |
| Brotli    | High             | High      | Good           | Text files (HTML, CSS, JS) |
| Deflate   | Low              | Very Low  | Excellent      | Legacy systems |
| Zstandard | High             | Medium    | Limited        | Server-to-server |

## Dynamic Compression in Rust

### Using Tower-HTTP Compression Middleware

The Tower ecosystem provides middleware for handling compression in Rust web applications:

```rust
use axum::{
    Router,
    routing::get,
    response::IntoResponse,
    http::{header, StatusCode},
};
use tower_http::compression::{CompressionLayer, CompressionLevel};
use tower_http::services::ServeDir;
use std::path::PathBuf;

#[tokio::main]
async fn main() {
    // Create a compression layer with default settings (gzip, deflate, br)
    let compression_layer = CompressionLayer::new()
        .quality(CompressionLevel::Default)
        .no_br(); // Disable Brotli if you want to save CPU
    
    // Create static file service with compression
    let assets_path = PathBuf::from(env!("CARGO_MANIFEST_DIR"))
        .join("assets");
    
    let static_files_service = ServeDir::new(assets_path)
        .append_index_html_on_directories(true);
    
    // Create router with compression middleware
    let app = Router::new()
        .nest_service("/assets", static_files_service)
        .route("/", get(index_handler))
        .layer(compression_layer);
    
    // Start server
    let addr = "0.0.0.0:3000".parse().unwrap();
    println!("Listening on http://{}", addr);
    
    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await
        .unwrap();
}

async fn index_handler() -> impl IntoResponse {
    let html = r#"<!DOCTYPE html>
    <html>
    <head>
        <title>Compressed Page</title>
        <link rel="stylesheet" href="/assets/style.css">
        <script src="/assets/app.js" defer></script>
    </head>
    <body>
        <h1>Hello, Compressed World!</h1>
        <p>This page and its assets are being served with compression.</p>
    </body>
    </html>"#;
    
    (
        StatusCode::OK,
        [(header::CONTENT_TYPE, "text/html; charset=utf-8")],
        html
    )
}
```

### Custom Compression Implementation

For more precise control, you can implement compression directly:

```rust
use axum::{
    extract::Extension,
    middleware::{self, Next},
    response::{IntoResponse, Response},
    routing::get,
    Router,
    http::{HeaderMap, HeaderValue, Request, StatusCode, header},
    body::{Body, BoxBody, HttpBody},
};
use bytes::Bytes;
use flate2::{write::GzEncoder, Compression};
use brotli::enc::writer::CompressorWriter;
use futures::stream::StreamExt;
use std::io::Write;
use std::sync::Arc;

// Custom compression middleware
async fn compress_response<B>(
    request: Request<B>,
    next: Next<B>,
) -> Result<Response, StatusCode>
where
    B: HttpBody + Send + 'static,
    B::Data: Send,
    B::Error: Into<BoxError>,
{
    // Check if client supports compression
    let accept_encoding = request
        .headers()
        .get(header::ACCEPT_ENCODING)
        .and_then(|v| v.to_str().ok())
        .unwrap_or("");
    
    // Determine supported algorithms
    let use_brotli = accept_encoding.contains("br");
    let use_gzip = accept_encoding.contains("gzip");
    let use_deflate = accept_encoding.contains("deflate");
    
    // Skip compression for certain content types or small responses
    let response = next.run(request).await;
    
    // Check content type for compressible media
    if let Some(content_type) = response.headers().get(header::CONTENT_TYPE) {
        let content_type = content_type.to_str().unwrap_or("");
        
        // Skip compression for already compressed formats
        if content_type.starts_with("image/") && !content_type.starts_with("image/svg")
            || content_type.starts_with("audio/")
            || content_type.starts_with("video/")
            || content_type.contains("zip")
            || content_type.contains("compressed")
            || content_type.contains("ogg")
            || content_type.contains("mp4")
        {
            return Ok(response);
        }
    }
    
    // Get response body
    let (parts, body) = response.into_parts();
    let bytes = hyper::body::to_bytes(body).await.map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    
    // Skip compression for small responses
    if bytes.len() < 1024 {
        return Ok(Response::from_parts(parts, Body::from(bytes)));
    }
    
    // Apply compression based on what the client supports
    if use_brotli {
        let mut encoder = CompressorWriter::new(Vec::new(), 4096, 11, 22);
        encoder.write_all(&bytes).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        let compressed = encoder.into_inner();
        
        let mut new_headers = parts.headers.clone();
        new_headers.insert(header::CONTENT_ENCODING, HeaderValue::from_static("br"));
        
        let mut new_parts = parts;
        new_parts.headers = new_headers;
        
        Ok(Response::from_parts(new_parts, Body::from(compressed)))
    } else if use_gzip {
        let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(&bytes).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        let compressed = encoder.finish().map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        
        let mut new_headers = parts.headers.clone();
        new_headers.insert(header::CONTENT_ENCODING, HeaderValue::from_static("gzip"));
        
        let mut new_parts = parts;
        new_parts.headers = new_headers;
        
        Ok(Response::from_parts(new_parts, Body::from(compressed)))
    } else if use_deflate {
        let mut encoder = flate2::write::DeflateEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(&bytes).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        let compressed = encoder.finish().map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        
        let mut new_headers = parts.headers.clone();
        new_headers.insert(header::CONTENT_ENCODING, HeaderValue::from_static("deflate"));
        
        let mut new_parts = parts;
        new_parts.headers = new_headers;
        
        Ok(Response::from_parts(new_parts, Body::from(compressed)))
    } else {
        // No supported compression method
        Ok(Response::from_parts(parts, Body::from(bytes)))
    }
}

// Router setup with custom compression middleware
let app = Router::new()
    .route("/", get(index_handler))
    .layer(middleware::from_fn(compress_response));
```

## Pre-Compression of Static Assets

Pre-compressing static assets at build time is more efficient than on-the-fly compression:

```rust
use std::fs::{self, File};
use std::io::{self, Read, Write};
use std::path::{Path, PathBuf};
use flate2::write::GzEncoder;
use flate2::Compression;
use brotli::enc::BrotliEncoderParams;
use walkdir::WalkDir;

// Compression utility for build scripts
fn precompress_assets() -> io::Result<()> {
    // Define paths
    let assets_dir = PathBuf::from("assets");
    
    // Ensure assets directory exists
    if !assets_dir.exists() {
        return Err(io::Error::new(io::ErrorKind::NotFound, "Assets directory not found"));
    }
    
    // Walk the assets directory
    for entry in WalkDir::new(&assets_dir).into_iter().filter_map(|e| e.ok()) {
        let path = entry.path();
        
        // Skip directories
        if path.is_dir() {
            continue;
        }
        
        // Only compress text-based files
        let extension = path.extension().and_then(|ext| ext.to_str()).unwrap_or("");
        if !is_compressible_extension(extension) {
            continue;
        }
        
        println!("Pre-compressing: {}", path.display());
        
        // Read file content
        let mut content = Vec::new();
        File::open(path)?.read_to_end(&mut content)?;
        
        // Create gzip version
        let gzip_path = format!("{}.gz", path.to_string_lossy());
        let mut encoder = GzEncoder::new(Vec::new(), Compression::best());
        encoder.write_all(&content)?;
        fs::write(gzip_path, encoder.finish()?)?;
        
        // Create brotli version
        let br_path = format!("{}.br", path.to_string_lossy());
        let mut output = Vec::new();
        let params = BrotliEncoderParams {
            quality: 11, // Maximum quality
            lgwin: 22,   // Large window for better compression
            ..Default::default()
        };
        brotli::encode::BrotliCompress(&mut &content[..], &mut output, &params)?;
        fs::write(br_path, output)?;
    }
    
    Ok(())
}

// Helper to determine if a file type should be compressed
fn is_compressible_extension(extension: &str) -> bool {
    match extension {
        "html" | "css" | "js" | "json" | "xml" | "svg" | "txt" | "md" |
        "map" | "woff" | "woff2" | "eot" | "ttf" | "otf" => true,
        _ => false,
    }
}

// Usage in build.rs
fn main() {
    println!("Pre-compressing static assets...");
    if let Err(e) = precompress_assets() {
        eprintln!("Error pre-compressing assets: {}", e);
    }
}
```

### Serving Pre-Compressed Assets

```rust
use std::path::{Path, PathBuf};
use std::sync::Arc;
use axum::{
    extract::{Path as PathExtract, State},
    http::{header, HeaderMap, StatusCode},
    response::IntoResponse,
    routing::get,
    Router,
};

struct AssetServer {
    root: PathBuf,
}

impl AssetServer {
    fn new<P: AsRef<Path>>(root: P) -> Self {
        Self {
            root: root.as_ref().to_path_buf(),
        }
    }
    
    async fn serve_file(&self, path: &str, headers: &HeaderMap) -> impl IntoResponse {
        // Normalize path to prevent directory traversal
        let mut normalized_path = PathBuf::new();
        for component in Path::new(path).components() {
            match component {
                std::path::Component::Normal(c) => normalized_path.push(c),
                _ => {} // Skip other components for security
            }
        }
        
        let file_path = self.root.join(normalized_path);
        if !file_path.exists() {
            return (StatusCode::NOT_FOUND, "File not found").into_response();
        }
        
        // Check if client accepts compressed formats
        let accept_encoding = headers
            .get(header::ACCEPT_ENCODING)
            .and_then(|v| v.to_str().ok())
            .unwrap_or("");
        
        // Check for pre-compressed versions
        if accept_encoding.contains("br") {
            let br_path = format!("{}.br", file_path.to_string_lossy());
            if Path::new(&br_path).exists() {
                let content = tokio::fs::read(br_path).await.unwrap_or_default();
                return (
                    StatusCode::OK,
                    [
                        (header::CONTENT_ENCODING, "br"),
                        (header::CONTENT_TYPE, mime_for_path(&file_path)),
                        (header::CACHE_CONTROL, "max-age=31536000"),
                    ],
                    content,
                )
                .into_response();
            }
        }
        
        if accept_encoding.contains("gzip") {
            let gzip_path = format!("{}.gz", file_path.to_string_lossy());
            if Path::new(&gzip_path).exists() {
                let content = tokio::fs::read(gzip_path).await.unwrap_or_default();
                return (
                    StatusCode::OK,
                    [
                        (header::CONTENT_ENCODING, "gzip"),
                        (header::CONTENT_TYPE, mime_for_path(&file_path)),
                        (header::CACHE_CONTROL, "max-age=31536000"),
                    ],
                    content,
                )
                .into_response();
            }
        }
        
        // Fall back to uncompressed version
        let content = tokio::fs::read(&file_path).await.unwrap_or_default();
        (
            StatusCode::OK,
            [
                (header::CONTENT_TYPE, mime_for_path(&file_path)),
                (header::CACHE_CONTROL, "max-age=31536000"),
            ],
            content,
        )
        .into_response()
    }
}

// Helper to determine MIME type
fn mime_for_path(path: &Path) -> &'static str {
    let extension = path.extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("");
    
    match extension {
        "html" => "text/html; charset=utf-8",
        "css" => "text/css; charset=utf-8",
        "js" => "application/javascript; charset=utf-8",
        "json" => "application/json; charset=utf-8",
        "svg" => "image/svg+xml",
        "png" => "image/png",
        "jpg" | "jpeg" => "image/jpeg",
        "gif" => "image/gif",
        "woff" => "font/woff",
        "woff2" => "font/woff2",
        "ttf" => "font/ttf",
        "otf" => "font/otf",
        _ => "application/octet-stream",
    }
}

// Router setup
let assets_server = Arc::new(AssetServer::new("./assets"));

let app = Router::new()
    .route("/assets/*path", get(|
        PathExtract(path): PathExtract<String>,
        headers: HeaderMap,
        State(assets_server): State<Arc<AssetServer>>
    | async move {
        assets_server.serve_file(&path, &headers).await
    }))
    .with_state(assets_server);
```

## Compression Strategies

### Adaptive Compression

Adjust compression level based on server load:

```rust
use std::sync::atomic::{AtomicU8, Ordering};
use tokio::time::interval;
use std::time::Duration;
use std::sync::Arc;

// Adaptive compression manager
struct AdaptiveCompression {
    level: AtomicU8,
    cpu_usage_threshold: f32,
}

impl AdaptiveCompression {
    fn new(initial_level: u8, cpu_threshold: f32) -> Arc<Self> {
        let compression = Arc::new(Self {
            level: AtomicU8::new(initial_level),
            cpu_usage_threshold: cpu_threshold,
        });
        
        // Spawn background task to adjust compression level
        let compression_clone = compression.clone();
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(30));
            loop {
                interval.tick().await;
                compression_clone.adjust_level().await;
            }
        });
        
        compression
    }
    
    async fn adjust_level(&self) {
        // Get current CPU usage (simplified example)
        let cpu_usage = get_system_cpu_usage().await;
        
        let current_level = self.level.load(Ordering::Relaxed);
        
        if cpu_usage > self.cpu_usage_threshold && current_level > 1 {
            // Reduce compression level when CPU is high
            self.level.store(current_level - 1, Ordering::Relaxed);
            println!("High CPU usage ({}%), reducing compression to level {}", 
                     cpu_usage, current_level - 1);
        } else if cpu_usage < self.cpu_usage_threshold * 0.7 && current_level < 9 {
            // Increase compression when CPU is low
            self.level.store(current_level + 1, Ordering::Relaxed);
            println!("Low CPU usage ({}%), increasing compression to level {}", 
                     cpu_usage, current_level + 1);
        }
    }
    
    fn current_level(&self) -> u8 {
        self.level.load(Ordering::Relaxed)
    }
}

// Sample implementation to get CPU usage
async fn get_system_cpu_usage() -> f32 {
    // In a real implementation, you would use a crate like sysinfo or sys-info
    // to get actual system metrics
    
    // For this example, simulate variable CPU load
    let mut random = rand::thread_rng();
    rand::Rng::gen_range(&mut random, 30.0..80.0)
}

// Usage in middleware
let adaptive_compression = AdaptiveCompression::new(6, 70.0);

async fn compress_with_adaptive_level(
    data: &[u8], 
    encoding: &str,
    compression: &AdaptiveCompression
) -> Vec<u8> {
    let level = compression.current_level();
    
    match encoding {
        "gzip" => {
            let mut encoder = GzEncoder::new(
                Vec::new(), 
                Compression::new(level.into())
            );
            encoder.write_all(data).unwrap();
            encoder.finish().unwrap()
        },
        "br" => {
            let mut output = Vec::new();
            let params = BrotliEncoderParams {
                quality: level as i32,
                lgwin: 22,
                ..Default::default()
            };
            brotli::encode::BrotliCompress(&mut &data[..], &mut output, &params).unwrap();
            output
        },
        _ => data.to_vec(),
    }
}
```

### Conditional Compression

Avoid compressing already compressed content:

```rust
// Helper to check if content should be compressed
fn should_compress(content_type: &str, size: usize) -> bool {
    // Skip small files
    if size < 1024 {
        return false;
    }
    
    // Check content type
    match content_type {
        // Text-based formats - good for compression
        t if t.starts_with("text/") => true,
        
        // Application formats - check specific subtypes
        t if t.starts_with("application/") => {
            t.contains("json") || 
            t.contains("javascript") || 
            t.contains("xml") || 
            t.contains("html") ||
            t.contains("svg+xml")
        },
        
        // Image formats - only compress SVG
        t if t.starts_with("image/") => t.contains("svg"),
        
        // Font formats - some benefit from compression
        t if t.starts_with("font/") || t.contains("font") => true,
        
        // Everything else - don't compress
        _ => false,
    }
}

// Usage in middleware
let content_type = headers.get(header::CONTENT_TYPE)
    .and_then(|v| v.to_str().ok())
    .unwrap_or("application/octet-stream");

if should_compress(content_type, body_bytes.len()) {
    // Apply compression
} else {
    // Skip compression
}
```

## Best Practices

### Optimal Compression Settings

```rust
// Gzip compression level guidelines
let gzip_level = match file_type {
    "text" | "html" | "css" | "js" => Compression::best(), // Level 9
    "json" | "xml" => Compression::default(),              // Level 6
    _ => Compression::fast(),                              // Level 1
};

// Brotli compression settings
let brotli_quality = match file_type {
    "text" | "html" | "css" | "js" => 11, // Maximum quality
    "json" | "xml" => 7,                 // Medium quality
    _ => 4,                              // Lower quality for other types
};

let brotli_window = match content_size {
    size if size > 1_000_000 => 24,  // Large window for large files
    size if size > 100_000 => 22,    // Medium window
    _ => 20,                         // Small window for small files
};
```

### Cache Control with Compression

Make sure to set appropriate caching headers for compressed content:

```rust
// For pre-compressed assets with content hash in filename
let cache_control = "public, max-age=31536000, immutable";

// For dynamically compressed content
let cache_control = "public, max-age=3600";

headers.insert(header::CACHE_CONTROL, HeaderValue::from_static(cache_control));
```

### Compression and HTTP/2

HTTP/2 has its own compression for headers, but still benefits from body compression:

```rust
// Allow tower_http to handle HTTP/2 specifics
let http2_only = cfg!(feature = "http2");

// Configure tower-http compression layer
let compression_layer = if http2_only {
    // For HTTP/2, prioritize Brotli which works well with multiplexing
    CompressionLayer::new()
        .br(true)
        .gzip(true)
        .deflate(false)
        .quality(CompressionLevel::Balanced)
} else {
    // For HTTP/1.x, use broader compatibility
    CompressionLayer::new()
        .br(true)
        .gzip(true)
        .deflate(true)
        .quality(CompressionLevel::Default)
};
```

## Knowledge Check

1. What compression algorithm typically provides the best compression ratio for text-based assets?
2. Why is pre-compression often preferred over on-the-fly compression?
3. How should you determine whether a particular asset should be compressed?
4. What HTTP header indicates to a client that content is compressed?
5. How would you implement an adaptive compression strategy that adjusts based on server load?
6. What types of files should generally not be compressed and why?

## Additional Resources

- [MDN: HTTP Compression](https://developer.mozilla.org/en-US/docs/Web/HTTP/Compression)
- [Rust flate2 Crate Documentation](https://docs.rs/flate2/)
- [Rust brotli Crate Documentation](https://docs.rs/brotli/)
- [HTTP Content Negotiation](https://developer.mozilla.org/en-US/docs/Web/HTTP/Content_negotiation)
- [Tower-HTTP Compression Documentation](https://docs.rs/tower-http/latest/tower_http/compression/)

## Diagram: Compression Decision Flow

```mermaid
flowchart TD
    A[HTTP Request] --> B{Check Accept-Encoding}
    B -->|br supported| C{Pre-compressed .br exists?}
    C -->|Yes| D[Serve .br file]
    C -->|No| E{Brotli on-the-fly?}
    E -->|Yes| F[Compress with Brotli]
    E -->|No| G{gzip supported?}
    B -->|br not supported| G
    G -->|Yes| H{Pre-compressed .gz exists?}
    H -->|Yes| I[Serve .gz file]
    H -->|No| J{Gzip on-the-fly?}
    J -->|Yes| K[Compress with Gzip]
    J -->|No| L[Serve uncompressed]
    G -->|No| L
    D --> M[Set Content-Encoding]
    F --> M
    I --> M
    K --> M
    L --> N[Set Content-Type]
    M --> N
    N --> O[Response]
```
