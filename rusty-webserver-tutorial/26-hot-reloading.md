# Hot Reloading and Live Code Updates

## Learning Objectives
- Understand the concept and benefits of hot reloading in web development
- Implement file watching mechanisms in Rust
- Design reloadable components in your webserver architecture
- Configure hot reloading for different types of assets and code
- Optimize development workflow with automatic reloading techniques

## Prerequisites
- Understanding of the webserver architecture from previous modules
- Familiarity with Rust's module system and dynamic loading capabilities
- Basic knowledge of file system operations

## Introduction

Hot reloading is a development technique that allows you to see changes to your code or assets without manually restarting your server. This dramatically speeds up the development cycle by providing near-instant feedback. This module covers how to implement hot reloading in your Rust webserver for both static assets and runtime code.

## Hot Reloading Fundamentals

### Types of Hot Reloading

1. **Static Asset Reloading**: Detects changes to static files like HTML, CSS, JavaScript, and images, and makes them immediately available.

2. **Template Reloading**: Recompiles templates when they change without restarting the server.

3. **Configuration Reloading**: Updates server configuration on-the-fly.

4. **Code Reloading**: The most complex type, which dynamically reloads Rust code while the server is running.

## Implementing File Watching in Rust

Let's start by creating a file watcher that monitors changes to files:

```rust
use notify::{RecommendedWatcher, RecursiveMode, Result, Watcher, Event};
use std::path::{Path, PathBuf};
use std::sync::mpsc::{channel, Receiver};
use std::time::Duration;

// File types to monitor
#[derive(Debug, Clone, PartialEq)]
pub enum FileType {
    Static,
    Template,
    Config,
    Source,
}

// File change event
#[derive(Debug)]
pub struct FileChangeEvent {
    pub path: PathBuf,
    pub file_type: FileType,
}

pub struct HotReloader {
    rx: Receiver<notify::Result<Event>>,
    _watcher: RecommendedWatcher, // Keep watcher alive
}

impl HotReloader {
    pub fn new<P: AsRef<Path>>(paths: Vec<(P, FileType)>) -> Result<Self> {
        // Create a channel to receive events
        let (tx, rx) = channel();
        
        // Create a watcher with default configuration
        let mut watcher = notify::recommended_watcher(move |res| {
            tx.send(res).unwrap();
        })?;
        
        // Add paths to watch
        for (path, _) in &paths {
            watcher.watch(path.as_ref(), RecursiveMode::Recursive)?;
        }
        
        Ok(Self {
            rx,
            _watcher: watcher,
        })
    }
    
    pub fn watch(&self, paths_info: &[(PathBuf, FileType)]) -> Receiver<FileChangeEvent> {
        let (tx, rx) = std::sync::mpsc::channel();
        
        let paths_info = paths_info.to_vec();
        
        // Spawn a thread to handle file change events
        std::thread::spawn(move || {
            let rx = &self.rx;
            
            loop {
                match rx.recv_timeout(Duration::from_millis(100)) {
                    Ok(Ok(event)) => {
                        // Process file change events
                        for path in event.paths {
                            // Determine file type based on path
                            for (watch_path, file_type) in &paths_info {
                                if path.starts_with(watch_path) {
                                    // Send file change event
                                    let _ = tx.send(FileChangeEvent {
                                        path: path.clone(),
                                        file_type: file_type.clone(),
                                    });
                                    break;
                                }
                            }
                        }
                    }
                    Ok(Err(e)) => eprintln!("Watch error: {:?}", e),
                    Err(std::sync::mpsc::RecvTimeoutError::Timeout) => {
                        // Timeout is normal, continue polling
                    }
                    Err(e) => {
                        eprintln!("Watch channel error: {:?}", e);
                        break;
                    }
                }
            }
        });
        
        rx
    }
}

// Usage example
fn main() -> Result<()> {
    // Define paths to watch
    let paths = vec![
        (PathBuf::from("./static"), FileType::Static),
        (PathBuf::from("./templates"), FileType::Template),
        (PathBuf::from("./config"), FileType::Config),
    ];
    
    // Create hot reloader
    let hot_reloader = HotReloader::new(paths.clone())?;
    
    // Start watching for changes
    let rx = hot_reloader.watch(&paths);
    
    println!("Watching for file changes...");
    
    // Process file change events
    for event in rx {
        match event.file_type {
            FileType::Static => println!("Static file changed: {:?}", event.path),
            FileType::Template => println!("Template changed: {:?}", event.path),
            FileType::Config => println!("Config changed: {:?}", event.path),
            FileType::Source => println!("Source code changed: {:?}", event.path),
        }
        
        // Handle the change based on file type
        // ...
    }
    
    Ok(())
}
```

## Asset Hot Reloading

### Static Assets Cache with Auto-Invalidation

Create a cache for static assets that automatically refreshes when files change:

```rust
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};

// Cached asset
struct CachedAsset {
    content: Vec<u8>,
    mime_type: String,
    last_modified: SystemTime,
}

// Static asset cache with auto-invalidation
pub struct StaticAssetCache {
    assets: RwLock<HashMap<String, CachedAsset>>,
    base_dir: PathBuf,
}

impl StaticAssetCache {
    pub fn new<P: AsRef<Path>>(base_dir: P) -> Self {
        Self {
            assets: RwLock::new(HashMap::new()),
            base_dir: base_dir.as_ref().to_path_buf(),
        }
    }
    
    // Get asset, loading from disk if needed or if file changed
    pub fn get_asset(&self, path: &str) -> Option<(Vec<u8>, String)> {
        let full_path = self.base_dir.join(path);
        
        // Check if file exists on disk
        if !full_path.exists() {
            return None;
        }
        
        // Get file metadata
        let metadata = match fs::metadata(&full_path) {
            Ok(meta) => meta,
            Err(_) => return None,
        };
        
        // Get last modified time
        let last_modified = metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH);
        
        // First try read lock to check if cached version is valid
        {
            let assets = self.assets.read().unwrap();
            if let Some(asset) = assets.get(path) {
                // If cached version is still fresh, return it
                if asset.last_modified >= last_modified {
                    return Some((asset.content.clone(), asset.mime_type.clone()));
                }
            }
        }
        
        // Need to load or reload the file
        let content = match fs::read(&full_path) {
            Ok(bytes) => bytes,
            Err(_) => return None,
        };
        
        // Determine MIME type
        let mime_type = mime_guess::from_path(&full_path)
            .first_or_octet_stream()
            .to_string();
            
        // Update cache with write lock
        {
            let mut assets = self.assets.write().unwrap();
            assets.insert(
                path.to_string(),
                CachedAsset {
                    content: content.clone(),
                    mime_type: mime_type.clone(),
                    last_modified,
                },
            );
        }
        
        Some((content, mime_type))
    }
    
    // Explicitly invalidate a cache entry
    pub fn invalidate(&self, path: &str) {
        let mut assets = self.assets.write().unwrap();
        assets.remove(path);
    }
    
    // Invalidate all cache entries
    pub fn invalidate_all(&self) {
        let mut assets = self.assets.write().unwrap();
        assets.clear();
    }
}

// Integration with file watcher for auto-invalidation
pub fn setup_asset_hot_reloading(
    asset_cache: Arc<StaticAssetCache>,
    hot_reloader: &HotReloader,
    static_dir: &Path,
) -> Receiver<FileChangeEvent> {
    let paths = vec![(static_dir.to_path_buf(), FileType::Static)];
    let rx = hot_reloader.watch(&paths);
    
    let static_dir = static_dir.to_path_buf();
    
    // Handle file changes in a separate thread
    std::thread::spawn(move || {
        for event in rx {
            if let Ok(rel_path) = event.path.strip_prefix(&static_dir) {
                let path_str = rel_path.to_string_lossy().to_string();
                println!("Invalidating cached asset: {}", path_str);
                asset_cache.invalidate(&path_str);
            }
        }
    });
    
    rx
}
```

## Template Hot Reloading

### Auto-recompiling Template Engine

Create a template engine that automatically recompiles templates when they change:

```rust
use tera::{Context, Tera};
use std::sync::{Arc, RwLock};
use std::path::Path;

// Hot-reloadable template engine
pub struct HotReloadableTemplates {
    tera: RwLock<Tera>,
    template_dir: String,
    hot_reload: bool,
}

impl HotReloadableTemplates {
    pub fn new(template_dir: &str, hot_reload: bool) -> Result<Self, tera::Error> {
        // Set up template pattern
        let template_pattern = format!("{}/**/*.html", template_dir);
        
        // Initialize Tera
        let mut tera = match Tera::new(&template_pattern) {
            Ok(t) => t,
            Err(e) => {
                eprintln!("Template parsing error(s): {}", e);
                // Create empty Tera instance
                Tera::default()
            }
        };
        
        // Configure auto-escaping and other options
        tera.autoescape_on(vec![".html", ".htm"]);
        
        Ok(Self {
            tera: RwLock::new(tera),
            template_dir: template_dir.to_string(),
            hot_reload,
        })
    }
    
    // Render a template with context
    pub fn render(&self, template_name: &str, context: &Context) -> Result<String, tera::Error> {
        // If hot reloading is enabled, reload templates first
        if self.hot_reload {
            self.reload_templates()?;
        }
        
        // Render template
        let tera = self.tera.read().unwrap();
        tera.render(template_name, context)
    }
    
    // Reload templates
    pub fn reload_templates(&self) -> Result<(), tera::Error> {
        let template_pattern = format!("{}/**/*.html", self.template_dir);
        
        // Create new Tera instance with fresh templates
        let mut new_tera = match Tera::new(&template_pattern) {
            Ok(t) => t,
            Err(e) => {
                eprintln!("Template reload error(s): {}", e);
                return Err(e);
            }
        };
        
        // Configure same options as original
        new_tera.autoescape_on(vec![".html", ".htm"]);
        
        // Replace the existing Tera instance
        let mut tera = self.tera.write().unwrap();
        *tera = new_tera;
        
        Ok(())
    }
    
    // Force reload a specific template
    pub fn reload_template(&self, template_path: &Path) -> Result<(), tera::Error> {
        if !self.hot_reload {
            return Ok(()); // Do nothing if hot reload is disabled
        }
        
        // Check if path is within template directory
        let template_dir = Path::new(&self.template_dir);
        if !template_path.starts_with(template_dir) {
            return Ok(());
        }
        
        // Get template name from path
        let rel_path = template_path.strip_prefix(template_dir).unwrap();
        let template_name = rel_path.to_string_lossy();
        
        // Read template content
        let content = match std::fs::read_to_string(template_path) {
            Ok(content) => content,
            Err(e) => {
                eprintln!("Failed to read template {}: {}", template_name, e);
                return Err(tera::Error::msg(format!(
                    "Failed to read template {}: {}", template_name, e
                )));
            }
        };
        
        // Update specific template
        let mut tera = self.tera.write().unwrap();
        tera.add_raw_template(&template_name, &content)?;
        
        println!("Reloaded template: {}", template_name);
        Ok(())
    }
}

// Integration with file watcher
pub fn setup_template_hot_reloading(
    templates: Arc<HotReloadableTemplates>,
    hot_reloader: &HotReloader,
    template_dir: &Path,
) -> Receiver<FileChangeEvent> {
    let paths = vec![(template_dir.to_path_buf(), FileType::Template)];
    let rx = hot_reloader.watch(&paths);
    
    // Handle template changes in a separate thread
    std::thread::spawn(move || {
        for event in rx {
            println!("Template changed: {:?}", event.path);
            let _ = templates.reload_template(&event.path);
        }
    });
    
    rx
}
```

## Configuration Hot Reloading

Create a configuration system that dynamically updates when config files change:

```rust
use config::{Config, ConfigError, File, FileFormat};
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use serde::Deserialize;

// Hot-reloadable configuration
pub struct HotConfig<T: for<'de> Deserialize<'de> + Default + Clone> {
    config: RwLock<T>,
    config_path: PathBuf,
}

impl<T: for<'de> Deserialize<'de> + Default + Clone> HotConfig<T> {
    pub fn new<P: AsRef<Path>>(config_path: P) -> Result<Self, ConfigError> {
        let config_path = config_path.as_ref().to_path_buf();
        
        // Load initial configuration
        let config = Self::load_config(&config_path)?;
        
        Ok(Self {
            config: RwLock::new(config),
            config_path,
        })
    }
    
    // Get current configuration (clone)
    pub fn get(&self) -> T {
        self.config.read().unwrap().clone()
    }
    
    // Reload configuration from disk
    pub fn reload(&self) -> Result<(), ConfigError> {
        let new_config = Self::load_config(&self.config_path)?;
        
        // Update configuration with write lock
        let mut config = self.config.write().unwrap();
        *config = new_config;
        
        println!("Configuration reloaded from {:?}", self.config_path);
        Ok(())
    }
    
    // Load configuration from file
    fn load_config(config_path: &Path) -> Result<T, ConfigError> {
        let mut settings = Config::default();
        
        // Determine format based on extension
        let format = match config_path.extension().and_then(|ext| ext.to_str()) {
            Some("json") => FileFormat::Json,
            Some("toml") => FileFormat::Toml,
            Some("yaml") => FileFormat::Yaml,
            Some("yml") => FileFormat::Yaml,
            _ => {
                return Err(ConfigError::Message(format!(
                    "Unsupported config format for {:?}", config_path
                )));
            }
        };
        
        // Load configuration file
        settings.merge(File::from(config_path).format(format))?;
        
        // Deserialize into target type
        let config: T = settings.try_into()?;
        
        Ok(config)
    }
}

// Integration with file watcher
pub fn setup_config_hot_reloading<T: for<'de> Deserialize<'de> + Default + Clone + Send + 'static>(
    hot_config: Arc<HotConfig<T>>,
    hot_reloader: &HotReloader,
    config_dir: &Path,
) -> Receiver<FileChangeEvent> {
    let paths = vec![(config_dir.to_path_buf(), FileType::Config)];
    let rx = hot_reloader.watch(&paths);
    
    // Handle config changes in a separate thread
    std::thread::spawn(move || {
        for event in rx {
            println!("Config file changed: {:?}", event.path);
            if let Err(e) = hot_config.reload() {
                eprintln!("Failed to reload config: {}", e);
            }
        }
    });
    
    rx
}
```

## Dynamic Code Reloading

### Using Dynamic Libraries for Code Hot Reloading

This approach uses dynamic libraries (DLLs/SOs) to reload code without restarting:

```rust
use libloading::{Library, Symbol};
use std::path::Path;
use std::sync::RwLock;

// Type for plugin function
type PluginFunction = unsafe fn() -> i32;

// Hot-reloadable plugin
pub struct HotPlugin {
    lib_path: String,
    library: RwLock<Option<Library>>,
}

impl HotPlugin {
    pub fn new(lib_path: &str) -> Self {
        Self {
            lib_path: lib_path.to_string(),
            library: RwLock::new(None),
        }
    }
    
    // Load or reload the plugin library
    pub fn reload(&self) -> Result<(), libloading::Error> {
        // Get write lock to update library
        let mut library = self.library.write().unwrap();
        
        // Drop existing library if loaded
        *library = None;
        
        // Load new library
        let lib = unsafe { Library::new(&self.lib_path)? };
        *library = Some(lib);
        
        println!("Plugin reloaded: {}", self.lib_path);
        Ok(())
    }
    
    // Call a function from the plugin
    pub fn call_function(&self, function_name: &str) -> Result<i32, Box<dyn std::error::Error>> {
        // Get read lock on library
        let library = self.library.read().unwrap();
        
        let lib = match &*library {
            Some(lib) => lib,
            None => return Err("Plugin not loaded".into()),
        };
        
        // Get function symbol
        unsafe {
            let func: Symbol<PluginFunction> = lib.get(function_name.as_bytes())?;
            Ok(func())
        }
    }
}

// Example plugin library (compile as a dynamic library):
/*
#[no_mangle]
pub extern "C" fn hello() -> i32 {
    println!("Hello from plugin!");
    42
}
*/

// Integration with file watcher
pub fn setup_plugin_hot_reloading(
    plugin: Arc<HotPlugin>,
    hot_reloader: &HotReloader,
    lib_dir: &Path,
) -> Receiver<FileChangeEvent> {
    let paths = vec![(lib_dir.to_path_buf(), FileType::Source)];
    let rx = hot_reloader.watch(&paths);
    
    // Handle library changes in a separate thread
    std::thread::spawn(move || {
        for event in rx {
            println!("Plugin library changed: {:?}", event.path);
            if let Err(e) = plugin.reload() {
                eprintln!("Failed to reload plugin: {}", e);
            }
        }
    });
    
    rx
}
```

### Child Process Approach for Code Hot Reloading

An alternative approach using child processes for code reloading:

```rust
use std::process::{Child, Command};
use std::sync::{Arc, Mutex};

// Hot reloadable server process manager
pub struct ServerProcess {
    process: Mutex<Option<Child>>,
    program: String,
    args: Vec<String>,
}

impl ServerProcess {
    pub fn new(program: &str, args: Vec<String>) -> Self {
        Self {
            process: Mutex::new(None),
            program: program.to_string(),
            args,
        }
    }
    
    // Start the server process
    pub fn start(&self) -> Result<(), std::io::Error> {
        let mut process_guard = self.process.lock().unwrap();
        
        // Kill existing process if running
        if let Some(mut process) = process_guard.take() {
            let _ = process.kill();
            let _ = process.wait();
        }
        
        // Start new process
        let child = Command::new(&self.program)
            .args(&self.args)
            .spawn()?;
            
        *process_guard = Some(child);
        
        println!("Server process started: {} {:?}", self.program, self.args);
        Ok(())
    }
    
    // Restart the server process
    pub fn restart(&self) -> Result<(), std::io::Error> {
        self.start()
    }
    
    // Stop the server process
    pub fn stop(&self) -> Result<(), std::io::Error> {
        let mut process_guard = self.process.lock().unwrap();
        
        if let Some(mut process) = process_guard.take() {
            process.kill()?;
            process.wait()?;
            println!("Server process stopped");
        }
        
        Ok(())
    }
}

impl Drop for ServerProcess {
    fn drop(&mut self) {
        let _ = self.stop();
    }
}

// Integration with file watcher
pub fn setup_server_hot_reloading(
    server: Arc<ServerProcess>,
    hot_reloader: &HotReloader,
    src_dir: &Path,
) -> Receiver<FileChangeEvent> {
    let paths = vec![(src_dir.to_path_buf(), FileType::Source)];
    let rx = hot_reloader.watch(&paths);
    
    // Handle source code changes in a separate thread
    std::thread::spawn(move || {
        for event in rx {
            if event.path.extension().map_or(false, |ext| ext == "rs") {
                println!("Source code changed: {:?}", event.path);
                println!("Recompiling and restarting server...");
                
                // Execute build command
                let build_status = Command::new("cargo")
                    .args(&["build"])
                    .status();
                    
                if let Ok(status) = build_status {
                    if status.success() {
                        // Restart server if build successful
                        if let Err(e) = server.restart() {
                            eprintln!("Failed to restart server: {}", e);
                        }
                    } else {
                        eprintln!("Build failed, not restarting server");
                    }
                } else {
                    eprintln!("Failed to execute build command");
                }
            }
        }
    });
    
    rx
}
```

## Development Tools for Hot Reloading

### Using `cargo-watch` for Rust Development

`cargo-watch` is a simple tool that watches your project for changes and triggers cargo commands:

```powershell
# Install cargo-watch
cargo install cargo-watch

# Basic usage - rebuild and run on any change
cargo watch -x run

# More complex example - clear screen, run tests, then run
cargo watch -c -x test -x run

# Watch specific files/directories
cargo watch -w src -w templates -x run
```

### Using `cargo-run-watcher` for Advanced Development

For more advanced needs, you can create a custom development tool:

```rust
use notify::{RecommendedWatcher, RecursiveMode, Watcher};
use std::process::Command;
use std::time::{Duration, Instant};

fn main() {
    // Define directories to watch
    let watch_dirs = vec!["src", "templates", "static"];
    
    // Create watcher
    let (tx, rx) = std::sync::mpsc::channel();
    let mut watcher = RecommendedWatcher::new(tx, notify::Config::default()).unwrap();
    
    // Add paths to watch
    for dir in &watch_dirs {
        watcher.watch(dir.as_ref(), RecursiveMode::Recursive).unwrap();
    }
    
    // Initial server start
    let mut server_process = start_server();
    let mut last_reload = Instant::now();
    
    println!("Watching for changes...");
    
    // Watch for changes
    loop {
        match rx.recv_timeout(Duration::from_millis(100)) {
            Ok(Ok(event)) => {
                // Debounce events (prevent multiple rapid reloads)
                if last_reload.elapsed() < Duration::from_secs(2) {
                    continue;
                }
                
                // Check if this is a relevant file change
                let should_reload = event.paths.iter().any(|path| {
                    let is_source = path.extension().map_or(false, |ext| ext == "rs");
                    let is_template = path.extension().map_or(false, |ext| ext == "html");
                    is_source || is_template
                });
                
                if should_reload {
                    println!("Change detected, restarting server...");
                    
                    // Kill old server
                    let _ = server_process.kill();
                    let _ = server_process.wait();
                    
                    // Rebuild and start new server
                    let build_status = Command::new("cargo")
                        .args(&["build"])
                        .status();
                        
                    if let Ok(status) = build_status {
                        if status.success() {
                            server_process = start_server();
                            println!("Server restarted successfully");
                        } else {
                            println!("Build failed, fix errors and save again");
                        }
                    }
                    
                    last_reload = Instant::now();
                }
            }
            Ok(Err(e)) => eprintln!("Watch error: {:?}", e),
            Err(std::sync::mpsc::RecvTimeoutError::Timeout) => {
                // Timeout is normal, continue polling
            }
            Err(e) => {
                eprintln!("Watch error: {:?}", e);
                break;
            }
        }
    }
}

fn start_server() -> std::process::Child {
    Command::new("cargo")
        .args(&["run", "--", "--dev"])
        .spawn()
        .expect("Failed to start server")
}
```

## WebSocket Live Reload for Frontend Development

For frontend development, you can use WebSockets to notify browsers of changes:

```rust
use warp::{Filter, ws::Message};
use futures::{FutureExt, StreamExt};
use tokio::sync::mpsc;
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use std::time::Instant;

// Client connection
struct Client {
    tx: mpsc::UnboundedSender<Result<Message, warp::Error>>,
    connected_at: Instant,
}

// Clients manager
type Clients = Arc<Mutex<HashMap<String, Client>>>;

// Live reload server
pub struct LiveReloadServer {
    clients: Clients,
    port: u16,
}

impl LiveReloadServer {
    pub fn new(port: u16) -> Self {
        Self {
            clients: Arc::new(Mutex::new(HashMap::new())),
            port,
        }
    }
    
    // Start the server
    pub async fn start(self) {
        let clients = self.clients.clone();
        
        // WebSocket handler
        let ws_route = warp::path("livereload")
            .and(warp::ws())
            .and(with_clients(clients.clone()))
            .map(|ws: warp::ws::Ws, clients| {
                ws.on_upgrade(move |socket| handle_connection(socket, clients))
            });
        
        // Start server
        let server = warp::serve(ws_route)
            .run(([127, 0, 0, 1], self.port));
            
        println!("LiveReload server running on port {}", self.port);
        
        server.await;
    }
    
    // Notify all clients of a change
    pub fn notify_change(&self, path: &str) {
        let clients = self.clients.lock().unwrap();
        
        let message = serde_json::json!({
            "type": "reload",
            "path": path
        }).to_string();
        
        for (client_id, client) in clients.iter() {
            if let Err(_) = client.tx.send(Ok(Message::text(&message))) {
                println!("Failed to send message to client {}", client_id);
            }
        }
        
        println!("Notified {} clients of change to {}", clients.len(), path);
    }
}

// Helper function to share clients with routes
fn with_clients(clients: Clients) -> impl Filter<Extract = (Clients,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || clients.clone())
}

// Handle new WebSocket connection
async fn handle_connection(ws: warp::ws::WebSocket, clients: Clients) {
    // Split the socket
    let (ws_tx, mut ws_rx) = ws.split();
    
    // Use an unbounded channel to handle buffering
    let (tx, rx) = mpsc::unbounded_channel();
    let client_id = uuid::Uuid::new_v4().to_string();
    
    // Add the client
    {
        let mut clients = clients.lock().unwrap();
        clients.insert(client_id.clone(), Client {
            tx: tx.clone(),
            connected_at: Instant::now(),
        });
    }
    
    // Forward messages from channel to WebSocket
    let forward = rx.forward(ws_tx).map(|result| {
        if let Err(e) = result {
            eprintln!("WebSocket send error: {}", e);
        }
    });
    
    // Handle messages from WebSocket
    let receive = ws_rx.for_each(|msg| {
        if let Ok(msg) = msg {
            if msg.is_ping() || msg.is_pong() {
                // Respond to ping with pong
                let _ = tx.send(Ok(Message::pong(vec![])));
            }
        }
        futures::future::ready(())
    });
    
    // Run both tasks concurrently
    tokio::select! {
        _ = forward => {},
        _ = receive => {},
    }
    
    // Remove client on disconnect
    let mut clients = clients.lock().unwrap();
    clients.remove(&client_id);
    println!("Client disconnected: {}", client_id);
}

// JavaScript code to include in browser for live reload:
/*
<script>
  (function() {
    const socket = new WebSocket('ws://localhost:8080/livereload');
    
    socket.onopen = () => {
      console.log('LiveReload connected');
    };
    
    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'reload') {
        console.log('Change detected, reloading page...');
        window.location.reload();
      }
    };
    
    socket.onclose = () => {
      console.log('LiveReload disconnected');
      // Try to reconnect after delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    };
  })();
</script>
*/

// Integration with file watcher
pub fn setup_livereload(
    live_reload: Arc<LiveReloadServer>,
    hot_reloader: &HotReloader,
    static_dir: &Path,
) {
    let paths = vec![(static_dir.to_path_buf(), FileType::Static)];
    let rx = hot_reloader.watch(&paths);
    
    // Handle static file changes
    std::thread::spawn(move || {
        for event in rx {
            let path = event.path.to_string_lossy().to_string();
            println!("Static file changed: {}", path);
            live_reload.notify_change(&path);
        }
    });
}
```

## Combining Everything: Complete Hot Reload Server

Here's how to integrate all the hot reloading features into a single server:

```rust
use std::path::Path;
use std::sync::Arc;
use tokio::sync::mpsc;

// Server configuration
#[derive(Clone, Debug, serde::Deserialize)]
struct ServerConfig {
    static_dir: String,
    template_dir: String,
    config_dir: String,
    dev_mode: bool,
    port: u16,
    livereload_port: u16,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Default configuration
    let config_path = "config/server.toml";
    
    // Create hot config
    let hot_config = Arc::new(HotConfig::<ServerConfig>::new(config_path)?);
    let config = hot_config.get();
    
    // Only enable hot reloading in dev mode
    if !config.dev_mode {
        println!("Running in production mode, hot reloading disabled");
        
        // Start server without hot reloading
        start_server(hot_config, None).await?;
        return Ok(());
    }
    
    println!("Running in development mode with hot reloading");
    
    // Set up hot reloader
    let static_dir = Path::new(&config.static_dir);
    let template_dir = Path::new(&config.template_dir);
    let config_dir = Path::new(&config.config_dir);
    
    let paths = vec![
        (static_dir.to_path_buf(), FileType::Static),
        (template_dir.to_path_buf(), FileType::Template),
        (config_dir.to_path_buf(), FileType::Config),
    ];
    
    let hot_reloader = Arc::new(HotReloader::new(paths)?);
    
    // Set up static asset cache
    let asset_cache = Arc::new(StaticAssetCache::new(static_dir));
    setup_asset_hot_reloading(asset_cache.clone(), &hot_reloader, static_dir);
    
    // Set up template engine
    let templates = Arc::new(HotReloadableTemplates::new(&config.template_dir, true)?);
    setup_template_hot_reloading(templates.clone(), &hot_reloader, template_dir);
    
    // Set up config reloading
    setup_config_hot_reloading(hot_config.clone(), &hot_reloader, config_dir);
    
    // Set up live reload server for browser refreshing
    let live_reload = Arc::new(LiveReloadServer::new(config.livereload_port));
    setup_livereload(live_reload.clone(), &hot_reloader, static_dir);
    
    // Start live reload server
    let lr = live_reload.clone();
    tokio::spawn(async move {
        lr.start().await;
    });
    
    // Create reload notifier channel for the main server
    let (reload_tx, reload_rx) = mpsc::channel(100);
    
    // Start main server with reload channel
    start_server(hot_config, Some(reload_rx)).await?;
    
    Ok(())
}

// Main server function
async fn start_server(
    hot_config: Arc<HotConfig<ServerConfig>>,
    mut reload_rx: Option<mpsc::Receiver<()>>,
) -> Result<(), Box<dyn std::error::Error>> {
    // Get initial config
    let config = hot_config.get();
    
    // Set up server routes
    let routes = create_routes(hot_config.clone());
    
    // Start server
    let addr = ([127, 0, 0, 1], config.port);
    let server = warp::serve(routes);
    
    if let Some(reload_rx) = &mut reload_rx {
        // With reload signal handling
        let (_, server_fut) = warp::serve(routes).bind_with_graceful_shutdown(addr, async move {
            while let Some(_) = reload_rx.recv().await {
                println!("Reload signal received, restarting server...");
                break;
            }
        });
        
        server_fut.await;
    } else {
        // Without reload handling (production mode)
        server.run(addr).await;
    }
    
    Ok(())
}

// Create server routes
fn create_routes(
    hot_config: Arc<HotConfig<ServerConfig>>,
) -> impl warp::Filter<Extract = impl warp::Reply, Error = warp::Rejection> + Clone {
    // Define routes here...
    // ...
    
    warp::any().map(|| "Server running")
}
```

## Best Practices for Hot Reloading

### 1. Development vs. Production

Always separate development and production configurations:

```rust
// In your server code
let dev_mode = std::env::var("RUST_ENV")
    .map(|env| env == "development")
    .unwrap_or(false);

if dev_mode {
    // Enable hot reloading
    // ...
} else {
    // Disable hot reloading for production
    // ...
}
```

### 2. Performance Considerations

Be careful with file watching to avoid excessive CPU usage:

```rust
// 1. Debounce file change events
use std::time::{Duration, Instant};

let mut last_reload = Instant::now();
let debounce_time = Duration::from_millis(500);

// In your file change handler
if last_reload.elapsed() < debounce_time {
    // Ignore changes that happen too quickly
    return;
}
last_reload = Instant::now();

// 2. Only watch necessary files/directories
watcher.watch("./src", RecursiveMode::Recursive)?;
watcher.watch("./templates", RecursiveMode::Recursive)?;
watcher.watch("./static/css", RecursiveMode::Recursive)?; // Only watch CSS
watcher.watch("./static/js", RecursiveMode::Recursive)?;  // Only watch JS

// 3. Ignore certain files (like editor temporary files)
if path.to_string_lossy().contains(".swp") || path.to_string_lossy().ends_with("~") {
    return; // Ignore editor temp files
}
```

### 3. Security Considerations

Disable hot reloading in production environments:

```rust
fn main() {
    // Check environment
    let env = std::env::var("RUST_ENV").unwrap_or_else(|_| "production".to_string());
    
    // Only enable hot reloading in development
    if env == "development" {
        // Enable hot reloading
        // ...
    } else {
        println!("Running in production mode - hot reloading disabled");
        // Start server without hot reloading
        // ...
    }
}
```

### 4. State Preservation Across Reloads

For dynamic code reloading, implement state preservation:

```rust
// Save important state before reload
fn save_state_before_reload(state: &AppState) -> Result<(), std::io::Error> {
    let serialized = serde_json::to_string(state)?;
    std::fs::write("./state.json", serialized)?;
    Ok(())
}

// Restore state after reload
fn restore_state_after_reload() -> Result<AppState, Box<dyn std::error::Error>> {
    let data = std::fs::read_to_string("./state.json")?;
    let state: AppState = serde_json::from_str(&data)?;
    Ok(state)
}
```

## Integrating with Frontend Frameworks

For full-stack applications, integrate with frontend build systems:

```rust
// Watch frontend changes and trigger rebuilds
fn watch_frontend_changes(
    frontend_dir: &Path, 
    build_script: &str
) -> Result<(), Box<dyn std::error::Error>> {
    let (tx, rx) = std::sync::mpsc::channel();
    let mut watcher = notify::recommended_watcher(tx)?;
    
    // Watch frontend source files
    watcher.watch(frontend_dir, RecursiveMode::Recursive)?;
    
    std::thread::spawn(move || {
        for res in rx {
            match res {
                Ok(event) => {
                    // Check if it's a source file change
                    let is_source = event.paths.iter().any(|p| {
                        let ext = p.extension().and_then(|e| e.to_str());
                        matches!(ext, Some("js") | Some("jsx") | Some("ts") | Some("tsx") | Some("css") | Some("scss"))
                    });
                    
                    if is_source {
                        println!("Frontend change detected, rebuilding...");
                        
                        // Run build script
                        let output = Command::new("sh")
                            .arg("-c")
                            .arg(build_script)
                            .output();
                            
                        match output {
                            Ok(output) => {
                                if output.status.success() {
                                    println!("Frontend build successful");
                                } else {
                                    let stderr = String::from_utf8_lossy(&output.stderr);
                                    eprintln!("Frontend build failed: {}", stderr);
                                }
                            }
                            Err(e) => {
                                eprintln!("Failed to run build script: {}", e);
                            }
                        }
                    }
                }
                Err(e) => eprintln!("Watch error: {:?}", e),
            }
        }
    });
    
    Ok(())
}
```

## Knowledge Check

1. What is hot reloading and how does it differ from live reloading?
   - Hot reloading updates specific parts of an application without losing state
   - Live reloading refreshes the entire application, losing state

2. What are the main components needed for a hot reloading system?
   - File watcher to detect changes
   - Reload mechanism for the affected components
   - Communication channel to notify relevant parts of the system

3. Why should hot reloading be disabled in production?
   - Performance overhead from file watching
   - Security risks from dynamic code loading
   - Unnecessary complexity in stable environments

4. What types of resources benefit most from hot reloading?
   - Static assets (CSS, JavaScript, images)
   - Templates and view components
   - Configuration files
   - Development server code

5. What is the main challenge when implementing code hot reloading in Rust?
   - Rust's static type system makes dynamic code loading complex
   - Managing state across reloads
   - Ensuring thread safety during reloads
   - Maintaining consistent behavior between development and production

## Additional Resources

- [notify Crate Documentation](https://docs.rs/notify/)
- [libloading Crate Documentation](https://docs.rs/libloading/)
- [cargo-watch Repository](https://github.com/watchexec/cargo-watch)
- [Hot Module Replacement in Web Development](https://webpack.js.org/concepts/hot-module-replacement/)
- [Dynamic Loading in Rust Blog Post](https://fasterthanli.me/articles/so-you-want-to-live-reload-rust)
