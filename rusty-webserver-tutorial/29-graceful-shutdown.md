# Graceful Shutdown and Signal Handling

## Learning Objectives
- Understand the importance of graceful shutdown in web services
- Implement signal handling in Rust for various termination scenarios
- Develop strategies for managing in-flight requests during shutdown
- Create proper resource cleanup procedures for database connections and file handles
- Design a shutdown coordination system for complex application architectures

## Prerequisites
- Understanding of async Rust programming
- Knowledge of HTTP connection handling
- Familiarity with system signals and process management

## Introduction

Graceful shutdown is a critical aspect of web service reliability. When a server stops, it should complete in-flight requests, reject new ones, and properly release all resources. This module covers implementing robust shutdown handling in a Rust webserver, from basic signal handling to complex coordination of subsystems during termination.

## Graceful Shutdown Fundamentals

### Key Concepts

- **Graceful Shutdown**: Controlled termination that preserves application state and client experience
- **Signal Handling**: Capturing and responding to OS-level termination signals
- **Resource Cleanup**: Properly closing connections, files, and other resources before termination
- **Connection Draining**: Completing in-flight requests while refusing new ones
- **Shutdown Coordination**: Managing the shutdown sequence across multiple application components

### Common Termination Signals

| Signal | Description | Unix Value | Behavior |
|--------|-------------|------------|----------|
| SIGINT | Terminal interrupt | 2 | Usually sent by Ctrl+C |
| SIGTERM | Termination | 15 | Standard termination signal (e.g., from container orchestrator) |
| SIGHUP | Hangup | 1 | Terminal disconnect, often used for config reload |
| SIGQUIT | Quit | 3 | Terminal quit, usually Ctrl+\ |

## Signal Handling in Rust

### Using Tokio for Signal Handling

Tokio provides utilities for handling OS signals in an async-friendly way:

```rust
use tokio::signal::unix::{signal, SignalKind};
use tokio::signal;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};

// Flag to track shutdown state
pub struct ShutdownSignal {
    is_shutting_down: AtomicBool,
}

impl ShutdownSignal {
    pub fn new() -> Arc<Self> {
        Arc::new(Self {
            is_shutting_down: AtomicBool::new(false),
        })
    }
    
    pub fn is_shutting_down(&self) -> bool {
        self.is_shutting_down.load(Ordering::Acquire)
    }
    
    pub fn initiate_shutdown(&self) {
        self.is_shutting_down.store(true, Ordering::Release);
        println!("Shutdown initiated");
    }
}

// Listen for multiple signals
async fn handle_signals(shutdown: Arc<ShutdownSignal>) {
    let mut sigint = signal(SignalKind::interrupt()).unwrap();
    let mut sigterm = signal(SignalKind::terminate()).unwrap();
    let mut sighup = signal(SignalKind::hangup()).unwrap();
    
    tokio::select! {
        _ = sigint.recv() => {
            println!("Received SIGINT");
            shutdown.initiate_shutdown();
        },
        _ = sigterm.recv() => {
            println!("Received SIGTERM");
            shutdown.initiate_shutdown();
        },
        _ = sighup.recv() => {
            println!("Received SIGHUP");
            shutdown.initiate_shutdown();
        }
    }
}

// For Windows compatibility
async fn handle_ctrl_c(shutdown: Arc<ShutdownSignal>) {
    signal::ctrl_c().await.expect("Failed to listen for ctrl+c event");
    println!("Received Ctrl+C");
    shutdown.initiate_shutdown();
}

// Cross-platform signal handling
#[tokio::main]
async fn main() {
    let shutdown = ShutdownSignal::new();
    
    #[cfg(unix)]
    let signal_task = tokio::spawn(handle_signals(shutdown.clone()));
    
    #[cfg(not(unix))]
    let signal_task = tokio::spawn(handle_ctrl_c(shutdown.clone()));
    
    // Start server and run until shutdown
    println!("Server started. Press Ctrl+C to stop.");
    
    // Wait for shutdown signal
    signal_task.await.unwrap();
    
    // Perform shutdown procedure
    perform_graceful_shutdown().await;
}

async fn perform_graceful_shutdown() {
    println!("Starting graceful shutdown...");
    
    // Add shutdown logic here
    tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
    
    println!("Shutdown complete.");
}
```

### HTTP Server Graceful Shutdown

For an HTTP server, we need to ensure in-flight requests are completed before shutting down:

```rust
use axum::{
    Router,
    routing::get,
    response::IntoResponse,
    extract::State,
};
use hyper::server::conn::AddrIncoming;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc;
use tokio::time::timeout;

#[derive(Clone)]
struct AppState {
    shutdown_signal: Arc<ShutdownSignal>,
    active_requests: Arc<std::sync::atomic::AtomicUsize>,
}

// Request tracking middleware
async fn track_request<B>(
    State(state): State<AppState>,
    request: axum::http::Request<B>,
    next: axum::middleware::Next<B>,
) -> impl IntoResponse {
    // Increment active request count
    state.active_requests.fetch_add(1, Ordering::SeqCst);
    
    // Check if server is shutting down
    if state.shutdown_signal.is_shutting_down() {
        // Reject new requests with 503 Service Unavailable
        return axum::http::Response::builder()
            .status(axum::http::StatusCode::SERVICE_UNAVAILABLE)
            .header("Connection", "close")
            .body(axum::body::boxed(axum::body::Empty::new()))
            .unwrap();
    }
    
    // Process the request
    let response = next.run(request).await;
    
    // Decrement active request count
    state.active_requests.fetch_sub(1, Ordering::SeqCst);
    
    response
}

// Slow request simulation
async fn slow_handler() -> &'static str {
    tokio::time::sleep(Duration::from_secs(5)).await;
    "Slow response completed"
}

#[tokio::main]
async fn main() {
    // Create shutdown signal
    let shutdown_signal = ShutdownSignal::new();
    
    // Create app state
    let state = AppState {
        shutdown_signal: shutdown_signal.clone(),
        active_requests: Arc::new(std::sync::atomic::AtomicUsize::new(0)),
    };
    
    // Create router
    let app = Router::new()
        .route("/", get(|| async { "Hello, World!" }))
        .route("/slow", get(slow_handler))
        .layer(axum::middleware::from_fn_with_state(
            state.clone(),
            track_request
        ))
        .with_state(state.clone());
    
    // Create server
    let addr = "0.0.0.0:3000".parse().unwrap();
    let server = axum::Server::bind(&addr)
        .serve(app.into_make_service());
    
    // Create shutdown signal channel
    let (tx, mut rx) = mpsc::channel(1);
    
    // Spawn signal handler
    let tx_clone = tx.clone();
    tokio::spawn(async move {
        // Wait for OS signals
        if cfg!(unix) {
            let mut sigint = signal(SignalKind::interrupt()).unwrap();
            let mut sigterm = signal(SignalKind::terminate()).unwrap();
            
            tokio::select! {
                _ = sigint.recv() => println!("Received SIGINT"),
                _ = sigterm.recv() => println!("Received SIGTERM"),
            }
        } else {
            signal::ctrl_c().await.expect("Failed to listen for ctrl+c");
            println!("Received Ctrl+C");
        }
        
        // Notify main task about shutdown
        let _ = tx_clone.send(()).await;
    });
    
    // Create server with graceful shutdown
    println!("Server started at http://{}", addr);
    
    // Wait for shutdown signal
    tokio::select! {
        _ = server.with_graceful_shutdown(async {
            rx.recv().await;
            println!("Shutdown signal received, starting graceful shutdown");
        }) => {},
        _ = tx.closed() => {
            println!("Shutdown channel closed unexpectedly");
        }
    }
    
    // Shutdown initiated - wait for in-flight requests to complete
    println!("Waiting for {} in-flight requests to complete...", 
        state.active_requests.load(Ordering::SeqCst));
    
    // Wait for requests to complete with timeout
    let wait_for_requests = async {
        let mut interval = tokio::time::interval(Duration::from_millis(100));
        loop {
            let count = state.active_requests.load(Ordering::SeqCst);
            if count == 0 {
                break;
            }
            println!("Still waiting for {} requests", count);
            interval.tick().await;
        }
    };
    
    // Set maximum wait time for in-flight requests
    match timeout(Duration::from_secs(10), wait_for_requests).await {
        Ok(_) => println!("All requests completed successfully"),
        Err(_) => println!("Timeout waiting for requests, forcing shutdown"),
    }
    
    // Clean up resources
    clean_up_resources().await;
    
    println!("Server shutdown complete");
}

async fn clean_up_resources() {
    println!("Cleaning up resources...");
    
    // Close database connections
    println!("Closing database connections...");
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Release file handles
    println!("Releasing file handles...");
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Flush logs
    println!("Flushing logs...");
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    println!("Cleanup complete");
}
```

## Coordinating Subsystem Shutdown

In a complex application, multiple subsystems need to be shut down in a specific order:

```rust
use std::time::Duration;
use tokio::sync::{oneshot, Mutex};
use std::sync::Arc;
use std::collections::HashMap;
use futures::future::join_all;

// Subsystem trait for components that need coordinated shutdown
#[async_trait::async_trait]
trait Subsystem: Send + Sync {
    fn name(&self) -> &str;
    async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>>;
}

// Example database subsystem
struct Database {
    pool: Arc<Mutex<Option<String>>>, // Simulating a DB connection pool
}

#[async_trait::async_trait]
impl Subsystem for Database {
    fn name(&self) -> &str {
        "database"
    }
    
    async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        println!("Shutting down database connections...");
        
        // Simulate closing connections
        let mut pool = self.pool.lock().await;
        *pool = None;
        
        tokio::time::sleep(Duration::from_millis(200)).await;
        println!("Database connections closed");
        
        Ok(())
    }
}

// Example cache subsystem
struct CacheSystem {
    client: Arc<Mutex<bool>>, // Simulating a Redis client
}

#[async_trait::async_trait]
impl Subsystem for CacheSystem {
    fn name(&self) -> &str {
        "cache"
    }
    
    async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        println!("Shutting down cache connections...");
        
        // Simulate closing Redis connection
        let mut client = self.client.lock().await;
        *client = false;
        
        tokio::time::sleep(Duration::from_millis(150)).await;
        println!("Cache connections closed");
        
        Ok(())
    }
}

// Shutdown coordinator
struct ShutdownCoordinator {
    subsystems: HashMap<String, Arc<dyn Subsystem>>,
    shutdown_order: Vec<String>,
}

impl ShutdownCoordinator {
    fn new() -> Self {
        Self {
            subsystems: HashMap::new(),
            shutdown_order: Vec::new(),
        }
    }
    
    fn register<T: Subsystem + 'static>(&mut self, subsystem: T) {
        let name = subsystem.name().to_string();
        self.subsystems.insert(name.clone(), Arc::new(subsystem));
        self.shutdown_order.push(name);
    }
    
    fn set_shutdown_order(&mut self, order: Vec<String>) {
        // Validate all subsystems are included
        for name in &order {
            if !self.subsystems.contains_key(name) {
                panic!("Unknown subsystem in shutdown order: {}", name);
            }
        }
        
        // Ensure all subsystems are in the order
        for name in self.subsystems.keys() {
            if !order.contains(name) {
                panic!("Subsystem missing from shutdown order: {}", name);
            }
        }
        
        self.shutdown_order = order;
    }
    
    async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        println!("Starting coordinated shutdown sequence");
        
        // Shutdown in specified order
        for name in &self.shutdown_order {
            if let Some(subsystem) = self.subsystems.get(name) {
                println!("Shutting down {}...", name);
                subsystem.shutdown().await?;
            }
        }
        
        println!("Coordinated shutdown complete");
        Ok(())
    }
    
    async fn parallel_shutdown(&self) -> Vec<Result<(), Box<dyn std::error::Error + Send + Sync>>> {
        println!("Starting parallel shutdown sequence");
        
        // Create futures for all subsystems
        let shutdown_futures = self.subsystems.values()
            .map(|subsystem| {
                let subsystem_clone = subsystem.clone();
                async move {
                    println!("Shutting down {} in parallel...", subsystem_clone.name());
                    subsystem_clone.shutdown().await
                }
            })
            .collect::<Vec<_>>();
        
        // Execute all shutdowns in parallel
        let results = join_all(shutdown_futures).await;
        
        println!("Parallel shutdown complete");
        results
    }
}

// Main application with subsystems
struct Application {
    shutdown_coordinator: Arc<ShutdownCoordinator>,
    shutdown_trigger: Option<oneshot::Sender<()>>,
    shutdown_complete: Option<oneshot::Receiver<()>>,
}

impl Application {
    fn new() -> Self {
        // Create subsystems
        let database = Database {
            pool: Arc::new(Mutex::new(Some("connected".to_string()))),
        };
        
        let cache = CacheSystem {
            client: Arc::new(Mutex::new(true)),
        };
        
        // Create shutdown coordinator
        let mut coordinator = ShutdownCoordinator::new();
        coordinator.register(database);
        coordinator.register(cache);
        
        // Set custom shutdown order (cache first, then database)
        coordinator.set_shutdown_order(vec![
            "cache".to_string(),
            "database".to_string(),
        ]);
        
        // Create shutdown channels
        let (shutdown_trigger, shutdown_receiver) = oneshot::channel();
        let (complete_sender, complete_receiver) = oneshot::channel();
        
        // Clone coordinator for shutdown task
        let coordinator_arc = Arc::new(coordinator);
        let coordinator_clone = coordinator_arc.clone();
        
        // Spawn shutdown task
        tokio::spawn(async move {
            // Wait for shutdown signal
            let _ = shutdown_receiver.await;
            println!("Shutdown signal received by application");
            
            // Execute coordinated shutdown
            if let Err(e) = coordinator_clone.shutdown().await {
                eprintln!("Error during shutdown: {}", e);
            }
            
            // Notify that shutdown is complete
            let _ = complete_sender.send(());
        });
        
        Self {
            shutdown_coordinator: coordinator_arc,
            shutdown_trigger: Some(shutdown_trigger),
            shutdown_complete: Some(complete_receiver),
        }
    }
    
    async fn run(&mut self) {
        println!("Application running...");
        
        // Simulate running the application
        tokio::time::sleep(Duration::from_secs(2)).await;
    }
    
    fn trigger_shutdown(&mut self) {
        if let Some(trigger) = self.shutdown_trigger.take() {
            println!("Application shutdown triggered");
            let _ = trigger.send(());
        }
    }
    
    async fn wait_for_shutdown(&mut self) {
        if let Some(receiver) = self.shutdown_complete.take() {
            let _ = receiver.await;
            println!("Application shutdown completed");
        }
    }
}

#[tokio::main]
async fn main() {
    // Create and run application
    let mut app = Application::new();
    
    // Spawn signal handler
    let (tx, mut rx) = mpsc::channel(1);
    tokio::spawn(async move {
        if cfg!(unix) {
            let mut sigint = signal(SignalKind::interrupt()).unwrap();
            tokio::select! {
                _ = sigint.recv() => println!("Received SIGINT"),
            }
        } else {
            signal::ctrl_c().await.expect("Failed to listen for ctrl+c");
            println!("Received Ctrl+C");
        }
        let _ = tx.send(()).await;
    });
    
    // Run until shutdown signal
    tokio::select! {
        _ = app.run() => {
            println!("Application completed normally");
        }
        _ = rx.recv() => {
            println!("Shutdown signal received from OS");
            app.trigger_shutdown();
            app.wait_for_shutdown().await;
        }
    }
}
```

## Timeout and Forced Shutdown

Sometimes we need to forcibly terminate if graceful shutdown takes too long:

```rust
use std::time::Duration;
use tokio::time::timeout;

async fn shutdown_with_timeout<F, T>(
    shutdown_fn: F, 
    max_wait: Duration
) -> Result<T, Box<dyn std::error::Error + Send + Sync>>
where
    F: std::future::Future<Output = Result<T, Box<dyn std::error::Error + Send + Sync>>> + Send,
{
    match timeout(max_wait, shutdown_fn).await {
        Ok(result) => result,
        Err(_) => {
            eprintln!("Shutdown timed out after {:?}, forcing exit", max_wait);
            Err("Shutdown timeout".into())
        }
    }
}

// Usage example
async fn main() {
    let coordinator = create_shutdown_coordinator();
    
    // Try graceful shutdown with timeout
    let shutdown_result = shutdown_with_timeout(
        coordinator.shutdown(),
        Duration::from_secs(5)
    ).await;
    
    match shutdown_result {
        Ok(_) => println!("Graceful shutdown completed"),
        Err(e) => {
            eprintln!("Shutdown error: {}", e);
            println!("Performing forced shutdown");
            // Perform minimal critical cleanup
            // ...
        }
    }
}
```

## Best Practices

### Shutdown Sequencing

Proper shutdown sequence is critical for data integrity and resource cleanup:

1. **Stop accepting new connections/requests**
2. **Complete in-flight requests** (with timeout)
3. **Close client connections** (databases, cache, messaging)
4. **Flush data** (logs, metrics, state to disk)
5. **Release resources** (file handles, sockets)

### Logging During Shutdown

Maintain logging during shutdown to diagnose issues:

```rust
use log::{info, warn, error};

async fn perform_shutdown(log: &Logger) -> Result<(), Box<dyn std::error::Error>> {
    info!(log, "Starting graceful shutdown");
    
    // Track shutdown progress
    let started_at = std::time::Instant::now();
    
    // Close DB connections
    if let Err(e) = close_database().await {
        error!(log, "Error closing database: {}", e);
        // Continue shutdown despite error
    }
    
    // Flush pending operations
    if let Err(e) = flush_operations().await {
        warn!(log, "Some operations could not be flushed: {}", e);
    }
    
    // Final log message
    info!(
        log, 
        "Shutdown completed in {:?}", 
        started_at.elapsed()
    );
    
    Ok(())
}
```

### Containerized Environment Considerations

In containerized environments, graceful shutdown is especially important:

```rust
// Kubernetes-aware shutdown
async fn kubernetes_aware_shutdown() {
    // SIGTERM - sent by Kubernetes before SIGKILL
    let mut sigterm = signal(SignalKind::terminate()).unwrap();
    
    // The termination grace period
    const GRACE_PERIOD: Duration = Duration::from_secs(30);
    
    tokio::select! {
        _ = sigterm.recv() => {
            println!("Received SIGTERM from Kubernetes");
            
            // Allow slightly less than the K8s grace period
            // to ensure we exit cleanly before forced SIGKILL
            let shutdown_timeout = GRACE_PERIOD - Duration::from_secs(2);
            
            let shutdown_result = timeout(
                shutdown_timeout,
                perform_graceful_shutdown()
            ).await;
            
            match shutdown_result {
                Ok(_) => println!("Clean shutdown complete"),
                Err(_) => println!("Clean shutdown timed out"),
            }
        }
    }
}
```

### Testing Graceful Shutdown

It's important to test shutdown procedures:

```rust
#[tokio::test]
async fn test_graceful_shutdown() {
    // Setup test server
    let app = create_test_application();
    
    // Spawn server in background
    let server_handle = tokio::spawn(async move {
        app.run().await
    });
    
    // Send requests to simulate load
    let client = reqwest::Client::new();
    let request_handles: Vec<_> = (0..10).map(|_| {
        let client = client.clone();
        tokio::spawn(async move {
            // Send a request that takes time to process
            let resp = client.get("http://localhost:3000/slow")
                .send()
                .await?;
            
            assert_eq!(resp.status(), 200);
            Ok::<_, Box<dyn std::error::Error + Send + Sync>>(())
        })
    }).collect();
    
    // Wait a bit for requests to start
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Trigger shutdown
    app.trigger_shutdown();
    
    // Verify all requests complete successfully
    for handle in request_handles {
        let result = handle.await.unwrap();
        assert!(result.is_ok());
    }
    
    // Verify server shut down
    let shutdown_result = timeout(
        Duration::from_secs(1), 
        server_handle
    ).await;
    
    assert!(shutdown_result.is_ok(), "Server did not shut down within timeout");
}
```

## Knowledge Check

1. What is the difference between a hard shutdown and a graceful shutdown?
2. In what order should you typically shut down different components of a web application?
3. How would you handle a timeout during graceful shutdown?
4. What signals should your application listen for in a containerized environment like Kubernetes?
5. How can you implement connection draining to ensure in-flight requests complete during shutdown?
6. What strategies can you use to coordinate the shutdown of multiple related services?

## Additional Resources

- [Tokio Signal Documentation](https://docs.rs/tokio/latest/tokio/signal/index.html)
- [Hyper Graceful Shutdown Guide](https://hyper.rs/guides/server/graceful-shutdown/)
- [Kubernetes Termination of Pods](https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#pod-termination)
- [Axum Graceful Shutdown Example](https://github.com/tokio-rs/axum/blob/main/examples/graceful-shutdown/src/main.rs)

## Diagram: Graceful Shutdown Process

```mermaid
stateDiagram-v2
    [*] --> Running: Start Server
    Running --> ShuttingDown: SIGTERM/SIGINT
    ShuttingDown --> RejectingConnections: Stop Listener
    RejectingConnections --> DrainingConnections: Refuse New Requests
    DrainingConnections --> CleaningUp: Wait for In-flight Requests
    CleaningUp --> ReleasingResources: Close Client Connections
    ReleasingResources --> FlushingLogs: Release File Handles
    FlushingLogs --> Terminated: Complete Flush
    Terminated --> [*]: Exit Process
    
    ShuttingDown --> ForcedTermination: Timeout
    ForcedTermination --> [*]
```
