# Advanced Testing and Fuzzing

Building robust webservers requires comprehensive testing beyond simple unit tests. In this module, we'll explore advanced techniques like property-based testing and fuzzing to find edge cases and security vulnerabilities in your Rust webserver.

## Learning Objectives
- Understand and implement property-based testing for HTTP components
- Set up cargo-fuzz and libFuzzer for finding bugs in your protocol handlers
- Build mutation-based testing strategies for request/response validation
- Integrate advanced testing into your CI/CD pipeline

## Prerequisites
- Completion of basic testing modules
- Understanding of HTTP request/response structure
- Familiarity with Rust testing frameworks

## Property-Based Testing Principles

Property-based testing generates many random inputs to verify that your code's properties hold true, regardless of the specific input values. Instead of writing individual test cases, you define properties that should always be true.

Key concepts:
- **Properties**: Invariants that should be true for all valid inputs
- **Generators**: Functions that produce random but valid test data
- **Shrinking**: Reducing failing test cases to minimal reproducible examples

Popular Rust crates:
- **proptest**: Modern property testing framework
- **quickcheck**: Haskell-inspired property testing

### When to Use Property-Based Testing
1. Testing HTTP request parsers
2. Validating encoding/decoding operations
3. Testing state transitions in your server
4. Ensuring idempotence of operations

## Implementing Property Tests for Webservers

Let's implement a property test for our HTTP request parser:

```rust
use proptest::prelude::*;
use http::{Request, StatusCode};
use bytes::Bytes;

proptest! {
    #[test]
    fn request_parsing_roundtrip(
        method in "GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS",
        path in "/[a-zA-Z0-9_/]*",
        headers in prop::collection::vec(("[a-zA-Z0-9_-]+", "[a-zA-Z0-9_\\s-]+"), 0..10),
        body in ".*"
    ) {
        // Create a request from the generated components
        let mut builder = Request::builder()
            .method(method.as_str())
            .uri(path.as_str());
            
        for (name, value) in &headers {
            builder = builder.header(name, value);
        }
        
        let request = builder.body(Bytes::from(body.clone())).unwrap();
        
        // Convert to raw bytes
        let raw_bytes = our_server::request_to_bytes(&request);
        
        // Parse back from bytes
        let parsed_request = our_server::parse_request(&raw_bytes).unwrap();
        
        // Verify the roundtrip
        prop_assert_eq!(parsed_request.method(), request.method());
        prop_assert_eq!(parsed_request.uri(), request.uri());
        prop_assert_eq!(parsed_request.body(), request.body());
        
        // Verify headers
        for (name, value) in request.headers() {
            prop_assert_eq!(
                parsed_request.headers().get(name),
                Some(value)
            );
        }
    }
}
```

### Testing Middleware Chains

Here's a property test for middleware chains that verifies composition properties:

```rust
#[test]
fn middleware_composition_is_associative(
    // Generate random middleware functions
    middleware_a in middleware_fn_strategy(),
    middleware_b in middleware_fn_strategy(),
    middleware_c in middleware_fn_strategy(),
    request in request_strategy(),
) {
    // Test associativity: (A ∘ B) ∘ C = A ∘ (B ∘ C)
    let chain1 = compose_middleware(
        compose_middleware(middleware_a.clone(), middleware_b.clone()),
        middleware_c.clone()
    );
    
    let chain2 = compose_middleware(
        middleware_a,
        compose_middleware(middleware_b, middleware_c)
    );
    
    // Both chains should produce the same result for any request
    let result1 = chain1(request.clone());
    let result2 = chain2(request);
    
    assert_eq!(result1, result2);
}
```

## Fuzzing in Rust

Fuzzing is a technique for finding security vulnerabilities and bugs by providing random, unexpected, or malformed inputs to your program.

### Fuzzing Tools for Rust
1. **cargo-fuzz**: Integration with libFuzzer
2. **afl.rs**: American Fuzzy Lop (AFL) integration
3. **honggfuzz-rs**: Google's honggfuzz integration

### Setting Up cargo-fuzz

```bash
# Install cargo-fuzz
cargo install cargo-fuzz

# Initialize a fuzz target
cargo fuzz init

# Create a fuzz target for HTTP parsing
cargo fuzz add fuzz_http_parser
```

### Example: Fuzzing an HTTP Request Parser

```rust
// in fuzz_targets/fuzz_http_parser.rs
#![no_main]
use libfuzzer_sys::fuzz_target;
use my_webserver::http::parse_request;

fuzz_target!(|data: &[u8]| {
    // Call the parser with fuzzer-generated input
    // The fuzzer will try to find inputs that cause panics
    let _ = parse_request(data);
});
```

Run the fuzzer:

```bash
cargo fuzz run fuzz_http_parser -- -max_len=4096
```

### Corpus-Based Fuzzing

For more efficient fuzzing, provide a corpus of valid inputs:

```bash
# Create a directory for the corpus
mkdir -p fuzz/corpus/http_parser

# Add some valid HTTP requests
echo -e "GET / HTTP/1.1\r\nHost: example.com\r\n\r\n" > fuzz/corpus/http_parser/simple_get.txt

# Run fuzzer with corpus
cargo fuzz run fuzz_http_parser fuzz/corpus/http_parser
```

## Structured Fuzzing with Arbitrary

The `arbitrary` crate helps create structured fuzz targets:

```rust
use arbitrary::{Arbitrary, Error, Unstructured};

#[derive(Debug)]
struct HttpRequest {
    method: String,
    path: String,
    headers: Vec<(String, String)>,
    body: Vec<u8>,
}

impl Arbitrary<'_> for HttpRequest {
    fn arbitrary(u: &mut Unstructured<'_>) -> Result<Self, Error> {
        // Generate structured but random HTTP request
        let methods = ["GET", "POST", "PUT", "DELETE", "HEAD"];
        let method = methods[u.int_in_range(0..=4)?].to_string();
        
        let path_len = u.int_in_range(1..=100)?;
        let path = format!("/{}", u.arbitrary::<String>()?.chars()
            .filter(|c| c.is_alphanumeric() || *c == '/')
            .take(path_len)
            .collect::<String>());
        
        let header_count = u.int_in_range(0..=10)?;
        let mut headers = Vec::with_capacity(header_count);
        for _ in 0..header_count {
            let name = u.arbitrary::<String>()?
                .chars()
                .filter(|c| c.is_ascii_alphanumeric() || *c == '-')
                .take(u.int_in_range(1..=20)?)
                .collect::<String>();
            
            let value = u.arbitrary::<String>()?
                .chars()
                .filter(|c| c.is_ascii() && !c.is_control())
                .take(u.int_in_range(1..=100)?)
                .collect::<String>();
            
            headers.push((name, value));
        }
        
        let body_len = u.int_in_range(0..=1024)?;
        let body = u.bytes(body_len)?.to_vec();
        
        Ok(HttpRequest { method, path, headers, body })
    }
}

// Fuzz target using structured input
fuzz_target!(|request: HttpRequest| {
    // Convert structured request to raw bytes
    let raw_request = format!(
        "{} {} HTTP/1.1\r\n{}\r\n\r\n{}",
        request.method,
        request.path,
        request.headers.iter()
            .map(|(name, value)| format!("{}: {}\r\n", name, value))
            .collect::<String>(),
        String::from_utf8_lossy(&request.body)
    );
    
    // Test the parser with structured input
    let _ = my_webserver::parse_http_request(raw_request.as_bytes());
});
```

## Best Practices for Advanced Testing

1. **Test boundary conditions**
   - Zero-length requests/responses
   - Maximum-length fields (paths, headers, bodies)
   - Invalid UTF-8 sequences
   
2. **Fuzz all input-handling code**
   - Request parsing
   - URL parsing
   - Header processing
   - JSON/form data parsing
   
3. **Use sanitizers with fuzzing**
   - Address Sanitizer (ASAN)
   - Memory Sanitizer (MSAN)
   - Undefined Behavior Sanitizer (UBSAN)

4. **Automate testing in CI**
   - Run property tests on every PR
   - Schedule long fuzzing runs nightly
   - Archive and track corpus growth

5. **Test resource exhaustion scenarios**
   - Connections at limit
   - Memory usage at high load
   - CPU utilization under concurrency

## Integration with Your Webserver

1. **Create a testing harness library**:

```rust
// tests/harness.rs
pub fn setup_test_server() -> (TestClient, JoinHandle<()>) {
    // Setup isolated test server instance
    let addr = find_available_port();
    let server = spawn_test_server(addr);
    let client = TestClient::new(addr);
    (client, server)
}

pub struct TestClient {
    addr: SocketAddr,
    http_client: Client<HttpConnector>,
}

impl TestClient {
    pub fn new(addr: SocketAddr) -> Self { /* ... */ }
    
    pub async fn get(&self, path: &str) -> Response<Body> { /* ... */ }
    pub async fn post(&self, path: &str, body: impl Into<Body>) -> Response<Body> { /* ... */ }
    // Other HTTP methods...
}
```

2. **Integrate with CI/CD pipelines**:

```yaml
# .github/workflows/test.yml
name: Advanced Testing

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  property-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: dtolnay/rust-toolchain@stable
      - name: Run property tests
        run: cargo test --test property_tests

  fuzzing:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: dtolnay/rust-toolchain@nightly
      - name: Install cargo-fuzz
        run: cargo install cargo-fuzz
      - name: Run fuzzing (limited time)
        run: |
          cargo fuzz build
          cargo fuzz run fuzz_http_parser -- -max_total_time=300
```

## Knowledge Check

1. **What is the main difference between property-based testing and unit testing?**
   - Unit testing tests specific examples, while property-based testing verifies properties hold for a wide range of inputs.

2. **Name three Rust crates for fuzzing.**
   - cargo-fuzz (libFuzzer integration)
   - afl.rs (American Fuzzy Lop)
   - honggfuzz-rs (Google's honggfuzz)

3. **What is "shrinking" in property-based testing?**
   - The process of reducing a failing test case to the minimal input that still triggers the failure.

4. **Why is fuzzing particularly important for HTTP servers?**
   - HTTP servers parse untrusted input from the network, making them vulnerable to malformed requests that could lead to security issues.

5. **How can you integrate fuzzing into a CI/CD pipeline?**
   - Run short fuzzing sessions in CI
   - Schedule longer fuzzing jobs nightly
   - Save and reuse corpus files between runs

## Additional Resources

- [Rust Fuzz Book](https://rust-fuzz.github.io/book/)
- [proptest Documentation](https://altsysrq.github.io/proptest-book/intro.html)
- [Fuzzing Rust Code (Mozilla Hacks)](https://hacks.mozilla.org/2021/04/fuzzing-rust-code/)
- [Guide to Property-Based Testing](https://blog.jle.im/entry/introduction-to-property-based-testing-quickcheck.html)

## Next Steps

In the [next module](39-distributed-systems.md), we'll explore distributed systems and microservices architecture for scalable webservers.

[Previous: Security Hardening](37-security-hardening.md) | [Next: Distributed Systems](39-distributed-systems.md)
