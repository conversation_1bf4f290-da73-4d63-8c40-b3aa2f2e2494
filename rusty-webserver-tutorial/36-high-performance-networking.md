# High-Performance Networking (epoll, IOCP)

## Navigation
- [Previous: Custom Protocols and Extensibility](35-custom-protocols.md)
- [Next: Security Hardening and Threat Modeling](37-security-hardening.md)

Optimize your webserver for maximum throughput using low-level networking APIs.

## Theory
- epoll (Linux) and IOCP (Windows) provide scalable event-driven IO.
- Rust crates: `mio`, `tokio` (under the hood).
- These APIs allow handling thousands of connections with few threads.

## Advanced Networking Concepts

### Event-driven I/O

Traditional blocking I/O uses one thread per connection, which doesn't scale well:

```rust
// Blocking approach (simplified)
for connection in listener.incoming() {
    let stream = connection.unwrap();
    // One thread handles one connection
    thread::spawn(move || {
        handle_connection(stream);
    });
}
```

Event-driven I/O allows a single thread to monitor many connections:

```rust
// Event-driven approach with mio (simplified)
let mut events = Events::with_capacity(1024);
let mut poll = Poll::new().unwrap();

// Register the listener
poll.registry().register(&mut listener, SERVER_TOKEN, Interest::READABLE).unwrap();

// Event loop
loop {
    poll.poll(&mut events, Some(Duration::from_millis(100))).unwrap();
    
    for event in events.iter() {
        match event.token() {
            SERVER_TOKEN => {
                // Accept new connection
                let (mut stream, _) = listener.accept().unwrap();
                let token = next_token();
                poll.registry().register(&mut stream, token, Interest::READABLE).unwrap();
                connections.insert(token, stream);
            },
            token => {
                // Handle existing connection
                if let Some(stream) = connections.get_mut(&token) {
                    if event.is_readable() {
                        // Read from socket
                    }
                    if event.is_writable() {
                        // Write to socket
                    }
                }
            }
        }
    }
}
```

## Example: Using mio for Low-Level IO Events

```rust
use std::collections::HashMap;
use std::io::{self, Read, Write};
use std::time::Duration;
use mio::event::Event;
use mio::{Events, Interest, Poll, Token};
use mio::net::{TcpListener, TcpStream};

// Token for the server socket
const SERVER: Token = Token(0);

// Our token counter
let mut next_token = 1;
fn next_token() -> Token {
    let token = Token(next_token);
    next_token += 1;
    token
}

fn main() -> io::Result<()> {
    // Create a poll instance
    let mut poll = Poll::new()?;
    // Create storage for events
    let mut events = Events::with_capacity(1024);

    // Setup the server socket
    let mut server = TcpListener::bind("127.0.0.1:8080".parse().unwrap())?;
    
    // Register the server
    poll.registry()
        .register(&mut server, SERVER, Interest::READABLE)?;

    // Map of `Token` -> `TcpStream`
    let mut connections = HashMap::new();
    // Map of `Token` -> incoming data for that connection
    let mut buffers = HashMap::new();

    println!("Server started on 127.0.0.1:8080");

    // Start an event loop
    loop {
        // Poll for events
        poll.poll(&mut events, Some(Duration::from_millis(100)))?;

        // Process each event
        for event in events.iter() {
            match event.token() {
                SERVER => {
                    // Accept all pending connections
                    loop {
                        match server.accept() {
                            Ok((mut stream, address)) => {
                                println!("Connection from {}", address);
                                
                                // Get a new token
                                let token = next_token();
                                
                                // Register the new connection
                                poll.registry().register(
                                    &mut stream,
                                    token,
                                    Interest::READABLE.add(Interest::WRITABLE),
                                )?;
                                
                                // Store the connection
                                connections.insert(token, stream);
                                buffers.insert(token, Vec::new());
                            }
                            Err(ref e) if e.kind() == io::ErrorKind::WouldBlock => {
                                // Socket is not ready anymore, stop accepting
                                break;
                            }
                            Err(e) => {
                                eprintln!("Accept error: {}", e);
                                break;
                            }
                        }
                    }
                }
                token => {
                    // Process events for client sockets
                    let done = if let Some(stream) = connections.get_mut(&token) {
                        if event.is_readable() {
                            // Read data from socket
                            let mut buffer = [0; 1024];
                            match stream.read(&mut buffer) {
                                Ok(0) => {
                                    // Connection was closed
                                    true
                                }
                                Ok(n) => {
                                    // Got some data
                                    if let Some(buf) = buffers.get_mut(&token) {
                                        buf.extend_from_slice(&buffer[..n]);
                                    }
                                    false
                                }
                                Err(ref e) if e.kind() == io::ErrorKind::WouldBlock => false,
                                Err(e) => {
                                    eprintln!("Read error: {}", e);
                                    true
                                }
                            }
                        } else if event.is_writable() {
                            // If we have data to write, write it
                            if let Some(buf) = buffers.get(&token) {
                                if !buf.is_empty() {
                                    match stream.write(buf) {
                                        Ok(n) if n < buf.len() => {
                                            // Not all data was written
                                            if let Some(buf) = buffers.get_mut(&token) {
                                                buf.drain(0..n);
                                            }
                                            false
                                        }
                                        Ok(_) => {
                                            // All data was written
                                            buffers.insert(token, Vec::new());
                                            false
                                        }
                                        Err(ref e) if e.kind() == io::ErrorKind::WouldBlock => false,
                                        Err(e) => {
                                            eprintln!("Write error: {}", e);
                                            true
                                        }
                                    }
                                } else {
                                    false
                                }
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                    } else {
                        // Got an event for a token with no connection - weird!
                        false
                    };

                    if done {
                        if let Some(mut stream) = connections.remove(&token) {
                            // Deregister the connection
                            poll.registry().deregister(&mut stream)?;
                        }
                        buffers.remove(&token);
                    }
                }
            }
        }
    }
}
```

## Platform-Specific Optimizations

### Linux (epoll)

```rust
#[cfg(target_os = "linux")]
fn optimize_socket(socket: &TcpStream) -> io::Result<()> {
    use socket2::{Socket, Domain, Type};
    use std::os::unix::io::{AsRawFd, FromRawFd};
    
    let fd = socket.as_raw_fd();
    let sock = unsafe { Socket::from_raw_fd(fd) };
    
    // Set TCP_NODELAY option (disable Nagle's algorithm)
    sock.set_nodelay(true)?;
    
    // TCP_QUICKACK - enable quickack mode
    const TCP_QUICKACK: i32 = 12;
    let optval: i32 = 1;
    unsafe {
        libc::setsockopt(
            fd,
            libc::IPPROTO_TCP,
            TCP_QUICKACK,
            &optval as *const _ as *const libc::c_void,
            std::mem::size_of_val(&optval) as libc::socklen_t,
        )
    };
    
    // Don't close the fd when the function exits
    let _ = sock.into_raw_fd();
    Ok(())
}
```

### Windows (IOCP)

```rust
#[cfg(target_os = "windows")]
fn optimize_socket(socket: &TcpStream) -> io::Result<()> {
    use socket2::{Socket, Domain, Type};
    use std::os::windows::io::{AsRawSocket, FromRawSocket};
    
    let handle = socket.as_raw_socket();
    let sock = unsafe { Socket::from_raw_socket(handle) };
    
    // Set TCP_NODELAY
    sock.set_nodelay(true)?;
    
    // Don't close the socket when the function exits
    let _ = sock.into_raw_socket();
    Ok(())
}
```

## Socket Buffer Tuning

```rust
fn tune_socket(socket: &TcpStream) -> io::Result<()> {
    use socket2::{Socket, Domain, Type};
    use std::os::unix::io::{AsRawFd, FromRawFd};
    
    let fd = socket.as_raw_fd();
    let sock = unsafe { Socket::from_raw_fd(fd) };
    
    // Set send buffer
    sock.set_send_buffer_size(256 * 1024)?;
    
    // Set receive buffer
    sock.set_recv_buffer_size(256 * 1024)?;
    
    // Don't close the fd when the function exits
    let _ = sock.into_raw_fd();
    Ok(())
}
```

## Best Practices
- Tune OS socket buffers and thread pools.
- Profile and benchmark regularly.
- Use non-blocking I/O for all network operations.
- Consider TCP_NODELAY for low-latency applications.
- Balance between event loops and thread pools.

## Integration
- Use with async runtimes for best results.
- Combine with custom protocol handlers.
- Implement graceful shutdown for active connections.

## Quiz
1. What is epoll used for?
2. Name a Rust crate for low-level networking.
3. Why is event-driven I/O more scalable than thread-per-connection?
4. What does TCP_NODELAY do?
5. How can you tune socket buffer sizes in Rust?

## Further Reading
- [mio crate documentation](https://docs.rs/mio)
- [Linux epoll manual](https://man7.org/linux/man-pages/man7/epoll.7.html)
- [Windows IOCP documentation](https://docs.microsoft.com/en-us/windows/win32/fileio/i-o-completion-ports)
