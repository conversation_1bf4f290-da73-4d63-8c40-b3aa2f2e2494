# WebSockets and Real-Time Communication

## Navigation
- [Previous: Plugin System](16-plugin-system.md)
- [Next: TLS & HTTPS](18-tls-https.md)

## Table of Contents
- [Introduction](#introduction)
- [Theory](#theory)
- [WebSocket Implementation](#rust-example-using-tokio-tungstenite)
- [Best Practices](#best-practices)
- [Security Considerations](#security-considerations)
- [Integration](#integration)
- [Quiz](#quiz)

## Introduction

WebSockets enable real-time, bidirectional communication between clients and your Rust webserver. This is useful for chat, notifications, live dashboards, and collaborative apps.

## Theory
- WebSockets use a persistent TCP connection, unlike HTTP's request/response model.
- The handshake starts as HTTP, then upgrades to the WebSocket protocol.

## Rust Example (using `tokio-tungstenite`)
```rust
use tokio_tungstenite::tungstenite::Message;
use tokio_tungstenite::accept_async;
// ...existing code for TCP listener...

while let Ok((stream, _)) = listener.accept().await {
    tokio::spawn(async move {
        let ws_stream = accept_async(stream).await.unwrap();
        // Handle messages
    });
}
```

## Best Practices
- Authenticate users before upgrading to WebSocket.
- Handle dropped connections and reconnections.
- Implement heartbeat mechanisms to detect stale connections.
- Consider using compression for high-volume WebSocket traffic.

## Security Considerations

WebSockets introduce distinct security challenges compared to standard HTTP communication due to their persistent nature and bidirectional capabilities. This section outlines essential security measures for your WebSocket implementation.

### WebSocket Threat Model

Understanding the threat landscape is key to securing WebSocket connections:

```mermaid
flowchart TD
    A[Attacker] -->|1. Connection hijacking| WS[WebSocket Connection]
    A -->|2. Unauthorized connection| WS
    A -->|3. Protocol-level attacks| WS
    A -->|4. Message injection| WS
    A -->|5. Resource exhaustion| WS
    A -->|6. Data exfiltration| WS
    
    WS <-->|Persistent bidirectional communication| S[Server]
    WS <-->|Persistent bidirectional communication| C[Client]
    
    class A fill:#f96,stroke:#333
    class WS fill:#69f,stroke:#333
    class S,C fill:#6d9,stroke:#333
```

### Secure WebSocket Handshake

The initial handshake is critical for establishing a secure WebSocket connection:

```rust
/// Validate WebSocket handshake request
fn validate_websocket_upgrade(request: &Request) -> Result<()> {
    // Verify the required headers
    if !request.headers.contains_key("upgrade") || 
       !request.headers.contains_key("connection") ||
       !request.headers.contains_key("sec-websocket-key") ||
       !request.headers.contains_key("sec-websocket-version") {
        return Err(ServerError::WebSocket("Missing required WebSocket headers".to_string()));
    }
    
    // Verify header values
    let upgrade = request.headers.get("upgrade").unwrap().to_lowercase();
    let connection = request.headers.get("connection").unwrap().to_lowercase();
    let version = request.headers.get("sec-websocket-version").unwrap();
    
    if upgrade != "websocket" {
        return Err(ServerError::WebSocket("Invalid upgrade header".to_string()));
    }
    
    if !connection.contains("upgrade") {
        return Err(ServerError::WebSocket("Invalid connection header".to_string()));
    }
    
    if version != "13" {
        return Err(ServerError::WebSocket(format!("Unsupported WebSocket version: {}", version)));
    }
    
    // Verify Sec-WebSocket-Key format (16 bytes Base64-encoded)
    let key = request.headers.get("sec-websocket-key").unwrap();
    if BASE64_STANDARD.decode(key).map(|v| v.len() != 16).unwrap_or(true) {
        return Err(ServerError::WebSocket("Invalid Sec-WebSocket-Key format".to_string()));
    }
    
    // Validate Origin header if present (helps prevent CSRF)
    if let Some(origin) = request.headers.get("origin") {
        if !is_allowed_origin(origin) {
            return Err(ServerError::WebSocket(format!("Origin not allowed: {}", origin)));
        }
    }
    
    Ok(())
}

/// Check if an origin is allowed to establish WebSocket connections
fn is_allowed_origin(origin: &str) -> bool {
    // List of allowed origins
    const ALLOWED_ORIGINS: &[&str] = &[
        "https://example.com",
        "https://app.example.com",
        "https://localhost:8080",
    ];
    
    // Check if the origin is in the allowed list
    ALLOWED_ORIGINS.iter().any(|allowed| *allowed == origin)
}
```

### Authentication and Authorization

Implement robust authentication before establishing WebSocket connections:

```rust
/// Authenticate WebSocket connection
async fn authenticate_websocket(request: &Request) -> Result<User> {
    // Extract authentication token
    let token = match extract_auth_token(request) {
        Some(t) => t,
        None => return Err(ServerError::WebSocket("Authentication required".to_string())),
    };
    
    // Validate the token
    let user = auth_service::verify_token(&token).await?;
    
    // Check if user has permission to use WebSockets
    if !user.has_permission("websocket:connect") {
        return Err(ServerError::WebSocket("Insufficient permissions".to_string()));
    }
    
    log::info!("WebSocket authenticated for user: {}", user.id);
    Ok(user)
}

/// Extract authentication token from various possible sources
fn extract_auth_token(request: &Request) -> Option<String> {
    // Try Authorization header (Bearer token)
    if let Some(auth_header) = request.headers.get("authorization") {
        if auth_header.starts_with("Bearer ") {
            return Some(auth_header[7..].to_string());
        }
    }
    
    // Try auth token from query parameter
    if let Some(query) = request.path.split('?').nth(1) {
        for param in query.split('&') {
            if let Some((key, value)) = param.split_once('=') {
                if key == "token" {
                    return Some(value.to_string());
                }
            }
        }
    }
    
    // Try auth token from cookie
    if let Some(cookie) = request.headers.get("cookie") {
        for item in cookie.split(';') {
            let item = item.trim();
            if let Some((key, value)) = item.split_once('=') {
                if key == "auth_token" {
                    return Some(value.to_string());
                }
            }
        }
    }
    
    None
}
```

### Message Validation and Rate Limiting

Implement message validation and rate limiting to protect against abuse:

```rust
/// WebSocket rate limiting configuration
struct WebSocketRateLimit {
    // Max messages per minute
    max_messages_per_minute: u32,
    // Max message size in bytes
    max_message_size: usize,
    // Max connections per IP
    max_connections_per_ip: u32,
    // Burst allowance
    burst_allowance: u32,
}

/// WebSocket message validator
fn validate_and_rate_limit(
    message: &Message, 
    client_id: &str,
    rate_limiter: &RateLimiter
) -> Result<()> {
    // Check message size
    if message.len() > rate_limiter.config.max_message_size {
        return Err(ServerError::WebSocket(format!(
            "Message too large: {} bytes (max: {})",
            message.len(), rate_limiter.config.max_message_size
        )));
    }
    
    // Apply rate limiting
    if !rate_limiter.allow_message(client_id) {
        return Err(ServerError::WebSocket("Rate limit exceeded".to_string()));
    }
    
    // For text messages, validate content
    if let Message::Text(text) = message {
        // Validate JSON if applicable
        if text.trim().starts_with('{') {
            match serde_json::from_str::<Value>(text) {
                Ok(json) => validate_message_content(&json)?,
                Err(e) => return Err(ServerError::WebSocket(format!("Invalid JSON: {}", e))),
            }
        }
        
        // Check for common injection patterns
        if contains_injection_patterns(text) {
            log::warn!("Potential injection attack in WebSocket message from {}", client_id);
            return Err(ServerError::WebSocket("Invalid message content".to_string()));
        }
    }
    
    Ok(())
}

/// Check for common injection patterns in messages
fn contains_injection_patterns(text: &str) -> bool {
    let lowercase = text.to_lowercase();
    
    // SQL injection patterns
    if lowercase.contains("select ") && lowercase.contains(" from ") ||
       lowercase.contains("union ") && lowercase.contains(" select ") ||
       lowercase.contains("insert into ") ||
       lowercase.contains("delete from ") {
        return true;
    }
    
    // Script injection patterns
    if text.contains("<script") || 
       text.contains("javascript:") ||
       text.contains("eval(") {
        return true;
    }
    
    // Command injection patterns
    if text.contains("$(") || 
       text.contains("`") ||
       text.contains(" | ") && (text.contains("/bin/") || text.contains("cmd.exe")) {
        return true;
    }
    
    false
}
```

### Secure Frame Processing

Implement secure WebSocket frame processing to prevent attacks:

```rust
/// Process WebSocket frames securely
async fn process_frame(frame: Frame, user: &User, context: &mut ConnectionContext) -> Result<Option<Frame>> {
    // Track frame metrics for abuse detection
    context.update_metrics(frame.payload().len());
    
    // Check for protocol violations
    if frame.is_control() && !frame.header().rsv1 && !frame.header().rsv2 && !frame.header().rsv3 {
        // RSV bits must be 0 for control frames
        return Err(ServerError::WebSocket("Protocol violation: RSV bits set for control frame".to_string()));
    }
    
    if frame.is_control() && frame.payload().len() > 125 {
        // Control frames cannot have payload > 125 bytes
        return Err(ServerError::WebSocket("Protocol violation: Control frame too large".to_string()));
    }
    
    // Handle specific frame types
    match frame.opcode() {
        OpCode::Close => {
            // Process close frame
            let status = if frame.payload().len() >= 2 {
                let status_code = u16::from_be_bytes([frame.payload()[0], frame.payload()[1]]);
                CloseCode::from(status_code)
            } else {
                CloseCode::Normal
            };
            
            log::info!("WebSocket close received from user {}: {:?}", user.id, status);
            
            // Return close acknowledgment
            return Ok(Some(Frame::close_with(status, "Server acknowledged close")));
        },
        
        OpCode::Ping => {
            // Immediately respond to ping with pong containing same payload
            return Ok(Some(Frame::pong(frame.into_data())));
        },
        
        OpCode::Pong => {
            // Update last pong time
            context.update_last_pong();
            return Ok(None); // No response needed
        },
        
        OpCode::Text | OpCode::Binary => {
            // Handle data frames
            if let Err(e) = validate_message_content(&frame) {
                log::warn!("Invalid message content from user {}: {}", user.id, e);
                return Err(e);
            }
            
            // Process message (actual business logic would go here)
            // ...
            
            return Ok(None); // Response handled by business logic
        },
        
        _ => {
            // Unrecognized opcode
            return Err(ServerError::WebSocket(format!(
                "Unrecognized WebSocket opcode: {:?}", frame.opcode()
            )));
        }
    }
}
```

### Connection Management and Timeouts

Implement proper connection management to prevent resource exhaustion:

```rust
/// WebSocket connection manager
struct WebSocketConnectionManager {
    // Active connections
    connections: RwLock<HashMap<String, ConnectionInfo>>,
    // Maximum allowed concurrent connections
    max_connections: usize,
    // Idle timeout in seconds
    idle_timeout_secs: u64,
    // Per-IP connection limits
    ip_limits: RwLock<HashMap<String, usize>>,
}

impl WebSocketConnectionManager {
    /// Create a new connection
    async fn create_connection(&self, client_id: String, ip: String, user_id: Option<String>) -> Result<()> {
        // Check total connection limit
        {
            let connections = self.connections.read().await;
            if connections.len() >= self.max_connections {
                return Err(ServerError::WebSocket("Maximum connections reached".to_string()));
            }
        }
        
        // Check per-IP connection limit
        {
            let mut ip_limits = self.ip_limits.write().await;
            let count = ip_limits.entry(ip.clone()).or_insert(0);
            if *count >= 10 { // Maximum 10 connections per IP
                return Err(ServerError::WebSocket("Too many connections from this IP".to_string()));
            }
            *count += 1;
        }
        
        // Add the connection
        let mut connections = self.connections.write().await;
        connections.insert(client_id.clone(), ConnectionInfo {
            client_id,
            ip,
            user_id,
            connected_at: Instant::now(),
            last_activity: AtomicInstant::now(),
            message_count: AtomicUsize::new(0),
            bytes_received: AtomicUsize::new(0),
            bytes_sent: AtomicUsize::new(0),
        });
        
        Ok(())
    }
    
    /// Close inactive connections
    async fn clean_inactive_connections(&self) {
        let now = Instant::now();
        let mut to_remove = Vec::new();
        
        // Find inactive connections
        {
            let connections = self.connections.read().await;
            for (id, info) in connections.iter() {
                let last_activity = info.last_activity.get();
                if now.duration_since(last_activity).as_secs() > self.idle_timeout_secs {
                    to_remove.push((id.clone(), info.ip.clone()));
                }
            }
        }
        
        // Remove inactive connections
        if !to_remove.is_empty() {
            let mut connections = self.connections.write().await;
            let mut ip_limits = self.ip_limits.write().await;
            
            for (id, ip) in to_remove {
                connections.remove(&id);
                
                // Update IP connection count
                if let Some(count) = ip_limits.get_mut(&ip) {
                    if *count > 0 {
                        *count -= 1;
                    }
                }
                
                log::info!("Closed inactive WebSocket connection: {}", id);
            }
        }
    }
}
```

### Connection Origin Validation

Enforce strict origin checking to prevent cross-site WebSocket hijacking:

```rust
/// WebSocket origin validator
struct OriginValidator {
    // Allowed origins (exact matches)
    allowed_origins: HashSet<String>,
    // Allowed origin patterns (with wildcards)
    allowed_patterns: Vec<Regex>,
    // Whether to enforce origin validation
    enforce_same_origin: bool,
}

impl OriginValidator {
    /// Validate the origin of a WebSocket connection request
    fn validate_origin(&self, request: &Request) -> Result<()> {
        // Skip validation if not enforcing
        if !self.enforce_same_origin {
            return Ok(());
        }
        
        // Get origin from headers
        let origin = match request.headers.get("origin") {
            Some(origin) => origin,
            None => {
                // Origin is required when enforcing
                return Err(ServerError::WebSocket("Missing Origin header".to_string()));
            }
        };
        
        // Check against exact matches
        if self.allowed_origins.contains(origin) {
            return Ok(());
        }
        
        // Check against patterns
        for pattern in &self.allowed_patterns {
            if pattern.is_match(origin) {
                return Ok(());
            }
        }
        
        // No match found
        log::warn!("Rejected WebSocket connection from disallowed origin: {}", origin);
        Err(ServerError::WebSocket(format!("Origin not allowed: {}", origin)))
    }
}
```

### Secure Data Transmission

Ensure WebSocket data is securely transmitted:

```rust
/// Configure secure WebSocket settings
fn configure_secure_websocket() -> WebSocketConfig {
    WebSocketConfig {
        // Maximum message size in bytes (1MB)
        max_message_size: Some(1024 * 1024),
        // Maximum frame size (64KB)
        max_frame_size: Some(64 * 1024),
        // Accept only masked client frames (required by RFC)
        accept_unmasked_frames: false,
        // Fragment large messages automatically
        fragment_size: Some(16 * 1024),
        // Enable permessage-deflate compression extension
        // This can make WebSocket communication more efficient
        compression: Some(CompressionOptions {
            server_no_context_takeover: false,
            client_no_context_takeover: false,
            server_max_window_bits: 15, // 32KB window
            client_max_window_bits: 15, // 32KB window
            ..Default::default()
        }),
    }
}

/// Ensure WebSocket connections use TLS
fn require_secure_websocket(request: &Request) -> Result<()> {
    // Check if the connection is over TLS
    let is_secure = request.headers
        .get("x-forwarded-proto")
        .map(|proto| proto == "https")
        .unwrap_or(false);
    
    if !is_secure && !cfg!(debug_assertions) {
        // In production, require secure WebSocket connections
        return Err(ServerError::WebSocket(
            "WebSocket connections must use wss:// protocol (TLS)".to_string()
        ));
    }
    
    Ok(())
}
```

### Protection Against Cross-Site WebSocket Hijacking (CSWSH)

Implement specific protections against CSWSH attacks:

```rust
/// CSRF protection for WebSocket connections
struct WebSocketCsrfProtection {
    // Token validator
    token_validator: Arc<CsrfTokenValidator>,
    // Whether to enforce CSRF checks
    enforce: bool,
}

impl WebSocketCsrfProtection {
    /// Validate CSRF token in WebSocket handshake
    fn validate_csrf(&self, request: &Request) -> Result<()> {
        if !self.enforce {
            return Ok(());
        }
        
        // Get CSRF token from request
        let token = self.extract_csrf_token(request)?;
        
        // Validate the token
        if !self.token_validator.validate_token(&token) {
            log::warn!("CSRF validation failed for WebSocket connection");
            return Err(ServerError::WebSocket("Invalid CSRF token".to_string()));
        }
        
        Ok(())
    }
    
    /// Extract CSRF token from various locations
    fn extract_csrf_token(&self, request: &Request) -> Result<String> {
        // Try from header
        if let Some(token) = request.headers.get("x-csrf-token") {
            return Ok(token.clone());
        }
        
        // Try from query parameter
        if let Some(query) = request.path.split('?').nth(1) {
            for param in query.split('&') {
                if let Some((key, value)) = param.split_once('=') {
                    if key == "csrf_token" {
                        return Ok(value.to_string());
                    }
                }
            }
        }
        
        // Try from cookie
        if let Some(cookie) = request.headers.get("cookie") {
            for item in cookie.split(';') {
                let item = item.trim();
                if let Some((key, value)) = item.split_once('=') {
                    if key == "csrf_token" {
                        return Ok(value.to_string());
                    }
                }
            }
        }
        
        Err(ServerError::WebSocket("CSRF token not found".to_string()))
    }
}
```

### WebSocket-Specific Log Monitoring

Implement specialized logging for WebSocket connections to detect security issues:

```rust
/// WebSocket security logger
struct WebSocketSecurityLogger {
    // Connection events log
    connection_log: Arc<RwLock<Vec<ConnectionEvent>>>,
    // Maximum log entries to keep
    max_log_entries: usize,
}

/// Connection event types
enum ConnectionEvent {
    Connect {
        timestamp: DateTime<Utc>,
        client_id: String,
        ip: String,
        user_agent: String,
        origin: String,
    },
    Disconnect {
        timestamp: DateTime<Utc>,
        client_id: String,
        reason: String,
    },
    RateLimitExceeded {
        timestamp: DateTime<Utc>,
        client_id: String,
        message_rate: f64,
    },
    SecurityViolation {
        timestamp: DateTime<Utc>,
        client_id: String,
        violation_type: String,
        details: String,
    },
}

impl WebSocketSecurityLogger {
    /// Log a connection event and trim log if needed
    async fn log_event(&self, event: ConnectionEvent) {
        let mut log = self.connection_log.write().await;
        log.push(event);
        
        // Trim log if too large
        if log.len() > self.max_log_entries {
            let excess = log.len() - self.max_log_entries;
            log.drain(0..excess);
        }
    }
    
    /// Get recent security violations
    async fn get_recent_violations(&self) -> Vec<ConnectionEvent> {
        let log = self.connection_log.read().await;
        log.iter()
            .filter(|event| matches!(event, ConnectionEvent::SecurityViolation { .. }))
            .cloned()
            .collect()
    }
}
```

### Best Practices for WebSocket Security

1. **Always use secure WebSocket connections (WSS)**:
   - Require TLS/SSL for all WebSocket connections in production
   - Configure proper TLS settings (see [TLS & HTTPS module](18-tls-https.md))

2. **Implement strong authentication**:
   - Authenticate users before establishing WebSocket connections
   - Use secure authentication methods (JWT, session tokens)

3. **Validate all messages**:
   - Perform schema validation on incoming messages
   - Sanitize user-generated content to prevent injection attacks

4. **Monitor connections for abuse**:
   - Implement rate limiting for message frequency
   - Track resource usage and close connections that abuse resources

5. **Set appropriate timeouts**:
   - Implement ping/pong heartbeats to detect dead connections
   - Close inactive connections to free server resources

6. **Enforce origin restrictions**:
   - Validate Origin header to prevent cross-site attacks
   - Use strict Same-Origin Policy for WebSocket connections

7. **Protect against denial of service**:
   - Limit total active connections
   - Limit connections per IP address
   - Limit message sizes and frequency

8. **Secure the upgrade handshake**:
   - Validate all required headers
   - Apply proper CSRF protections

## Integration
- Integrate with your plugin system for real-time event plugins.

## Quiz
1. What is the main difference between HTTP and WebSocket communication?
2. Name a Rust crate for WebSocket support.

## Diagram
```mermaid
graph TD
    A[Client] -- HTTP Upgrade --> B[Webserver]
    B -- WebSocket Frame <--> A
```
