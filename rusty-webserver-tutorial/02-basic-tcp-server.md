<!-- filepath: c:\Users\<USER>\Documents\Repos\Playground\renx\rusty-webserver-tutorial\02-basic-tcp-server.md -->
# Creating a Basic TCP Server

## Learning Objectives
- Understand how TCP servers function and their core components
- Implement a basic TCP listener in Rust that accepts client connections
- Build a connection handler that reads requests and sends responses
- Design clean interfaces with proper error handling for server components
- Test TCP server functionality using both automated tests and manual verification

## Prerequisites
- Completion of Module 01: Project Setup and Structure
- Basic understanding of networking concepts (IP addresses, ports, sockets)
- Familiarity with Rust ownership and borrowing rules
- Understanding of error handling with Result types in Rust

## Navigation
- [Previous: Project Setup and Structure](01-project-setup.md)
- [Next: HTTP Protocol](03-http-protocol.md)

## Introduction
In this module, we'll implement a basic TCP server that can accept connections and respond with simple messages. This forms the foundation of our web server. TCP (Transmission Control Protocol) provides reliable, ordered, and error-checked delivery of data between applications, making it the perfect transport layer for HTTP.

## Understanding TCP Servers

Before writing code, let's understand what a TCP server does and the core principles behind it.

### TCP Server Lifecycle

1. **Binding**: The server binds to a specific network address and port
2. **Listening**: It listens for incoming connection attempts
3. **Accepting**: When a client attempts to connect, the server accepts the connection, establishing a socket
4. **Reading**: The server reads data sent by the client through the connection
5. **Processing**: It processes the received data (in our case, interpreting HTTP requests)
6. **Writing**: The server writes a response back to the client
7. **Closing/Keeping Alive**: It either closes the connection or keeps it alive for future requests

### TCP in the Network Stack

```
┌─────────────────┐
│  Application    │  HTTP, FTP, SMTP, etc.
├─────────────────┤
│  Transport      │  TCP (reliable), UDP (unreliable)
├─────────────────┤
│  Internet       │  IP (addressing and routing)
├─────────────────┤
│  Link           │  Ethernet, Wi-Fi, etc.
└─────────────────┘
```

TCP operates at the transport layer, providing:
- **Reliability**: Ensures data delivery through acknowledgment and retransmission
- **Ordering**: Guarantees that data arrives in the same order it was sent
- **Error Detection**: Uses checksums to verify data integrity
- **Flow Control**: Prevents overwhelming receivers with too much data
- **Congestion Control**: Adapts to network conditions

### TCP Connection Establishment (Three-Way Handshake)

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Server
    
    C->>S: SYN (Synchronize)
    S->>C: SYN-ACK (Synchronize-Acknowledge)
    C->>S: ACK (Acknowledge)
    Note over C,S: Connection Established
```

This handshake occurs automatically at the operating system level when we use Rust's `TcpListener` and `TcpStream` types.

## Basic TCP Server Implementation

Let's implement a minimal but functional TCP server that can accept connections and respond with a fixed message. We'll follow best practices for structuring our code and handling errors properly.

### Key Components Overview

Our server implementation will consist of:

1. **Server Struct**: Holds configuration data and provides the interface for starting the server
2. **Connection Handling**: Logic for processing incoming connections
3. **Error Management**: Robust handling of network and I/O errors
4. **Logging**: Basic logging to understand server behavior

### Creating the Server Module

First, let's create the server module. We need to create the `src/server/mod.rs` file to hold our server implementation:

```bash
# Create the server directory and module file
mkdir -p src/server
touch src/server/mod.rs
```

Now let's implement the server in `src/server/mod.rs`. We'll use a struct-based approach with methods to encapsulate server functionality:

```rust
use std::net::{TcpListener, TcpStream};
use std::io::{Read, Write};
use std::io;

/// A basic TCP server that listens for connections
pub struct Server {
    address: String,
}

impl Server {
    /// Create a new server instance that will listen on the given address
    pub fn new(address: impl Into<String>) -> Self {
        Server {
            address: address.into(),
        }
    }

    /// Run the server, listening for and handling incoming connections
    pub fn run(&self) -> io::Result<()> {
        // Create a TCP listener that binds to our address
        let listener = TcpListener::bind(&self.address)?;
        
        println!("Server listening on {}", self.address);
        
        // Accept connections in a loop
        for stream in listener.incoming() {
            match stream {
                Ok(stream) => {
                    if let Err(e) = self.handle_connection(stream) {
                        eprintln!("Error handling connection: {}", e);
                    }
                }
                Err(e) => {
                    eprintln!("Connection error: {}", e);
                }
            }
        }
        
        Ok(())
    }
    
    /// Handle a single client connection
    fn handle_connection(&self, mut stream: TcpStream) -> io::Result<()> {
        // Get peer address for logging
        let peer_addr = stream.peer_addr()?;
        println!("Connection established from: {}", peer_addr);
        
        // Create a buffer to read the request into
        let mut buffer = [0; 1024];
        
        // Read from the stream
        let bytes_read = stream.read(&mut buffer)?;
            
        // Convert the request to a string for logging
        let request = String::from_utf8_lossy(&buffer[0..bytes_read]);
        println!("Received request: {}", request);
        
        // Send a simple response back
        let response = "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: 13\r\n\r\nHello, World!";
        stream.write_all(response.as_bytes())?;
            
        println!("Response sent to {}", peer_addr);
        
        Ok(())
    }
}
```

### Understanding the Code

Let's analyze the key Rust concepts used in our server implementation:

#### Ownership and Borrowing

- **String Ownership**: The `Server` struct owns its `address` field (as a `String`), following Rust's ownership model
- **Immutable References**: The `run` and `handle_connection` methods take `&self` (an immutable reference to the server), as they only need to read data from the server instance
- **Stream Ownership Transfer**: The `TcpStream` is moved into the `handle_connection` method, transferring ownership to ensure proper resource management
- **Buffer Lifetime**: The fixed-size buffer's lifetime is confined to the `handle_connection` method, ensuring it's properly deallocated

```rust
// Ownership diagram for our server implementation
// Server owns address -> String
// TcpListener created in run() and owned by the run method
// TcpStream transferred from run() to handle_connection()
```

#### Error Handling

- **Result Propagation**: We use Rust's `Result<T, E>` type to propagate errors up the call stack
- **Error Operator**: The `?` operator provides concise error handling, automatically unwrapping success values or returning errors to the caller
- **Pattern Matching**: The `match` expression provides explicit handling of the `Result` from `listener.incoming()`, allowing separate code paths for success and error cases
- **Error Logging**: We use `eprintln!` to log errors to standard error, helping with debugging

```rust
// Example of ? operator for error propagation
let listener = TcpListener::bind(&self.address)?; // Returns error to caller if binding fails

// Example of match for explicit error handling
match stream {
    Ok(stream) => { /* handle connection */ },
    Err(e) => { /* log error */ },
}
```

#### Rust Traits

- **Generic Constructors**: The `Into<String>` trait bound on the `new` method's parameter allows flexible input types
- **Type Conversion**: This trait implementation means we can pass a string literal (`&str`), a `String`, or any other type that implements `Into<String>`
- **Read/Write Traits**: We use the `Read` and `Write` traits from `std::io` to interact with the TCP stream
- **Error Trait**: The `io::Result` type leverages Rust's error handling traits

#### Network Programming

- **Socket Binding**: `TcpListener::bind` creates a socket and binds it to the specified address
- **Connection Acceptance**: The `incoming()` iterator yields new connections as they arrive
- **Peer Information**: We use `peer_addr()` to get information about the connected client for logging
- **Buffered I/O**: Fixed-size buffers are used for reading from and writing to the network socket

### TCP Server Design Considerations

When building our TCP server, we made several architectural decisions. Understanding these trade-offs is crucial for building robust network applications.

#### Address Binding Strategy

We chose to store the address as a `String` in our `Server` struct. Let's examine this and other approaches:

| Approach | Implementation | Advantages | Disadvantages | Best For |
|----------|---------------|------------|--------------|----------|
| **String Storage** (our choice) | `address: String` | Flexible input formats, human-readable | Delayed validation | Development, simple applications |
| **SocketAddr Direct** | `address: SocketAddr` | Type-safety, validated early | Less flexible inputs | Production systems |
| **Lazy Binding** | Create listener in `run()` | Flexible lifecycle | Errors discovered late | Applications with dynamic config |
| **Eager Binding** | Create listener in `new()` | Fail-fast on invalid addresses | Less flexible lifecycle | Critical systems |

Our choice of a `String` favors flexibility during development, as we can easily change the address format and delay binding until the server actually runs.

```rust
// Our approach: Flexible string storage with delayed binding
pub struct Server {
    address: String,  // Store as string
}

impl Server {
    pub fn run(&self) -> io::Result<()> {
        let listener = TcpListener::bind(&self.address)?;  // Bind at runtime
        // ...
    }
}
```

#### Buffer Management Strategies

For reading client requests, we chose a fixed 1024-byte buffer. This represents a trade-off between simplicity and capability:

| Strategy | Implementation | Advantages | Disadvantages | Memory Usage | Performance |
|----------|---------------|------------|--------------|-------------|------------|
| **Fixed Buffer** (our choice) | `[0; 1024]` | Simple, predictable | Limited request size | Fixed & bounded | Fast allocation |
| **Dynamic Buffer** | `Vec<u8>` with capacity | Handles any size | Complex resizing logic | Variable & unbounded | Potential reallocation |
| **Streaming Parser** | Process chunks | Memory efficient | Complex state management | Minimal | CPU intensive |
| **Buffer Pool** | Reuse allocations | Efficient | Complex management | Fixed total | Low allocation cost |

Our fixed buffer is simple for learning purposes but has limitations for real-world use. We'll improve this in later tutorials.

#### Connection Handling Model

Our server handles connections sequentially in a single thread. This approach has implications:

| Model | Characteristics | Advantages | Disadvantages | Scalability |
|-------|----------------|------------|--------------|-------------|
| **Single-threaded** (our choice) | One connection at a time | Simple, low resource usage | Blocks on slow clients | Poor (1-10 clients) |
| **Thread per connection** | New thread for each client | Simple mental model | Resource intensive | Limited (100s of clients) |
| **Thread pool** | Fixed number of worker threads | Controlled resource usage | More complex | Better (1000s of clients) |
| **Asynchronous** | Non-blocking I/O | Highly efficient | Complex programming model | Excellent (10,000+ clients) |

We're starting with the simplest approach to focus on core concepts, but we'll implement more scalable models in later modules.

### Setting Up the Main Function

Now, let's update the `src/main.rs` file to use our server. This entrypoint will:

1. Set up the module structure
2. Create a server instance with the appropriate address
3. Handle any errors that occur during server operation
4. Provide useful feedback to the user

```rust
mod server;

use server::Server;

fn main() {
    println!("Starting Rusty Webserver...");
    println!("Press Ctrl+C to stop the server");

    // Create a server that listens on localhost port 8080
    let server = Server::new("127.0.0.1:8080");

    // Run the server, handling any errors that occur
    match server.run() {
        Ok(_) => println!("Server shutdown successfully"),
        Err(e) => eprintln!("Server error: {}", e),
    }
}
```

#### Understanding the Main Function

- **Module Structure**: We declare the modules that make up our application
- **Server Instance**: We create a `Server` instance bound to localhost (127.0.0.1) on port 8080
- **Error Handling**: We use pattern matching to handle the `Result` returned by `server.run()`
- **User Feedback**: We provide clear messages at startup and shutdown

This approach follows Rust's best practices for error handling and provides a clean entrypoint to our application.

### Testing the Basic Server

Testing network services requires special consideration since they involve external resources (sockets) and may run indefinitely. We'll implement a comprehensive testing strategy that includes both unit tests and manual verification.

#### Automated Testing with Rust's Test Framework

Let's create a test module in `src/server/tests.rs`:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use std::io::{Read, Write};
    use std::net::TcpStream;
    use std::thread;
    use std::time::Duration;

    // Helper function to start a server in a separate thread
    fn start_test_server(address: &str) -> thread::JoinHandle<()> {
        let server_address = address.to_string();
        thread::spawn(move || {
            let server = Server::new(server_address);
            let _ = server.run();  // Note: This runs indefinitely in the thread
        })
    }

    #[test]
    fn test_server_responds_to_request() {
        // Start server on a specific test port (different from main server)
        let address = "127.0.0.1:8081";
        let _handle = start_test_server(address);
        
        // Give the server a moment to start and begin listening
        thread::sleep(Duration::from_millis(100));
        
        // Connect to the server as a client
        let mut stream = TcpStream::connect(address)
            .expect("Failed to connect to test server");
        
        // Send a simple HTTP request
        let request = "GET / HTTP/1.1\r\nHost: localhost\r\n\r\n";
        stream.write_all(request.as_bytes())
            .expect("Failed to send request");
        
        // Read the response from the server
        let mut buffer = [0; 1024];
        let bytes_read = stream.read(&mut buffer)
            .expect("Failed to read response");
        let response = String::from_utf8_lossy(&buffer[0..bytes_read]);
        
        // Verify the response matches our expectations
        assert!(response.starts_with("HTTP/1.1 200 OK"), 
               "Response should start with HTTP/1.1 200 OK");
        assert!(response.contains("Hello, World!"), 
               "Response should contain 'Hello, World!'");
        
        // Note: In a production test, we would use a one-shot server 
        // or implement a shutdown mechanism
    }
    
    #[test]
    fn test_server_handles_invalid_request() {
        // Start server on another test port
        let address = "127.0.0.1:8082";
        let _handle = start_test_server(address);
        thread::sleep(Duration::from_millis(100));
        
        // Connect to the server
        let mut stream = TcpStream::connect(address)
            .expect("Failed to connect to test server");
        
        // Send an invalid HTTP request
        let request = "INVALID REQUEST\r\n\r\n";
        stream.write_all(request.as_bytes())
            .expect("Failed to send request");
        
        // Server should still respond with something (not hang)
        let mut buffer = [0; 1024];
        let bytes_read = stream.read(&mut buffer)
            .expect("Failed to read response");
        
        // We just verify we got some response, we don't care about the content yet
        // In later modules, we'll add proper HTTP error handling
        assert!(bytes_read > 0, "Server should send some response");
    }
}
```

Update the `src/server/mod.rs` file to include our test module:

```rust
// At the top of the file, add:
#[cfg(test)]
mod tests;
```

#### Testing Considerations

1. **Parallel Test Execution**: By default, Rust runs tests in parallel. Using different ports for each test prevents conflicts.

2. **Resource Cleanup**: Our current approach leaves test servers running in background threads. In a production server:
   - Implement a shutdown mechanism for clean resource release
   - Use `oneshot` patterns or thread cancellation
   - Consider setup/teardown hooks with Rust's test attributes

3. **Test Isolation**: Each test creates its own server to avoid interference between tests.

#### Manual Testing

To manually verify the server works:

1. Build and run the server:
   ```bash
   cargo run
   ```

2. In a separate terminal, connect using curl:
   ```bash
   curl http://localhost:8080
   ```
   You should receive a "Hello, World!" response.

3. Alternatively, open a web browser and navigate to http://localhost:8080

### Running the Server

Let's build and run our server to see it in action:

```bash
cargo run
```

You should see output similar to:
```
Starting Rusty Webserver...
Press Ctrl+C to stop the server
Server listening on 127.0.0.1:8080
```

#### Testing the Running Server

There are several ways to test our server:

**1. Using curl (Command Line)**
```bash
curl http://localhost:8080
```
Expected output: `Hello, World!`

**2. Using a Web Browser**
Navigate to http://localhost:8080 in your favorite browser. You should see "Hello, World!" displayed.

**3. Using telnet (Low-Level Testing)**
```bash
telnet localhost 8080
```
Then type:
```
GET / HTTP/1.1
Host: localhost

```
(Press Enter twice after "Host: localhost")

You should see a response containing "Hello, World!"

### Limitations and Next Steps

Our current implementation provides a solid foundation but has several limitations to address in future modules:

| Limitation | Description | Impact | Solution (Next Modules) |
|------------|-------------|--------|------------------------|
| **Fixed Response** | Server always returns "Hello, World!" | No dynamic content | HTTP request parsing and routing |
| **HTTP Compliance** | Not fully HTTP compliant | Limited browser compatibility | Proper HTTP request/response handling |
| **Single-Threaded** | Handles one connection at a time | Poor performance under load | Multi-threaded or async handling |
| **Connection Management** | Basic connection handling | No keep-alive or pipelining | HTTP connection management |
| **Configuration** | Hardcoded settings | Inflexible deployment | Configuration system |
| **Error Handling** | Basic error reporting | Limited diagnostics | Comprehensive error handling |
| **Security** | No security measures | Vulnerable to attacks | Security hardening |

In the next module, we'll implement proper HTTP request parsing and response generation to make our server HTTP-compliant.

## Summary

In this module, we've built a foundational TCP server that:
- Listens for incoming connections on a specified address and port
- Accepts client connections and handles them sequentially
- Reads data from clients and sends back responses
- Implements proper error handling throughout the code
- Follows a structured approach with server lifecycle management

This TCP server forms the backbone of our web server. While it currently responds with a fixed HTTP message, this foundation will allow us to build a fully-featured HTTP server in upcoming modules.

## Knowledge Check

1. **What are the three stages of the TCP connection establishment handshake?**
   - A) SYN, FIN, ACK
   - B) SYN, SYN-ACK, ACK
   - C) CONNECT, ACCEPT, ESTABLISH
   - D) REQUEST, RESPONSE, CLOSE

2. **Why does our server implementation use `&self` instead of `&mut self` for the `run` method?**
   - A) It's a mistake, the method should mutate the server
   - B) The server instance doesn't need to be modified during operation
   - C) To allow multiple threads to call run simultaneously
   - D) All methods in Rust default to immutable references

3. **What is the primary limitation of using a fixed-size buffer for reading client requests?**
   - A) It's slower than dynamic allocation
   - B) It can't handle requests larger than the buffer size
   - C) It causes memory fragmentation
   - D) Fixed buffers are incompatible with the TCP protocol

4. **Which approach did we use for handling connections in our basic TCP server?**
   - A) Thread pool with worker threads
   - B) Asynchronous I/O with futures
   - C) Single-threaded sequential processing
   - D) Event-driven callback system

5. **What method does Rust's `TcpListener` provide to accept incoming connections?**
   - A) `accept()`
   - B) `connect()`
   - C) `incoming()`
   - D) `listen()`

<details>
<summary>Click to see answers</summary>

1. B) SYN, SYN-ACK, ACK
2. B) The server instance doesn't need to be modified during operation
3. B) It can't handle requests larger than the buffer size
4. C) Single-threaded sequential processing
5. C) `incoming()`
</details>

## Additional Resources

### Documentation
- [Rust's std::net Documentation](https://doc.rust-lang.org/std/net/index.html) - Official documentation for Rust's networking types
- [TCP/IP Illustrated](https://en.wikipedia.org/wiki/TCP/IP_Illustrated) - Comprehensive reference on the TCP/IP protocol suite
- [RFC 793: TCP Specification](https://datatracker.ietf.org/doc/html/rfc793) - The original TCP specification

### Tutorials and Guides
- [Rust Cookbook: Network Programming](https://rust-lang-nursery.github.io/rust-cookbook/net.html) - Recipes for common networking tasks in Rust
- [Beej's Guide to Network Programming](https://beej.us/guide/bgnet/) - Classic guide to network programming (C-focused but concepts apply)
- [Network Programming with Rust](https://www.packtpub.com/product/network-programming-with-rust/9781788624893) - Book on network programming in Rust

### Tools for Testing
- [curl Documentation](https://curl.se/docs/) - Comprehensive guide to using curl for HTTP testing
- [Wireshark](https://www.wireshark.org/docs/) - Network protocol analyzer for examining TCP traffic

## Next Steps

In the next module, we'll build upon our TCP server to implement proper HTTP request parsing and response generation. We'll explore the structure of HTTP messages, status codes, headers, and content types.

## Navigation
- [Previous: Project Setup and Structure](01-project-setup.md)
- [Next: HTTP Protocol](03-http-protocol.md)
